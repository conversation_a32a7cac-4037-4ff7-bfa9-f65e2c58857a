-- Manufacturing ERP - Fix Raw Material Lot Locations
-- This script updates raw material lots to use correct location IDs

-- First, let's see what we have
SELECT 
  rml.id,
  rml.location as current_location,
  rm.name as material_name,
  rml.qty,
  rml.status
FROM raw_material_lots rml
JOIN raw_materials rm ON rml.raw_material_id = rm.id
WHERE rml.company_id = 'company_9171a574-418c-43ef-aa6c-cda0c636b72b';

-- Show available locations
SELECT 
  id,
  name,
  type,
  location_code
FROM locations 
WHERE company_id = 'company_9171a574-418c-43ef-aa6c-cda0c636b72b';

-- Update raw material lots to use correct location IDs
-- Update lots with 'rm_building_a' to use the Raw Materials Storage location ID
UPDATE raw_material_lots 
SET 
  location = (
    SELECT id 
    FROM locations 
    WHERE company_id = 'company_9171a574-418c-43ef-aa6c-cda0c636b72b' 
    AND type = 'raw_materials'
    LIMIT 1
  ),
  updated_at = NOW()
WHERE 
  company_id = 'company_9171a574-418c-43ef-aa6c-cda0c636b72b'
  AND location = 'rm_building_a';

-- Verify the update
SELECT 
  rml.id,
  rml.location as updated_location,
  l.name as location_name,
  l.type as location_type,
  rm.name as material_name,
  rml.qty,
  rml.status
FROM raw_material_lots rml
JOIN raw_materials rm ON rml.raw_material_id = rm.id
LEFT JOIN locations l ON rml.location = l.id
WHERE rml.company_id = 'company_9171a574-418c-43ef-aa6c-cda0c636b72b';
