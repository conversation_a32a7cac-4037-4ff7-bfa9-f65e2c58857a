/**
 * Manufacturing ERP - Fix Raw Material Lot Locations
 * Node.js script to update raw material lots with correct location IDs
 */

const { drizzle } = require('drizzle-orm/postgres-js')
const postgres = require('postgres')
const { eq, and } = require('drizzle-orm')

// Database connection
const connectionString = process.env.DATABASE_URL || "postgresql://localhost:5432/manufacturing_erp"
const sql = postgres(connectionString)
const db = drizzle(sql)

// Import schema (simplified for this script)
const rawMaterialLots = {
  id: 'id',
  company_id: 'company_id',
  location: 'location',
  updated_at: 'updated_at'
}

const locations = {
  id: 'id',
  company_id: 'company_id',
  name: 'name',
  type: 'type'
}

async function fixRawMaterialLocations() {
  try {
    console.log('🔧 Starting raw material location fix...')

    const companyId = 'company_9171a574-418c-43ef-aa6c-cda0c636b72b'

    // Get the Raw Materials Storage location ID
    const locationResult = await sql`
      SELECT id, name, type
      FROM locations
      WHERE company_id = ${companyId} AND type = 'raw_materials'
      LIMIT 1
    `

    if (locationResult.length === 0) {
      console.error('❌ No raw materials location found!')
      return
    }

    const rawMaterialsLocation = locationResult[0]
    console.log('📍 Found Raw Materials location:', rawMaterialsLocation)

    // Get lots that need updating
    const lotsToUpdate = await sql`
      SELECT id, location, qty
      FROM raw_material_lots
      WHERE company_id = ${companyId} AND location = 'rm_building_a'
    `
    console.log(`🔍 Found ${lotsToUpdate.length} lots to update`)

    if (lotsToUpdate.length === 0) {
      console.log('✅ No lots need updating')
      return
    }

    // Update the lots
    const updateResult = await sql`
      UPDATE raw_material_lots
      SET
        location = ${rawMaterialsLocation.id},
        updated_at = NOW()
      WHERE
        company_id = ${companyId}
        AND location = 'rm_building_a'
    `
    console.log(`✅ Updated ${updateResult.count} raw material lots`)

    // Verify the update
    const verifyResult = await sql`
      SELECT
        rml.id,
        rml.location,
        l.name as location_name,
        rml.qty
      FROM raw_material_lots rml
      LEFT JOIN locations l ON rml.location = l.id
      WHERE rml.company_id = ${companyId}
    `
    console.log('🔍 Verification results:')
    verifyResult.forEach(lot => {
      console.log(`  - Lot ${lot.id}: ${lot.qty} units at "${lot.location_name}" (${lot.location})`)
    })

    console.log('🎉 Raw material location fix completed successfully!')

  } catch (error) {
    console.error('❌ Fix failed:', error)
  } finally {
    await sql.end()
  }
}

// Run the fix
fixRawMaterialLocations()
