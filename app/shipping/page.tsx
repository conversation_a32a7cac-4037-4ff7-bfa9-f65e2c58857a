/**
 * Manufacturing ERP - Shipping Management Page
 * Professional shipping module with complete workflow integration
 */

import { Suspense } from "react"
import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { db } from "@/lib/db"
import { shipments } from "@/lib/schema-postgres"
import { eq, desc, and, or, ilike } from "drizzle-orm"
import { ShippingPageContent } from "@/components/shipping/shipping-page-content"

interface ShippingPageProps {
  searchParams: Promise<{
    search?: string
    status?: string
    customer_id?: string
    page?: string
  }>
}

async function getShipments(companyId: string, searchParams: any) {
  const search = searchParams.search || ""
  const status = searchParams.status || ""
  const customer_id = searchParams.customer_id || ""
  const page = parseInt(searchParams.page || "1")
  const limit = 50
  const offset = (page - 1) * limit

  // Build where conditions
  let whereConditions = [eq(shipments.company_id, companyId)]

  if (search) {
    whereConditions.push(
      or(
        ilike(shipments.shipment_number, `%${search}%`),
        ilike(shipments.tracking_number, `%${search}%`),
        ilike(shipments.notes, `%${search}%`)
      )!
    )
  }

  if (status) {
    whereConditions.push(eq(shipments.status, status))
  }

  if (customer_id) {
    whereConditions.push(eq(shipments.customer_id, customer_id))
  }

  // Get shipments with relationships
  const shipmentsList = await db.query.shipments.findMany({
    where: and(...whereConditions),
    with: {
      customer: {
        columns: {
          id: true,
          name: true,
          contact_name: true,
          contact_email: true,
          contact_phone: true
        }
      },
      salesContract: {
        columns: {
          id: true,
          number: true,
          status: true
        }
      },
      items: {
        with: {
          product: {
            columns: {
              id: true,
              name: true,
              sku: true,
              unit: true
            }
          }
        }
      }
    },
    orderBy: [desc(shipments.created_at)],
    limit,
    offset
  })

  return shipmentsList
}

async function getShippingStats(companyId: string) {
  // Get shipment counts by status
  const allShipments = await db.query.shipments.findMany({
    where: eq(shipments.company_id, companyId),
    columns: {
      status: true
    }
  })

  const stats = {
    total: allShipments.length,
    preparing: allShipments.filter(s => s.status === 'preparing').length,
    ready: allShipments.filter(s => s.status === 'ready').length,
    shipped: allShipments.filter(s => s.status === 'shipped').length,
    in_transit: allShipments.filter(s => s.status === 'in_transit').length,
    delivered: allShipments.filter(s => s.status === 'delivered').length,
    cancelled: allShipments.filter(s => s.status === 'cancelled').length,
    exception: allShipments.filter(s => s.status === 'exception').length
  }

  return stats
}

export default async function ShippingPage({ searchParams }: ShippingPageProps) {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const resolvedSearchParams = await searchParams
  const [shipmentsList, stats] = await Promise.all([
    getShipments(context.companyId, resolvedSearchParams),
    getShippingStats(context.companyId)
  ])

  return (
    <AppShell>
      <ShippingPageContent shipments={shipmentsList} stats={stats} />
    </AppShell>
  )
}
