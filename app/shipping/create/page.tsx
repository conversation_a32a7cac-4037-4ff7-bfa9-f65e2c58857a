/**
 * Manufacturing ERP - Create Shipment Page
 * Professional shipment creation with contract integration
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { db } from "@/lib/db"
import { customers, salesContracts, products } from "@/lib/schema-postgres"
import { eq, and, or } from "drizzle-orm"
import { CreateShipmentPageContent } from "@/components/shipping/create-shipment-page-content"

interface CreateShipmentPageProps {
  searchParams: Promise<{
    method?: string
    contract_id?: string
  }>
}

async function getFormData(companyId: string) {
  // Get customers for selection
  const customersList = await db.query.customers.findMany({
    where: eq(customers.company_id, companyId),
    columns: {
      id: true,
      name: true,
      contact_name: true,
      contact_email: true,
      address: true
    },
    orderBy: [customers.name]
  })

  // Get shippable sales contracts (ERP best practice: approved, active, in_production)
  const contractsList = await db.query.salesContracts.findMany({
    where: and(
      eq(salesContracts.company_id, companyId),
      or(
        eq(salesContracts.status, "approved"),
        eq(salesContracts.status, "active"),
        eq(salesContracts.status, "in_production")
      )
    ),
    with: {
      customer: {
        columns: {
          id: true,
          name: true
        }
      },
      items: {
        with: {
          product: {
            columns: {
              id: true,
              name: true,
              sku: true,
              unit: true
            }
          }
        }
      }
    },
    orderBy: [salesContracts.created_at]
  })

  // Get products for manual item selection
  const productsList = await db.query.products.findMany({
    where: eq(products.company_id, companyId),
    columns: {
      id: true,
      name: true,
      sku: true,
      unit: true,
      image: true,
      // ✅ PRICING FIX: Include pricing fields for auto-population
      price: true,
      base_price: true,
      cost_price: true,
      currency: true
    },
    orderBy: [products.name]
  })

  return {
    customers: customersList,
    contracts: contractsList,
    products: productsList
  }
}

export default async function CreateShipmentPage({ searchParams }: CreateShipmentPageProps) {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const resolvedSearchParams = await searchParams
  const formData = await getFormData(context.companyId)

  // Pre-select contract if specified
  let selectedContract = null
  if (resolvedSearchParams.contract_id) {
    selectedContract = formData.contracts.find(c => c.id === resolvedSearchParams.contract_id)
  }

  return (
    <AppShell>
      <CreateShipmentPageContent
        customers={formData.customers}
        contracts={formData.contracts}
        products={formData.products}
        defaultMethod={resolvedSearchParams.method}
        selectedContract={selectedContract}
      />
    </AppShell>
  )
}
