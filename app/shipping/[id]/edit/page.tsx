/**
 * Manufacturing ERP - Edit Shipment Page
 * Professional shipment editing with validation
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect, notFound } from "next/navigation"
import { db } from "@/lib/db"
import { shipments, customers, products } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { EditShipmentForm } from "@/components/shipping/edit-shipment-form"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Ship, Edit } from "lucide-react"
import Link from "next/link"
import { EditShipmentPageContent } from "@/components/shipping/edit-shipment-page-content"

interface EditShipmentPageProps {
  params: Promise<{ id: string }>
}

async function getShipmentForEdit(shipmentId: string, companyId: string) {
  const shipment = await db.query.shipments.findFirst({
    where: and(
      eq(shipments.id, shipmentId),
      eq(shipments.company_id, companyId)
    ),
    with: {
      customer: {
        columns: {
          id: true,
          name: true,
          contact_name: true,
          contact_email: true,
          contact_phone: true,
          address: true
        }
      },
      salesContract: {
        columns: {
          id: true,
          number: true,
          status: true
        }
      },
      items: {
        with: {
          product: {
            columns: {
              id: true,
              name: true,
              sku: true,
              unit: true,
              image: true
            }
          }
        }
      }
    }
  })

  return shipment
}

async function getFormData(companyId: string) {
  // Get customers for selection
  const customersList = await db.query.customers.findMany({
    where: eq(customers.company_id, companyId),
    columns: {
      id: true,
      name: true,
      contact_name: true,
      contact_email: true,
      address: true
    },
    orderBy: [customers.name]
  })

  // Get products for item selection
  const productsList = await db.query.products.findMany({
    where: eq(products.company_id, companyId),
    columns: {
      id: true,
      name: true,
      sku: true,
      unit: true,
      image: true
    },
    orderBy: [products.name]
  })

  return {
    customers: customersList,
    products: productsList
  }
}

export default async function EditShipmentPage({ params }: EditShipmentPageProps) {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const { id } = await params
  const [shipment, formData] = await Promise.all([
    getShipmentForEdit(id, context.companyId),
    getFormData(context.companyId)
  ])

  if (!shipment) {
    notFound()
  }

  return (
    <AppShell>
      <EditShipmentPageContent
        shipment={shipment}
        customers={formData.customers}
        products={formData.products}
      />
    </AppShell>
  )
}
