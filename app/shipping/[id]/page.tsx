/**
 * Manufacturing ERP - Shipment Detail Page
 * Professional shipment view with tracking and status management
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect, notFound } from "next/navigation"
import { db } from "@/lib/db"
import { shipments } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { ShipmentDetailView } from "@/components/shipping/shipment-detail-view"
import { FinancialImpactCard } from "@/components/operational/financial-impact-card"
import { ShippingDetailHeader } from "./shipping-detail-header"

interface ShipmentDetailPageProps {
  params: Promise<{ id: string }>
}

async function getShipment(shipmentId: string, companyId: string) {
  const shipment = await db.query.shipments.findFirst({
    where: and(
      eq(shipments.id, shipmentId),
      eq(shipments.company_id, companyId)
    ),
    with: {
      customer: {
        columns: {
          id: true,
          name: true,
          contact_name: true,
          contact_email: true,
          contact_phone: true,
          address: true
        }
      },
      salesContract: {
        columns: {
          id: true,
          number: true,
          status: true
        }
      },
      items: {
        with: {
          product: {
            columns: {
              id: true,
              name: true,
              sku: true,
              unit: true,
              image: true
            }
          }
        }
      }
    }
  })

  return shipment
}

export default async function ShipmentDetailPage({ params }: ShipmentDetailPageProps) {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const { id } = await params
  const shipment = await getShipment(id, context.companyId)

  if (!shipment) {
    notFound()
  }



  return (
    <AppShell>
      <div className="space-y-6">
        {/* Header */}
        <ShippingDetailHeader shipment={shipment} />

        {/* ✅ NEW: Financial Impact Panel - Zero Breaking Changes */}
        <FinancialImpactCard
          title="Shipment Financial Impact"
          type="shipment"
          entityId={shipment.id}
          className="mb-6"
          collapsible={true}
        />

        {/* Shipment Details */}
        <ShipmentDetailView shipment={shipment} />
      </div>
    </AppShell>
  )
}
