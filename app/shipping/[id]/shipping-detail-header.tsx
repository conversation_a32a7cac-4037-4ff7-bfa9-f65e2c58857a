"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Edit, Truck, Ship, Plane, Package } from "lucide-react"
import Link from "next/link"
import { useI18n } from "@/components/i18n-provider"

interface ShippingDetailHeaderProps {
  shipment: any
}

export function ShippingDetailHeader({ shipment }: ShippingDetailHeaderProps) {
  const { t } = useI18n()

  const getShippingMethodIcon = (method: string) => {
    switch (method) {
      case 'sea_freight':
        return <Ship className="h-5 w-5 text-blue-600" />
      case 'air_freight':
        return <Plane className="h-5 w-5 text-sky-600" />
      case 'ground':
        return <Truck className="h-5 w-5 text-green-600" />
      default:
        return <Package className="h-5 w-5 text-gray-600" />
    }
  }

  const getShippingMethodLabel = (method: string) => {
    switch (method) {
      case 'sea_freight':
        return 'Sea Freight'
      case 'air_freight':
        return 'Air Freight'
      case 'ground':
        return 'Ground'
      default:
        return method
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { variant: "outline" as const, className: "text-gray-600 border-gray-300" },
      confirmed: { variant: "outline" as const, className: "text-blue-600 border-blue-300" },
      in_transit: { variant: "outline" as const, className: "text-yellow-600 border-yellow-300" },
      delivered: { variant: "outline" as const, className: "text-green-600 border-green-300" },
      cancelled: { variant: "outline" as const, className: "text-red-600 border-red-300" },
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft

    return (
      <Badge variant={config.variant} className={config.className}>
        {status.toUpperCase()}
      </Badge>
    )
  }

  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/shipping">
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t("shipping.back_to_shipping")}
          </Link>
        </Button>

        <div>
          <div className="flex items-center gap-3">
            {getShippingMethodIcon(shipment.shipping_method)}
            <h1 className="text-2xl font-bold tracking-tight">
              {shipment.shipment_number}
            </h1>
            {getStatusBadge(shipment.status)}
          </div>
          <p className="text-muted-foreground">
            {getShippingMethodLabel(shipment.shipping_method)} {t("shipping.shipment_to")} {shipment.customer.name}
          </p>
        </div>
      </div>

      <div className="flex items-center gap-2">
        <Button variant="outline" asChild>
          <Link href={`/shipping/${shipment.id}/edit`}>
            <Edit className="mr-2 h-4 w-4" />
            Edit Shipment
          </Link>
        </Button>
      </div>
    </div>
  )
}
