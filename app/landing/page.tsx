"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { LanguageSwitcher } from "@/components/language-switcher"
import { useI18n } from "@/components/i18n-provider"
import { <PERSON>hinaLogo } from "@/components/fc-china-logo"
import { ThemeToggle } from "@/components/theme-toggle"
import Link from "next/link"
import Image from "next/image"
import {
  Factory,
  Package,
  Ship,
  Users,
  CheckCircle,
  TrendingUp,
  Globe,
  Shield,
  Zap,
  ArrowRight,
  Star,
  Building,
  Truck,
  BarChart3
} from "lucide-react"

export default function LandingPage() {
  const { t } = useI18n()

  return (
    <div className="min-h-screen relative bg-[var(--sidebar)] dark:bg-[var(--sidebar)]">
      {/* Global subtle grid overlay */}
      <div className="absolute inset-0 pointer-events-none bg-[linear-gradient(rgba(15,23,42,0.06)_1px,transparent_1px),linear-gradient(90deg,rgba(15,23,42,0.06)_1px,transparent_1px)] dark:bg-[linear-gradient(rgba(255,255,255,0.06)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.06)_1px,transparent_1px)] bg-[size:32px_32px]" />

      {/* Header */}
      <header className="relative border-b border-slate-200/60 bg-background/80 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <FCChinaLogo size="md" href="/" />
          <div className="flex items-center gap-2">
            <ThemeToggle />
            <LanguageSwitcher />
            <Button asChild className="bg-slate-900 hover:bg-slate-800 text-white">
              <Link href="/api/auth/login">{t("landing.login")}</Link>
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative container mx-auto px-4 py-20">
        {/* Subtle diagonal stripes overlay for hero */}
        <div className="pointer-events-none absolute inset-0 -z-10 rounded-3xl bg-[repeating-linear-gradient(45deg,rgba(15,23,42,0.05)_0,rgba(15,23,42,0.05)_2px,transparent_2px,transparent_12px)] dark:bg-[repeating-linear-gradient(45deg,rgba(255,255,255,0.07)_0,rgba(255,255,255,0.07)_2px,transparent_2px,transparent_12px)]" />

        <div className="relative grid lg:grid-cols-2 gap-12 items-center max-w-7xl mx-auto">
          {/* Left side - Content */}
          <div className="space-y-8 animate-fade-in-up animate-delay-1">

            <div className="space-y-6">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-slate-900 dark:text-white leading-tight">
                {t("landing.hero.title")}
              </h1>
              <p className="text-xl text-slate-600 dark:text-slate-300 leading-relaxed max-w-lg">
                {t("landing.hero.subtitle")}
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" asChild className="bg-slate-900 hover:bg-slate-800 text-white text-lg px-8 py-3">
                <Link href="/api/auth/login?screen_hint=signup">
                  {t("landing.getStarted")} <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild className="text-lg px-8 py-3 border-slate-200 dark:border-slate-700 text-slate-900 dark:text-white hover:bg-slate-50 dark:hover:bg-slate-800/60 focus-visible:ring-2 focus-visible:ring-slate-300 dark:focus-visible:ring-slate-700">
                <Link href="/tutorial">{t("landing.learnMore")}</Link>
              </Button>
            </div>

          </div>

          {/* Right side - Dashboard mockup */}
          <div className="relative animate-fade-in animate-delay-3">
            <div className="bg-white rounded-2xl shadow-2xl border border-slate-200 overflow-hidden">
              <div className="bg-slate-50 px-4 py-3 border-b border-slate-200 flex items-center gap-2">
                <div className="flex gap-1.5">
                  <div className="w-3 h-3 rounded-full bg-slate-300"></div>
                  <div className="w-3 h-3 rounded-full bg-slate-400"></div>
                  <div className="w-3 h-3 rounded-full bg-slate-500"></div>
                </div>
                <div className="flex-1 text-center">
                  <div className="bg-white rounded px-3 py-1 text-xs text-slate-600 inline-block">
                    {t("app.name")}
                  </div>
                </div>
              </div>
              <div className="p-6 space-y-4">
                {/* KPI Row */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-slate-50 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Users className="h-4 w-4 text-slate-600" />
                      <span className="text-sm font-medium text-slate-900">{t("kpi.customers")}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-2xl font-bold text-slate-900">1,247</div>
                      <div className="text-xs text-slate-500">+3.4%</div>
                    </div>
                  </div>
                  <div className="bg-slate-50 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Package className="h-4 w-4 text-slate-600" />
                      <span className="text-sm font-medium text-slate-900">{t("kpi.products")}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-2xl font-bold text-slate-900">856</div>
                      <div className="text-xs text-slate-500">+1.2%</div>
                    </div>
                  </div>
                </div>

                {/* Export Revenue Sparkline */}
                <div className="bg-white rounded-lg border border-slate-200 p-4 col-span-2">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-slate-900">{t("finance.kpis.totalRevenue")}</span>
                    <span className="text-xs text-slate-500">{t("landing.hero.mock.last30days")}</span>
                  </div>
                  <svg viewBox="0 0 200 64" className="w-full h-16">
                    <defs>
                      <linearGradient id="revFill" x1="0" x2="0" y1="0" y2="1">
                        <stop offset="0%" stopColor="rgb(51 65 85 / 0.18)" />
                        <stop offset="100%" stopColor="rgb(51 65 85 / 0.02)" />
                      </linearGradient>
                    </defs>
                    <path d="M0 44 C 20 38, 40 50, 60 36 C 80 30, 100 34, 120 24 C 140 18, 160 26, 180 18 L 200 18 L 200 64 L 0 64 Z" fill="url(#revFill)" />
                    <path d="M0 44 C 20 38, 40 50, 60 36 C 80 30, 100 34, 120 24 C 140 18, 160 26, 180 18" className="stroke-slate-600/70" strokeWidth="2" fill="none" />
                  </svg>
                </div>

                {/* Quality & Shipping mini-cards */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-slate-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm font-medium text-slate-900">{t("quality.metrics.pass_rate")}</span>
                      <span className="text-xs text-slate-500">{t("landing.hero.mock.last30days")}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="relative h-16 w-16 rounded-full bg-[conic-gradient(rgba(15,23,42,0.8)_0_300deg,rgba(15,23,42,0.12)_300deg_360deg)]">
                        <div className="absolute inset-2 rounded-full bg-white" />
                        <div className="absolute inset-0 flex items-center justify-center text-slate-900 text-sm font-semibold">96%</div>
                      </div>
                      <div>
                        <div className="text-sm text-slate-600">{t("landing.features.quality.title")}</div>
                        <div className="text-xs text-slate-500">{t("quality.metrics.total_inspections")}: 128</div>
                      </div>
                    </div>
                  </div>
                  <div className="bg-slate-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm font-medium text-slate-900">{t("landing.hero.mock.onTimeShipments")}</span>
                      <span className="text-xs text-slate-500">{t("landing.hero.mock.last30days")}</span>
                    </div>
                    <div className="h-20 flex items-end gap-1">
                      <div className="w-3 rounded-t bg-slate-400/60 h-8" />
                      <div className="w-3 rounded-t bg-slate-400/60 h-14" />
                      <div className="w-3 rounded-t bg-slate-400/60 h-10" />
                      <div className="w-3 rounded-t bg-slate-400/60 h-16" />
                      <div className="w-3 rounded-t bg-slate-400/60 h-12" />
                      <div className="w-3 rounded-t bg-slate-400/60 h-9" />
                      <div className="w-3 rounded-t bg-slate-400/60 h-[60px]" />
                      <div className="w-3 rounded-t bg-slate-400/60 h-12" />
                    </div>
                  </div>
                </div>

                {/* Links snapshot */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                    <span className="text-sm text-slate-700">{t("nav.item.sales-contracts")}</span>
                    <Badge variant="secondary" className="bg-slate-100 text-slate-700">24</Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                    <span className="text-sm text-slate-700">{t("nav.item.quality-control")}</span>
                    <Badge variant="secondary" className="bg-slate-100 text-slate-700">12</Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                    <span className="text-sm text-slate-700">{t("nav.item.inventory")}</span>
                    <Badge variant="secondary" className="bg-slate-100 text-slate-700">8</Badge>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="relative py-20 bg-[var(--sidebar-accent)] dark:bg-slate-950">
        <div className="absolute inset-0 pointer-events-none bg-[linear-gradient(rgba(15,23,42,0.06)_1px,transparent_1px),linear-gradient(90deg,rgba(15,23,42,0.06)_1px,transparent_1px)] dark:bg-[linear-gradient(rgba(255,255,255,0.06)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.06)_1px,transparent_1px)] bg-[size:32px_32px]" />
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              {t("landing.features.title")}
            </h2>
            <p className="text-xl text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              {t("landing.features.subtitle")}
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            <FeatureCard
              icon={<Users className="h-8 w-8 text-slate-700 dark:text-slate-300" />}
              title={t("landing.features.crm.title")}
              description={t("landing.features.crm.description")}
            />
            <FeatureCard
              icon={<Package className="h-8 w-8 text-slate-700 dark:text-slate-300" />}
              title={t("landing.features.inventory.title")}
              description={t("landing.features.inventory.description")}
            />
            <FeatureCard
              icon={<Factory className="h-8 w-8 text-slate-700 dark:text-slate-300" />}
              title={t("landing.features.production.title")}
              description={t("landing.features.production.description")}
            />
            <FeatureCard
              icon={<CheckCircle className="h-8 w-8 text-slate-700 dark:text-slate-300" />}
              title={t("landing.features.quality.title")}
              description={t("landing.features.quality.description")}
            />
            <FeatureCard
              icon={<Ship className="h-8 w-8 text-slate-700 dark:text-slate-300" />}
              title={t("landing.features.export.title")}
              description={t("landing.features.export.description")}
            />
            <FeatureCard
              icon={<BarChart3 className="h-8 w-8 text-slate-700 dark:text-slate-300" />}
              title={t("landing.features.analytics.title")}
              description={t("landing.features.analytics.description")}
            />
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="relative bg-white dark:bg-slate-950 py-20">
        {/* Subtle grid overlay */}
        <div className="absolute inset-0 pointer-events-none bg-[linear-gradient(rgba(15,23,42,0.06)_1px,transparent_1px),linear-gradient(90deg,rgba(15,23,42,0.06)_1px,transparent_1px)] dark:bg-[linear-gradient(rgba(255,255,255,0.06)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.06)_1px,transparent_1px)] bg-[size:32px_32px]" />
        <div className="relative container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              {t("landing.benefits.title")}
            </h2>
            <p className="text-xl text-slate-600 dark:text-slate-300">
              {t("landing.benefits.subtitle")}
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <BenefitCard
              icon={<Zap className="h-12 w-12 text-slate-700 dark:text-slate-300" />}
              title={t("landing.benefits.speed.title")}
              description={t("landing.benefits.speed.description")}
            />
            <BenefitCard
              icon={<Shield className="h-12 w-12 text-slate-700 dark:text-slate-300" />}
              title={t("landing.benefits.compliance.title")}
              description={t("landing.benefits.compliance.description")}
            />
            <BenefitCard
              icon={<Globe className="h-12 w-12 text-slate-700 dark:text-slate-300" />}
              title={t("landing.benefits.global.title")}
              description={t("landing.benefits.global.description")}
            />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative bg-slate-900 dark:bg-slate-950 py-20">
        <div className="absolute inset-0 pointer-events-none bg-[linear-gradient(rgba(15,23,42,0.06)_1px,transparent_1px),linear-gradient(90deg,rgba(15,23,42,0.06)_1px,transparent_1px)] dark:bg-[linear-gradient(rgba(255,255,255,0.06)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.06)_1px,transparent_1px)] bg-[size:32px_32px]" />
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              {t("landing.cta.title")}
            </h2>
            <p className="text-xl text-slate-300 mb-8">
              {t("landing.cta.subtitle")}
            </p>
            <Button size="lg" asChild className="bg-white hover:bg-slate-100 text-slate-900 text-lg px-8 py-3">
              <Link href="/api/auth/login?screen_hint=signup">
                {t("landing.cta.button")} <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <p className="text-sm text-slate-400 mt-6">
              {t("landing.cta.features")}
            </p>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative border-t border-slate-200 dark:border-slate-800 bg-slate-50 dark:bg-slate-900 py-12">
        <div className="absolute inset-0 pointer-events-none bg-[linear-gradient(rgba(15,23,42,0.06)_1px,transparent_1px),linear-gradient(90deg,rgba(15,23,42,0.06)_1px,transparent_1px)] dark:bg-[linear-gradient(rgba(255,255,255,0.06)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.06)_1px,transparent_1px)] bg-[size:32px_32px]" />
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <FCChinaLogo size="sm" href="/" />
            </div>
            <p className="text-sm text-slate-600 dark:text-slate-400">
              {t("landing.footer.copyright")}
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}

function FeatureCard({ icon, title, description }: {
  icon: React.ReactNode
  title: string
  description: string
}) {
  return (
    <Card className="border border-slate-200 dark:border-slate-800 bg-white dark:bg-slate-900/60 hover:shadow-lg transition-all duration-200 hover:-translate-y-1 animate-fade-in-up">
      <CardHeader className="pb-4">
        <div className="mb-3">{icon}</div>
        <CardTitle className="text-xl font-semibold text-slate-900 dark:text-white">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <CardDescription className="text-base text-slate-600 dark:text-slate-300 leading-relaxed">{description}</CardDescription>
      </CardContent>
    </Card>
  )
}

function BenefitCard({ icon, title, description }: {
  icon: React.ReactNode
  title: string
  description: string
}) {
  return (
    <div className="text-center group animate-fade-in-up">
      <div className="flex justify-center mb-6 group-hover:scale-110 transition-transform duration-200">{icon}</div>
      <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3">{title}</h3>
      <p className="text-slate-600 dark:text-slate-300 leading-relaxed">{description}</p>
    </div>
  )
}
