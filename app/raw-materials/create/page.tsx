/**
 * Manufacturing ERP - Create Raw Material Page
 * Professional form for creating new raw materials with supplier integration
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { db } from "@/lib/db"
import { suppliers } from "@/lib/schema-postgres"
import { eq } from "drizzle-orm"
import { CreateRawMaterialPageContent } from "@/components/raw-materials/create-raw-material-page-content"

export default async function CreateRawMaterialPage() {
  // ✅ REQUIRED: Tenant context validation
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  // ✅ REAL DATA: Fetch suppliers for dropdown
  const suppliersList = await db.query.suppliers.findMany({
    where: eq(suppliers.company_id, context.companyId),
    orderBy: (suppliers, { asc }) => [asc(suppliers.name)],
    limit: 100,
  })

  return (
    <AppShell>
      <CreateRawMaterialPageContent suppliers={suppliersList} />
    </AppShell>
  )
}
