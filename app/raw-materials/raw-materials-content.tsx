"use client"

/**
 * Manufacturing ERP - Raw Materials Content Component
 * Client-side component with localization support
 */

import { AppShell } from "@/components/app-shell"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, Search, Filter, Package2, AlertTriangle, TrendingUp, DollarSign } from "lucide-react"
import Link from "next/link"
import { RawMaterialActions } from "@/components/raw-materials/raw-material-actions"
import { useI18n } from "@/components/i18n-provider"
import { useState, useMemo, useEffect } from "react"

interface RawMaterialsContentProps {
    materials: any[]
    totalMaterials: number
    activeMaterials: number
    discontinuedMaterials: number
    materialsWithStock: number
}

export function RawMaterialsContent({
    materials,
    totalMaterials,
    activeMaterials,
    discontinuedMaterials,
    materialsWithStock
}: RawMaterialsContentProps) {
    const { t } = useI18n()

    // ✅ Filter state
    const [searchTerm, setSearchTerm] = useState("")
    const [categoryFilter, setCategoryFilter] = useState("all")
    const [statusFilter, setStatusFilter] = useState("all")

    // ✅ Location data for proper location name resolution
    const [locations, setLocations] = useState<any[]>([])

    // ✅ Fetch locations on component mount
    useEffect(() => {
        const fetchLocations = async () => {
            try {
                const response = await fetch('/api/locations')
                if (response.ok) {
                    const data = await response.json()
                    setLocations(data.locations || [])
                }
            } catch (error) {
                console.error('Failed to fetch locations:', error)
            }
        }

        fetchLocations()
    }, [])

    const getStatusBadge = (status: string) => {
        switch (status) {
            case "active":
                return <Badge className="bg-green-100 text-green-800">{t('raw_materials.filters.active')}</Badge>
            case "inactive":
                return <Badge variant="secondary">{t('raw_materials.filters.inactive')}</Badge>
            case "discontinued":
                return <Badge variant="destructive">{t('raw_materials.filters.discontinued')}</Badge>
            default:
                return <Badge variant="outline">{status}</Badge>
        }
    }

    const getCategoryBadge = (category: string) => {
        const categoryColors = {
            yarn: "bg-blue-100 text-blue-800",
            fabric: "bg-purple-100 text-purple-800",
            dyes: "bg-red-100 text-red-800",
            chemicals: "bg-yellow-100 text-yellow-800",
            accessories: "bg-green-100 text-green-800",
            other: "bg-gray-100 text-gray-800",
        }

        const getCategoryLabel = (cat: string) => {
            switch (cat) {
                case "yarn": return t('raw_materials.filters.yarn')
                case "fabric": return t('raw_materials.filters.fabric')
                case "dyes": return t('raw_materials.filters.dyes')
                case "chemicals": return t('raw_materials.filters.chemicals')
                case "accessories": return t('raw_materials.filters.accessories')
                case "other": return t('raw_materials.filters.other')
                default: return cat.charAt(0).toUpperCase() + cat.slice(1)
            }
        }

        return (
            <Badge className={categoryColors[category as keyof typeof categoryColors] || categoryColors.other}>
                {getCategoryLabel(category)}
            </Badge>
        )
    }

    const formatCurrency = (amount: string | number) => {
        const num = typeof amount === 'string' ? parseFloat(amount) : amount
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(num || 0)
    }

    // ✅ NEW: Calculate stock status based on available lots with location info
    const getStockStatus = (material: any) => {
        if (!material.lots || material.lots.length === 0) {
            return <Badge variant="outline" className="text-red-600 border-red-200">{t('raw_materials.no_stock')}</Badge>
        }

        // Calculate total available quantity and get location info
        const totalQty = material.lots.reduce((sum: number, lot: any) => {
            return sum + parseFloat(lot.qty || "0")
        }, 0)

        // Get primary location (most common location or first one)
        const locations = material.lots.map((lot: any) => lot.location).filter(Boolean)
        const primaryLocation = locations.length > 0 ? locations[0] : null

        // Format location name for display using real location data
        const getLocationDisplay = (locationId: string) => {
            // First try to find the location in our locations data
            const locationRecord = locations.find(loc => loc.id === locationId)
            if (locationRecord) {
                return locationRecord.name
            }

            // Fallback for legacy location codes
            if (locationId === "raw_materials_storage" || locationId === "rm_building_a") {
                return t('raw_materials.raw_materials_storage')
            }
            if (locationId === "fg_main_warehouse") {
                return t('raw_materials.main_finished_goods_warehouse')
            }

            // Generic fallback
            return locationId.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())
        }

        if (totalQty === 0) {
            return <Badge variant="outline" className="text-red-600 border-red-200">{t('raw_materials.no_stock')}</Badge>
        } else if (totalQty < 100) { // Low stock threshold
            return (
                <div className="flex flex-col gap-1">
                    <Badge variant="outline" className="text-yellow-600 border-yellow-200">
                        {t('raw_materials.low_stock')} ({totalQty} {material.unit})
                    </Badge>
                    {primaryLocation && (
                        <span className="text-xs text-muted-foreground">
                            @ {getLocationDisplay(primaryLocation)}
                        </span>
                    )}
                </div>
            )
        } else {
            return (
                <div className="flex flex-col gap-1">
                    <Badge variant="outline" className="text-green-600 border-green-200">
                        {t('raw_materials.in_stock')} ({totalQty} {material.unit})
                    </Badge>
                    {primaryLocation && (
                        <span className="text-xs text-muted-foreground">
                            @ {getLocationDisplay(primaryLocation)}
                        </span>
                    )}
                </div>
            )
        }
    }

    // ✅ Filter materials based on search and filters
    const filteredMaterials = useMemo(() => {
        return materials.filter(material => {
            // Search filter
            const matchesSearch = searchTerm === "" ||
                material.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                material.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
                (material.description && material.description.toLowerCase().includes(searchTerm.toLowerCase()))

            // Category filter
            const matchesCategory = categoryFilter === "all" || material.category === categoryFilter

            // Status filter
            const matchesStatus = statusFilter === "all" || material.status === statusFilter

            return matchesSearch && matchesCategory && matchesStatus
        })
    }, [materials, searchTerm, categoryFilter, statusFilter])

    return (
        <AppShell>
            <div className="space-y-6">
                {/* Professional Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">{t('raw_materials.title')}</h1>
                        <p className="text-muted-foreground">
                            {t('raw_materials.subtitle')}
                        </p>
                    </div>
                    <Button asChild>
                        <Link href="/raw-materials/create">
                            <Plus className="mr-2 h-4 w-4" />
                            {t('raw_materials.new_material')}
                        </Link>
                    </Button>
                </div>

                {/* Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                        <CardContent className="pt-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">{t('raw_materials.total_materials')}</p>
                                    <p className="text-2xl font-bold">{totalMaterials}</p>
                                </div>
                                <Package2 className="h-8 w-8 text-blue-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="pt-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">{t('raw_materials.active_materials')}</p>
                                    <p className="text-2xl font-bold">{activeMaterials}</p>
                                </div>
                                <TrendingUp className="h-8 w-8 text-green-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="pt-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">{t('raw_materials.with_stock')}</p>
                                    <p className="text-2xl font-bold">{materialsWithStock}</p>
                                </div>
                                <DollarSign className="h-8 w-8 text-purple-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="pt-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">{t('raw_materials.discontinued')}</p>
                                    <p className="text-2xl font-bold">{discontinuedMaterials}</p>
                                </div>
                                <AlertTriangle className="h-8 w-8 text-red-600" />
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters and Search */}
                <div className="flex items-center gap-4">
                    <div className="relative flex-1 max-w-sm">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <Input
                            placeholder={t('raw_materials.search_placeholder')}
                            className="pl-10"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                    </div>
                    <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                        <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder={t('raw_materials.filter_by_category')} />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">{t('raw_materials.filters.all_categories')}</SelectItem>
                            <SelectItem value="yarn">{t('raw_materials.filters.yarn')}</SelectItem>
                            <SelectItem value="fabric">{t('raw_materials.filters.fabric')}</SelectItem>
                            <SelectItem value="dyes">{t('raw_materials.filters.dyes')}</SelectItem>
                            <SelectItem value="chemicals">{t('raw_materials.filters.chemicals')}</SelectItem>
                            <SelectItem value="accessories">{t('raw_materials.filters.accessories')}</SelectItem>
                            <SelectItem value="other">{t('raw_materials.filters.other')}</SelectItem>
                        </SelectContent>
                    </Select>
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                        <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder={t('raw_materials.filter_by_status')} />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">{t('raw_materials.filters.all_status')}</SelectItem>
                            <SelectItem value="active">{t('raw_materials.filters.active')}</SelectItem>
                            <SelectItem value="inactive">{t('raw_materials.filters.inactive')}</SelectItem>
                            <SelectItem value="discontinued">{t('raw_materials.filters.discontinued')}</SelectItem>
                        </SelectContent>
                    </Select>
                    <Button variant="outline" size="sm">
                        <Filter className="mr-2 h-4 w-4" />
                        {t('raw_materials.more_filters')}
                    </Button>
                </div>

                {/* Materials Table */}
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <CardTitle>{t('raw_materials.inventory_title')}</CardTitle>
                            <div className="text-sm text-muted-foreground">
                                {t('raw_materials.showing_results')
                                    .replace('{count}', filteredMaterials.length.toString())
                                    .replace('{total}', materials.length.toString())
                                }
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="rounded-md border">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>{t('raw_materials.table.sku')}</TableHead>
                                        <TableHead>{t('raw_materials.table.material_name')}</TableHead>
                                        <TableHead>{t('raw_materials.table.category')}</TableHead>
                                        <TableHead>{t('raw_materials.table.primary_supplier')}</TableHead>
                                        <TableHead>{t('raw_materials.table.unit')}</TableHead>
                                        <TableHead className="text-right">{t('raw_materials.table.standard_cost')}</TableHead>
                                        <TableHead>{t('raw_materials.table.stock_status')}</TableHead>
                                        <TableHead>{t('raw_materials.table.status')}</TableHead>
                                        <TableHead>{t('raw_materials.table.actions')}</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {filteredMaterials.length === 0 ? (
                                        <TableRow>
                                            <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                                                {materials.length === 0
                                                    ? t('raw_materials.no_materials_found')
                                                    : t('raw_materials.no_materials_match_filters')
                                                }
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        filteredMaterials.map((material) => (
                                            <TableRow key={material.id}>
                                                <TableCell className="font-mono text-sm font-medium">
                                                    {material.sku}
                                                </TableCell>
                                                <TableCell className="font-medium">
                                                    {material.name}
                                                </TableCell>
                                                <TableCell>
                                                    {getCategoryBadge(material.category)}
                                                </TableCell>
                                                <TableCell>
                                                    {material.primarySupplier?.name || (
                                                        <span className="text-muted-foreground">{t('raw_materials.no_supplier')}</span>
                                                    )}
                                                </TableCell>
                                                <TableCell className="text-sm">
                                                    {material.unit}
                                                </TableCell>
                                                <TableCell className="text-right">
                                                    {material.standard_cost ? (
                                                        <span className="font-semibold">
                                                            {formatCurrency(material.standard_cost)}
                                                        </span>
                                                    ) : (
                                                        <span className="text-muted-foreground">-</span>
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    {getStockStatus(material)}
                                                </TableCell>
                                                <TableCell>
                                                    {getStatusBadge(material.status)}
                                                </TableCell>
                                                <TableCell>
                                                    <RawMaterialActions
                                                        materialId={material.id}
                                                        materialName={material.name}
                                                        materialSku={material.sku}
                                                    />
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                    </CardContent>
                </Card>

                {/* Professional Note */}
                <Card>
                    <CardContent className="pt-6">
                        <p className="text-sm text-muted-foreground">
                            {t('raw_materials.note')}
                        </p>
                    </CardContent>
                </Card>
            </div>
        </AppShell>
    )
}
