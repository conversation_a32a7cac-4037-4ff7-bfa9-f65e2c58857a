/**
 * Manufacturing ERP - Create Raw Material Lot Page
 * Professional form for adding inventory lots to raw materials
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect, notFound } from "next/navigation"
import { db } from "@/lib/db"
import { rawMaterials, suppliers } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { AddLotPageContent } from "@/components/raw-materials/add-lot-page-content"

export default async function CreateRawMaterialLotPage({
  params,
}: {
  params: Promise<{ id: string }>
}) {
  // ✅ REQUIRED: Tenant context validation
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const { id } = await params

  // ✅ REAL DATA: Fetch raw material and suppliers
  const [material, suppliersList] = await Promise.all([
    db.query.rawMaterials.findFirst({
      where: and(
        eq(rawMaterials.id, id),
        eq(rawMaterials.company_id, context.companyId)
      ),
    }),
    db.query.suppliers.findMany({
      where: eq(suppliers.company_id, context.companyId),
      orderBy: (suppliers, { asc }) => [asc(suppliers.name)],
      limit: 100,
    }),
  ])

  if (!material) {
    notFound()
  }

  return (
    <AppShell>
      <AddLotPageContent material={material} suppliers={suppliersList} />
    </AppShell>
  )
}
