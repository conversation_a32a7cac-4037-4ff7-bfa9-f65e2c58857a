/**
 * Manufacturing ERP - Raw Material Detail Page
 * Professional detail view with lots, consumption history, and BOM usage
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect, notFound } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ArrowLeft, Edit, Plus, Package, TrendingUp, AlertTriangle, DollarSign } from "lucide-react"
import Link from "next/link"
import { db } from "@/lib/db"
import { rawMaterials, rawMaterialLots } from "@/lib/schema-postgres"
import { eq, and, desc } from "drizzle-orm"
import { ConsumptionHistoryTab } from "@/components/raw-materials/consumption-history-tab"
import { LotActions } from "@/components/raw-materials/lot-actions"
import { RawMaterialViewContent } from "@/components/raw-materials/raw-material-view-content"

export default async function RawMaterialDetailPage({
  params,
}: {
  params: Promise<{ id: string }>
}) {
  // ✅ REQUIRED: Tenant context validation
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const { id } = await params

  // ✅ REAL DATA: Fetch raw material with relationships
  const material = await db.query.rawMaterials.findFirst({
    where: and(
      eq(rawMaterials.id, id),
      eq(rawMaterials.company_id, context.companyId)
    ),
    with: {
      primarySupplier: true,
      lots: {
        orderBy: [desc(rawMaterialLots.created_at)],
        with: {
          supplier: true,
          purchaseContract: true,
          inspection: true,
        },
      },
      bomItems: {
        with: {
          product: true,
        },
      },
    },
  })

  if (!material) {
    notFound()
  }

  // Calculate summary statistics
  const totalLots = material.lots.length
  const availableLots = material.lots.filter(lot => lot.status === "available")
  const totalQty = material.lots.reduce((sum, lot) => sum + parseFloat(lot.qty || "0"), 0)
  const availableQty = availableLots.reduce((sum, lot) => sum + parseFloat(lot.qty || "0"), 0)
  const totalValue = material.lots.reduce((sum, lot) => {
    return sum + (parseFloat(lot.qty || "0") * parseFloat(lot.unit_cost || "0"))
  }, 0)



  return (
    <AppShell>
      <RawMaterialViewContent
        material={material}
        totalLots={totalLots}
        availableQty={availableQty}
        totalValue={totalValue}
      />
    </AppShell>
  )
}
