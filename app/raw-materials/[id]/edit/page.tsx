/**
 * Manufacturing ERP - Edit Raw Material Page
 * Professional form for editing existing raw materials
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect, notFound } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Package2 } from "lucide-react"
import Link from "next/link"
import { db } from "@/lib/db"
import { rawMaterials, suppliers } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { EditRawMaterialForm } from "@/components/raw-materials/edit-raw-material-form"
import { EditRawMaterialPageContent } from "@/components/raw-materials/edit-raw-material-page-content"

export default async function EditRawMaterialPage({
  params,
}: {
  params: Promise<{ id: string }>
}) {
  // ✅ REQUIRED: Tenant context validation
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const { id } = await params

  // ✅ REAL DATA: Fetch raw material and suppliers
  const [material, suppliersList] = await Promise.all([
    db.query.rawMaterials.findFirst({
      where: and(
        eq(rawMaterials.id, id),
        eq(rawMaterials.company_id, context.companyId)
      ),
      with: {
        primarySupplier: true,
      },
    }),
    db.query.suppliers.findMany({
      where: eq(suppliers.company_id, context.companyId),
      orderBy: (suppliers, { asc }) => [asc(suppliers.name)],
      limit: 100,
    }),
  ])

  if (!material) {
    notFound()
  }

  return (
    <AppShell>
      <EditRawMaterialPageContent
        material={material}
        suppliers={suppliersList}
      />
    </AppShell>
  )
}
