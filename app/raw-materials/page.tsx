/**
 * Manufacturing ERP - Raw Materials Main Page
 * Professional raw materials management with real data integration
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, Search, Filter, Package2, AlertTriangle, TrendingUp, DollarSign } from "lucide-react"
import Link from "next/link"
import { RawMaterialActions } from "@/components/raw-materials/raw-material-actions"
import { RawMaterialsContent } from "./raw-materials-content"
import { db } from "@/lib/db"
import { rawMaterials, rawMaterialLots } from "@/lib/schema-postgres"
import { eq, desc, and, count, sum, sql } from "drizzle-orm"

export default async function RawMaterialsPage() {
    // ✅ REQUIRED: Tenant context validation
    const context = await getTenantContext()
    if (!context) {
        redirect('/api/auth/login')
    }

    // ✅ SIMPLIFIED: Fetch raw materials without complex relationships for debugging
    const [materials, summary] = await Promise.all([
        // Get all raw materials with simplified relationships
        db.query.rawMaterials.findMany({
            where: eq(rawMaterials.company_id, context.companyId),
            orderBy: [desc(rawMaterials.created_at)],
            with: {
                primarySupplier: true,
                // ✅ RESTORED: Lots relationship for stock status calculation
                lots: {
                    where: eq(rawMaterialLots.status, "available"),
                },
            },
            limit: 100, // Limit for performance
        }),

        // Get summary statistics
        db.select({
            totalMaterials: count(),
            activeMaterials: count(sql`CASE WHEN ${rawMaterials.status} = 'active' THEN 1 END`),
            discontinuedMaterials: count(sql`CASE WHEN ${rawMaterials.status} = 'discontinued' THEN 1 END`),
        }).from(rawMaterials)
            .where(eq(rawMaterials.company_id, context.companyId))
    ])

    // Calculate additional metrics
    const totalMaterials = Number(summary[0]?.totalMaterials || 0)
    const activeMaterials = Number(summary[0]?.activeMaterials || 0)
    const discontinuedMaterials = Number(summary[0]?.discontinuedMaterials || 0)
    // ✅ RESTORED: Calculate materials with stock based on available lots
    const materialsWithStock = materials.filter(m => m.lots && m.lots.length > 0).length

    const getStatusBadge = (status: string) => {
        switch (status) {
            case "active":
                return <Badge className="bg-green-100 text-green-800">Active</Badge>
            case "inactive":
                return <Badge variant="secondary">Inactive</Badge>
            case "discontinued":
                return <Badge variant="destructive">Discontinued</Badge>
            default:
                return <Badge variant="outline">{status}</Badge>
        }
    }

    const getCategoryBadge = (category: string) => {
        const categoryColors = {
            yarn: "bg-blue-100 text-blue-800",
            fabric: "bg-purple-100 text-purple-800",
            dyes: "bg-red-100 text-red-800",
            chemicals: "bg-yellow-100 text-yellow-800",
            accessories: "bg-green-100 text-green-800",
            other: "bg-gray-100 text-gray-800",
        }

        return (
            <Badge className={categoryColors[category as keyof typeof categoryColors] || categoryColors.other}>
                {category.charAt(0).toUpperCase() + category.slice(1)}
            </Badge>
        )
    }

    const formatCurrency = (amount: string | number) => {
        const num = typeof amount === 'string' ? parseFloat(amount) : amount
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(num || 0)
    }

    return (
        <RawMaterialsContent
            materials={materials}
            totalMaterials={totalMaterials}
            activeMaterials={activeMaterials}
            discontinuedMaterials={discontinuedMaterials}
            materialsWithStock={materialsWithStock}
        />
    )
}