"use client"

/**
 * Manufacturing ERP - Inventory Intelligence Report Client Component
 * Advanced inventory dashboard with comprehensive bilingual localization
 */

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Progress } from "@/components/ui/progress"
import {
  Package,
  ArrowLeft,
  TrendingUp,
  AlertTriangle,
  BarChart3,
  MapPin,
  DollarSign,
  Clock,
  Target,
  Activity,
  Truck,
  Warehouse,
  TrendingDown
} from "lucide-react"
import Link from "next/link"
import { useI18n } from "@/components/i18n-provider"

interface InventoryIntelligenceReportClientProps {
  overview: any
  costs: any
  rawMaterialsData: any
  containerOpt: any
  stockHealthScore: number
  totalInventoryValue: number
  stockLevelAnalysis: any[]
  turnoverAnalysis: any[]
  locationDistribution: any[]
  reorderRecommendations: any[]
}

export function InventoryIntelligenceReportClient({
  overview,
  costs,
  rawMaterialsData,
  containerOpt,
  stockHealthScore,
  totalInventoryValue,
  stockLevelAnalysis,
  turnoverAnalysis,
  locationDistribution,
  reorderRecommendations
}: InventoryIntelligenceReportClientProps) {
  const { t } = useI18n()

  return (
    <div className="space-y-6">
      {/* Professional Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/reports">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t("inventoryIntelligence.backToReports")}
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Package className="h-8 w-8 text-blue-600" />
              {t("inventoryIntelligence.title")}
            </h1>
            <p className="text-muted-foreground">
              {t("inventoryIntelligence.subtitle")}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="flex items-center gap-1">
            <Activity className="h-3 w-3" />
            {t("inventoryIntelligence.liveInventoryData")}
          </Badge>
          <Badge variant="outline">
            {Number(overview.totalStockLots)} {t("inventoryIntelligence.stockLots")}
          </Badge>
        </div>
      </div>

      {/* Key Inventory Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("inventoryIntelligence.totalInventoryValue")}</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              ${Number(totalInventoryValue || 0).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </div>
            <p className="text-xs text-muted-foreground">
              {totalInventoryValue === 0
                ? t("inventoryIntelligence.costTrackingNotImplemented")
                : `${t("inventoryIntelligence.includesRawMaterials")}: $${Number(rawMaterialsData.totalRawMaterialValue || 0).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
              }
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("inventoryIntelligence.stockHealthScore")}</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stockHealthScore.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              {Number(overview.totalStockLots)} {t("inventoryIntelligence.activeLots")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("inventoryIntelligence.lowStockAlerts")}</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {reorderRecommendations.length}
            </div>
            <p className="text-xs text-muted-foreground">
              {t("inventoryIntelligence.itemsBelowReorderPoint")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("inventoryIntelligence.storageUtilization")}</CardTitle>
            <Warehouse className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {containerOpt.utilizationRate > 0 ? `${containerOpt.utilizationRate}%` : t("inventoryIntelligence.notAvailable")}
            </div>
            <p className="text-xs text-muted-foreground">
              {Number(containerOpt.totalLocations)} {t("inventoryIntelligence.locations")}
              {containerOpt.totalCapacity > 0 && (
                <span className="block">{containerOpt.totalUtilized?.toLocaleString() || 0} / {containerOpt.totalCapacity?.toLocaleString() || 0} {t("inventoryIntelligence.capacity")}</span>
              )}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Inventory Status Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>{t("inventoryIntelligence.inventoryComposition")}</CardTitle>
            <CardDescription>{t("inventoryIntelligence.breakdownInventoryByTypeValue")}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">{Number(overview.totalStockLots)}</div>
                  <div className="text-sm text-muted-foreground">{t("inventoryIntelligence.finishedGoods")}</div>
                  <div className="text-xs text-muted-foreground">{overview.totalQuantity || 0} {t("inventoryIntelligence.units")}</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">{Number(rawMaterialsData.totalRawMaterials)}</div>
                  <div className="text-sm text-muted-foreground">{t("inventoryIntelligence.rawMaterials")}</div>
                  <div className="text-xs text-muted-foreground">{rawMaterialsData.totalRawMaterialQuantity || 0} {t("inventoryIntelligence.units")}</div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">{t("inventoryIntelligence.finishedGoodsValue")}</span>
                  <span className="text-sm font-medium text-muted-foreground">{t("inventoryIntelligence.notAvailable")}</span>
                </div>
                <Progress value={0} className="h-2" />

                <div className="flex justify-between items-center">
                  <span className="text-sm">{t("inventoryIntelligence.rawMaterialsValue")}</span>
                  <span className="text-sm font-medium">
                    {rawMaterialsData.totalRawMaterialValue > 0
                      ? `$${Number(rawMaterialsData.totalRawMaterialValue).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
                      : t("inventoryIntelligence.notAvailable")
                    }
                  </span>
                </div>
                <Progress
                  value={rawMaterialsData.totalRawMaterialValue > 0 ? 75 : 0}
                  className="h-2"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t("inventoryIntelligence.costDistributionAnalysis")}</CardTitle>
            <CardDescription>{t("inventoryIntelligence.inventoryValueDistributionCostTiers")}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-red-600">0</div>
                  <div className="text-sm text-muted-foreground">{t("inventoryIntelligence.highValue")}</div>
                  <div className="text-xs text-muted-foreground">($100+)</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-yellow-600">0</div>
                  <div className="text-sm text-muted-foreground">{t("inventoryIntelligence.mediumValue")}</div>
                  <div className="text-xs text-muted-foreground">($10-$100)</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">0</div>
                  <div className="text-sm text-muted-foreground">{t("inventoryIntelligence.lowValue")}</div>
                  <div className="text-xs text-muted-foreground">(&lt;$10)</div>
                </div>
              </div>

              <div className="text-center pt-2">
                <div className="text-lg font-semibold">{t("inventoryIntelligence.averageUnitCost")}</div>
                <div className="text-2xl font-bold text-blue-600 text-muted-foreground">{t("inventoryIntelligence.notAvailable")}</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Inventory Analysis */}
      <Tabs defaultValue="stock-levels" className="space-y-4">
        <TabsList>
          <TabsTrigger value="stock-levels">{t("inventoryIntelligence.stockLevels")}</TabsTrigger>
          <TabsTrigger value="reorder">{t("inventoryIntelligence.reorderAnalysis")}</TabsTrigger>
          <TabsTrigger value="locations">{t("inventoryIntelligence.locationDistribution")}</TabsTrigger>
          <TabsTrigger value="turnover">{t("inventoryIntelligence.turnoverAnalysis")}</TabsTrigger>
        </TabsList>

        <TabsContent value="stock-levels" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("inventoryIntelligence.stockLevelsByProduct")}</CardTitle>
              <CardDescription>{t("inventoryIntelligence.currentInventoryLevelsValuesByProduct")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t("inventoryIntelligence.product")}</TableHead>
                      <TableHead>{t("inventoryIntelligence.sku")}</TableHead>
                      <TableHead className="text-right">{t("inventoryIntelligence.quantity")}</TableHead>
                      <TableHead className="text-right">{t("inventoryIntelligence.avgCost")}</TableHead>
                      <TableHead className="text-right">{t("inventoryIntelligence.totalValue")}</TableHead>
                      <TableHead className="text-right">{t("inventoryIntelligence.lots")}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {stockLevelAnalysis.map((item) => (
                      <TableRow key={item.productId}>
                        <TableCell className="font-medium">{item.productName || t("inventoryIntelligence.unknownProduct")}</TableCell>
                        <TableCell className="text-muted-foreground">{item.productSku || 'N/A'}</TableCell>
                        <TableCell className="text-right">{Number(item.totalQuantity || 0).toLocaleString()}</TableCell>
                        <TableCell className="text-right text-muted-foreground">
                          {t("inventoryIntelligence.notAvailable")}
                        </TableCell>
                        <TableCell className="text-right text-muted-foreground">
                          {t("inventoryIntelligence.notAvailable")}
                        </TableCell>
                        <TableCell className="text-right">{Number(item.lotCount)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reorder" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("inventoryIntelligence.reorderRecommendations")}</CardTitle>
              <CardDescription>{t("inventoryIntelligence.intelligentReorderSuggestionsBasedStockDemand")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t("inventoryIntelligence.product")}</TableHead>
                      <TableHead>{t("inventoryIntelligence.sku")}</TableHead>
                      <TableHead className="text-right">{t("inventoryIntelligence.currentStock")}</TableHead>
                      <TableHead className="text-right">{t("inventoryIntelligence.forecastDemand")}</TableHead>
                      <TableHead className="text-right">{t("inventoryIntelligence.recommendation")}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {reorderRecommendations.length > 0 ? reorderRecommendations.map((item) => (
                      <TableRow key={item.productId}>
                        <TableCell className="font-medium">{item.productName || t("inventoryIntelligence.unknownProduct")}</TableCell>
                        <TableCell className="text-muted-foreground">{item.productSku || 'N/A'}</TableCell>
                        <TableCell className="text-right">{Number(item.currentStock || 0).toLocaleString()}</TableCell>
                        <TableCell className="text-right">{Number(item.forecastDemand || 0).toLocaleString()}</TableCell>
                        <TableCell className="text-right">
                          <Badge variant={
                            item.reorderRecommendation === 'Urgent' ? 'destructive' :
                              item.reorderRecommendation === 'Recommended' ? 'secondary' :
                                'default'
                          }>
                            {item.reorderRecommendation as string}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    )) : (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                          <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                          <p>{t("inventoryIntelligence.noReorderRecommendations")}</p>
                          <p className="text-sm">{t("inventoryIntelligence.reorderRecommendationsWillAppear")}</p>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="locations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("inventoryIntelligence.inventoryByLocation")}</CardTitle>
              <CardDescription>{t("inventoryIntelligence.distributionInventoryAcrossStorageLocations")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t("inventoryIntelligence.location")}</TableHead>
                      <TableHead className="text-right">{t("inventoryIntelligence.itemCount")}</TableHead>
                      <TableHead className="text-right">{t("inventoryIntelligence.totalQuantity")}</TableHead>
                      <TableHead className="text-right">{t("inventoryIntelligence.totalValue")}</TableHead>
                      <TableHead className="text-right">{t("inventoryIntelligence.utilization")}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {locationDistribution.length > 0 ? locationDistribution.map((location) => (
                      <TableRow key={location.locationId}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4 text-muted-foreground" />
                            {location.locationName || t("inventoryIntelligence.unknownLocation")}
                          </div>
                        </TableCell>
                        <TableCell className="text-right text-muted-foreground">
                          {t("inventoryIntelligence.notAvailable")}
                        </TableCell>
                        <TableCell className="text-right text-muted-foreground">
                          {t("inventoryIntelligence.notAvailable")}
                        </TableCell>
                        <TableCell className="text-right text-muted-foreground">
                          {t("inventoryIntelligence.notAvailable")}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-muted-foreground">{t("inventoryIntelligence.notAvailable")}</span>
                          </div>
                        </TableCell>
                      </TableRow>
                    )) : (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                          <MapPin className="h-12 w-12 mx-auto mb-4 opacity-50" />
                          <p>{t("inventoryIntelligence.noLocationData")}</p>
                          <p className="text-sm">{t("inventoryIntelligence.locationDataWillAppear")}</p>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="turnover" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("inventoryIntelligence.inventoryTurnoverAnalysis")}</CardTitle>
              <CardDescription>{t("inventoryIntelligence.productMovementTurnoverPerformance")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t("inventoryIntelligence.product")}</TableHead>
                      <TableHead className="text-right">{t("inventoryIntelligence.currentStock")}</TableHead>
                      <TableHead className="text-right">{t("inventoryIntelligence.movementStatus")}</TableHead>
                      <TableHead className="text-right">{t("inventoryIntelligence.performance")}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {turnoverAnalysis.length > 0 ? turnoverAnalysis.map((item) => (
                      <TableRow key={item.productId}>
                        <TableCell className="font-medium">{item.productName || t("inventoryIntelligence.unknownProduct")}</TableCell>
                        <TableCell className="text-right">{Number(item.currentStock || 0).toLocaleString()}</TableCell>
                        <TableCell className="text-right">
                          <Badge variant={item.estimatedTurnover === 'Active' ? 'default' : 'secondary'}>
                            {item.estimatedTurnover as string}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center gap-2">
                            {item.estimatedTurnover === 'Active' ? (
                              <TrendingUp className="h-4 w-4 text-green-600" />
                            ) : (
                              <TrendingDown className="h-4 w-4 text-orange-600" />
                            )}
                            <span className="text-sm">
                              {item.estimatedTurnover === 'Active' ? t("inventoryIntelligence.good") : t("inventoryIntelligence.review")}
                            </span>
                          </div>
                        </TableCell>
                      </TableRow>
                    )) : (
                      <TableRow>
                        <TableCell colSpan={4} className="text-center py-8 text-muted-foreground">
                          <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
                          <p>{t("inventoryIntelligence.noTurnoverData")}</p>
                          <p className="text-sm">{t("inventoryIntelligence.turnoverDataWillAppear")}</p>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Inventory Intelligence Integration Notice */}
      <Card className="border-blue-200 bg-blue-50/50">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Package className="h-5 w-5 text-blue-600" />
            {t("inventoryIntelligence.inventoryIntelligenceIntegration")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm">
            {t("inventoryIntelligence.dataSourceDescription")}
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
