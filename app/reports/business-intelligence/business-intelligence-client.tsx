"use client"

/**
 * Manufacturing ERP - Business Intelligence Report Client Component
 * Executive dashboard with comprehensive bilingual localization
 */

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Progress } from "@/components/ui/progress"
import {
  BarChart3,
  ArrowLeft,
  TrendingUp,
  Users,
  DollarSign,
  FileText,
  Target,
  Activity,
  Award,
  Calendar,
  Briefcase,
  PieChart,
  TrendingDown,
  Package
} from "lucide-react"
import Link from "next/link"
import { useI18n } from "@/components/i18n-provider"

interface BusinessIntelligenceReportClientProps {
  kpis: any
  contracts: any
  operations: any
  quality: any
  profitMargin: number
  contractCompletionRate: number
  operationalEfficiency: number
  customerAnalytics: any[]
  revenueAnalysis: any[]
  topCustomers: any[]
  topProducts: any[]
  businessTrends: any[]
}

export function BusinessIntelligenceReportClient({
  kpis,
  contracts,
  operations,
  quality,
  profitMargin,
  contractCompletionRate,
  operationalEfficiency,
  customerAnalytics,
  revenueAnalysis,
  topCustomers,
  topProducts,
  businessTrends
}: BusinessIntelligenceReportClientProps) {
  const { t } = useI18n()

  // Safe number helper
  const safeNumber = (value: any, fallback = 0) => {
    const num = Number(value)
    return isNaN(num) ? fallback : num
  }

  return (
    <div className="space-y-6">
      {/* Professional Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/reports">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t("businessIntelligence.backToReports")}
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <BarChart3 className="h-8 w-8 text-purple-600" />
              {t("businessIntelligence.title")}
            </h1>
            <p className="text-muted-foreground">
              {t("businessIntelligence.subtitle")}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="flex items-center gap-1">
            <Activity className="h-3 w-3" />
            {t("businessIntelligence.liveBusinessData")}
          </Badge>
          <Badge variant="outline">
            {t("businessIntelligence.customersCount", { count: Number(kpis.totalCustomers) })}
          </Badge>
        </div>
      </div>

      {/* Executive KPI Dashboard */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("businessIntelligence.totalRevenue")}</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              ${safeNumber(kpis.totalRevenue).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {safeNumber(profitMargin).toFixed(1)}% {t("businessIntelligence.profitMargin")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("businessIntelligence.activeContracts")}</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{safeNumber(kpis.activeSalesContracts)}</div>
            <p className="text-xs text-muted-foreground">
              {safeNumber(contractCompletionRate).toFixed(1)}% {t("businessIntelligence.completionRate")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("businessIntelligence.customerBase")}</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{Number(kpis.totalCustomers)}</div>
            <p className="text-xs text-muted-foreground">
              {Number(kpis.totalSuppliers)} {t("businessIntelligence.suppliers")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("businessIntelligence.operationalEfficiency")}</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{safeNumber(operationalEfficiency).toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              {safeNumber(operations.completedWorkOrders)} {t("businessIntelligence.of")} {safeNumber(operations.totalWorkOrders)} {t("businessIntelligence.orders")}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Business Performance Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>{t("businessIntelligence.financialPerformance")}</CardTitle>
            <CardDescription>{t("businessIntelligence.revenueExpensesProfitabilityAnalysis")}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <span className="text-sm">{t("businessIntelligence.revenue")}</span>
                  </div>
                  <span className="text-sm font-medium">${Number(kpis.totalRevenue || 0).toLocaleString()}</span>
                </div>
                <Progress value={75} className="h-2" />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <TrendingDown className="h-4 w-4 text-red-600" />
                    <span className="text-sm">{t("businessIntelligence.expenses")}</span>
                  </div>
                  <span className="text-sm font-medium">${Number(kpis.totalExpenses || 0).toLocaleString()}</span>
                </div>
                <Progress value={45} className="h-2" />
              </div>

              <div className="pt-2 border-t">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">{t("businessIntelligence.netProfitMargin")}</span>
                  <span className="text-lg font-bold text-green-600">{profitMargin.toFixed(1)}%</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t("businessIntelligence.qualityOperations")}</CardTitle>
            <CardDescription>{t("businessIntelligence.qualityMetricsOperationalPerformance")}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-green-600">{Number(quality.qualityScore || 0).toFixed(1)}%</div>
                  <div className="text-sm text-muted-foreground">{t("businessIntelligence.qualityScore")}</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-600">{operationalEfficiency.toFixed(1)}%</div>
                  <div className="text-sm text-muted-foreground">{t("businessIntelligence.efficiency")}</div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>{t("businessIntelligence.qualityInspections")}: {Number(quality.passedInspections)} {t("businessIntelligence.passed")}</span>
                  <span>{t("businessIntelligence.workOrders")}: {Number(operations.completedWorkOrders)} {t("businessIntelligence.completed")}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>{t("businessIntelligence.failed")}: {Number(quality.failedInspections)}</span>
                  <span>{t("businessIntelligence.inProgress")}: {Number(operations.inProgressWorkOrders)}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Business Analytics */}
      <Tabs defaultValue="customers" className="space-y-4">
        <TabsList>
          <TabsTrigger value="customers">{t("businessIntelligence.customerAnalytics")}</TabsTrigger>
          <TabsTrigger value="contracts">{t("businessIntelligence.contractPerformance")}</TabsTrigger>
          <TabsTrigger value="revenue">{t("businessIntelligence.revenueTrends")}</TabsTrigger>
          <TabsTrigger value="operations">{t("businessIntelligence.operations")}</TabsTrigger>
        </TabsList>

        <TabsContent value="customers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("businessIntelligence.topCustomersByRevenue")}</CardTitle>
              <CardDescription>{t("businessIntelligence.customerRelationshipRevenueAnalysis")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t("businessIntelligence.customer")}</TableHead>
                      <TableHead className="text-right">{t("businessIntelligence.contracts")}</TableHead>
                      <TableHead className="text-right">{t("businessIntelligence.totalRevenue")}</TableHead>
                      <TableHead className="text-right">{t("businessIntelligence.lastActivity")}</TableHead>
                      <TableHead className="text-right">{t("businessIntelligence.status")}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {topCustomers.length > 0 ? topCustomers.map((customer, index) => (
                      <TableRow key={customer.customerName}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-xs font-bold">
                              {index + 1}
                            </div>
                            {customer.customerName || t("businessIntelligence.unknownCustomer")}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">{safeNumber(customer.totalContracts)}</TableCell>
                        <TableCell className="text-right font-medium">
                          ${safeNumber(customer.totalRevenue).toLocaleString()}
                        </TableCell>
                        <TableCell className="text-right">
                          {customer.lastActivity ? new Date(customer.lastActivity as string).toLocaleDateString() : 'N/A'}
                        </TableCell>
                        <TableCell className="text-right">
                          <Badge variant="default">{t("businessIntelligence.active")}</Badge>
                        </TableCell>
                      </TableRow>
                    )) : (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                          <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                          <p>{t("businessIntelligence.noCustomerData")}</p>
                          <p className="text-sm">{t("businessIntelligence.customerDataWillAppear")}</p>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="contracts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("businessIntelligence.contractPerformanceMetrics")}</CardTitle>
              <CardDescription>{t("businessIntelligence.salesPurchaseContractAnalytics")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{Number(contracts.totalSalesContracts)}</div>
                  <div className="text-sm text-muted-foreground">{t("businessIntelligence.totalSalesContracts")}</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{Number(contracts.activeSalesContracts)}</div>
                  <div className="text-sm text-muted-foreground">{t("businessIntelligence.activeContracts")}</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">{Number(contracts.completedSalesContracts)}</div>
                  <div className="text-sm text-muted-foreground">{t("businessIntelligence.completedContracts")}</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">
                    ${Number(contracts.avgContractValue || 0).toLocaleString()}
                  </div>
                  <div className="text-sm text-muted-foreground">{t("businessIntelligence.avgContractValue")}</div>
                </div>
              </div>

              <div className="mt-6">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">{t("businessIntelligence.contractCompletionRate")}</span>
                  <span className="text-sm">{contractCompletionRate.toFixed(1)}%</span>
                </div>
                <Progress value={contractCompletionRate} className="h-2" />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("businessIntelligence.revenueTrendsLast6Months")}</CardTitle>
              <CardDescription>{t("businessIntelligence.monthlyRevenuePerformanceGrowthAnalysis")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {revenueAnalysis.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {revenueAnalysis.map((trend, index) => (
                      <div key={index} className="text-center p-4 border rounded-lg">
                        <div className="text-lg font-semibold">
                          {new Date(trend.month as string).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}
                        </div>
                        <div className="text-2xl font-bold text-green-600">
                          ${Number(trend.monthlyRevenue || 0).toLocaleString()}
                        </div>
                        <div className="text-sm text-muted-foreground">{Number(trend.invoiceCount)} {t("businessIntelligence.invoices")}</div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <PieChart className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>{t("businessIntelligence.noRevenueTrendData")}</p>
                    <p className="text-sm">{t("businessIntelligence.revenueTrendsWillAppear")}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="operations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("businessIntelligence.operationalPerformance")}</CardTitle>
              <CardDescription>{t("businessIntelligence.productionQualityLogisticsMetrics")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <Briefcase className="h-5 w-5 text-blue-600" />
                    <span className="font-medium">{t("businessIntelligence.production")}</span>
                  </div>
                  <div className="text-2xl font-bold text-blue-600">{Number(operations.totalWorkOrders)}</div>
                  <div className="text-sm text-muted-foreground">{t("businessIntelligence.totalWorkOrders")}</div>
                  <div className="text-xs text-green-600 mt-1">
                    {Number(operations.completedWorkOrders)} {t("businessIntelligence.completed")}
                  </div>
                </div>

                <div className="text-center p-4 border rounded-lg">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <Award className="h-5 w-5 text-green-600" />
                    <span className="font-medium">{t("businessIntelligence.quality")}</span>
                  </div>
                  <div className="text-2xl font-bold text-green-600">{Number(quality.qualityScore || 0).toFixed(1)}%</div>
                  <div className="text-sm text-muted-foreground">{t("businessIntelligence.qualityScore")}</div>
                  <div className="text-xs text-blue-600 mt-1">
                    {Number(quality.totalInspections)} {t("businessIntelligence.inspections")}
                  </div>
                </div>

                <div className="text-center p-4 border rounded-lg">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <Package className="h-5 w-5 text-purple-600" />
                    <span className="font-medium">{t("businessIntelligence.inventory")}</span>
                  </div>
                  <div className="text-2xl font-bold text-purple-600">{Number(operations.totalStockLots)}</div>
                  <div className="text-sm text-muted-foreground">{t("businessIntelligence.stockLots")}</div>
                  <div className="text-xs text-orange-600 mt-1">
                    {Number(operations.totalShipments)} {t("businessIntelligence.shipments")}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Business Intelligence Integration Notice */}
      <Card className="border-purple-200 bg-purple-50/50">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-purple-600" />
            {t("businessIntelligence.businessIntelligenceIntegration")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm">
            {t("businessIntelligence.dataSourceDescription")}
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
