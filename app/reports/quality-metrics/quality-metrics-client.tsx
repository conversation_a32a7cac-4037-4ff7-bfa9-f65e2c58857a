"use client"

/**
 * Manufacturing ERP - Quality Metrics Report Client Component
 * Professional quality dashboard with comprehensive bilingual localization
 */

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Progress } from "@/components/ui/progress"
import {
  CheckCircle,
  ArrowLeft,
  TrendingUp,
  AlertTriangle,
  XCircle,
  Clock,
  Target,
  Activity,
  Award,
  Users,
  FileText,
  BarChart3
} from "lucide-react"
import Link from "next/link"
import { useI18n } from "@/components/i18n-provider"

interface QualityMetricsReportClientProps {
  overview: any
  defects: any
  certificates: any
  overallPassRate: number
  defectRate: number
  certificateValidityRate: number
  inspectionsByType: any[]
  recentInspections: any[]
  qualityTrends: any[]
  supplierQuality: any[]
  productQuality: any[]
}

export function QualityMetricsReportClient({
  overview,
  defects,
  certificates,
  overallPassRate,
  defectRate,
  certificateValidityRate,
  inspectionsByType,
  recentInspections,
  qualityTrends,
  supplierQuality,
  productQuality
}: QualityMetricsReportClientProps) {
  const { t } = useI18n()

  return (
    <div className="space-y-6">
      {/* Professional Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/reports">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t("qualityMetrics.backToReports")}
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <CheckCircle className="h-8 w-8 text-emerald-600" />
              {t("qualityMetrics.title")}
            </h1>
            <p className="text-muted-foreground">
              {t("qualityMetrics.subtitle")}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="flex items-center gap-1">
            <Activity className="h-3 w-3" />
            {t("qualityMetrics.liveQualityData")}
          </Badge>
          <Badge variant="outline">
            {Number(overview.totalInspections)} {t("qualityMetrics.inspections")}
          </Badge>
        </div>
      </div>

      {/* Key Quality Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("qualityMetrics.overallPassRate")}</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-emerald-600">{overallPassRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              {Number(overview.passedInspections)} {t("reports.of")} {Number(overview.totalInspections)} {t("qualityMetrics.inspectionsPassed")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("qualityMetrics.defectRate")}</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{defectRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              {Number(defects.totalDefects)} {t("qualityMetrics.defectsIdentified")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("qualityMetrics.pendingInspections")}</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{Number(overview.pendingInspections)}</div>
            <p className="text-xs text-muted-foreground">
              {t("qualityMetrics.awaitingQualityReview")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("qualityMetrics.certificateValidity")}</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{certificateValidityRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              {Number(certificates.validCertificates)} {t("reports.of")} {Number(certificates.totalCertificates)} {t("qualityMetrics.certificatesValid")}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quality Status Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>{t("qualityMetrics.inspectionStatusBreakdown")}</CardTitle>
            <CardDescription>{t("qualityMetrics.qualityInspectionResultsDistribution")}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm">{t("qualityMetrics.passed")}</span>
                  </div>
                  <span className="text-sm font-medium">{Number(overview.passedInspections)}</span>
                </div>
                <Progress value={overallPassRate} className="h-2" />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <XCircle className="h-4 w-4 text-red-600" />
                    <span className="text-sm">{t("qualityMetrics.failed")}</span>
                  </div>
                  <span className="text-sm font-medium">{Number(overview.failedInspections)}</span>
                </div>
                <Progress value={overview.totalInspections > 0 ? (Number(overview.failedInspections) / Number(overview.totalInspections)) * 100 : 0} className="h-2" />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-blue-600" />
                    <span className="text-sm">{t("qualityMetrics.pending")}</span>
                  </div>
                  <span className="text-sm font-medium">{Number(overview.pendingInspections)}</span>
                </div>
                <Progress value={overview.totalInspections > 0 ? (Number(overview.pendingInspections) / Number(overview.totalInspections)) * 100 : 0} className="h-2" />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    <span className="text-sm">{t("qualityMetrics.quarantined")}</span>
                  </div>
                  <span className="text-sm font-medium">{Number(overview.quarantinedInspections)}</span>
                </div>
                <Progress value={overview.totalInspections > 0 ? (Number(overview.quarantinedInspections) / Number(overview.totalInspections)) * 100 : 0} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t("qualityMetrics.defectSeverityAnalysis")}</CardTitle>
            <CardDescription>{t("qualityMetrics.breakdownDefectsBySeverity")}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-red-600">{Number(defects.criticalDefects)}</div>
                  <div className="text-sm text-muted-foreground">{t("qualityMetrics.critical")}</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-orange-600">{Number(defects.majorDefects)}</div>
                  <div className="text-sm text-muted-foreground">{t("qualityMetrics.major")}</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-yellow-600">{Number(defects.minorDefects)}</div>
                  <div className="text-sm text-muted-foreground">{t("qualityMetrics.minor")}</div>
                </div>
              </div>

              {defects.totalDefects > 0 && (
                <div className="space-y-2">
                  <div className="text-sm font-medium">{t("qualityMetrics.defectDistribution")}</div>
                  <div className="space-y-1">
                    <div className="flex justify-between text-xs">
                      <span>{t("qualityMetrics.criticalPercentage", {
                        percentage: ((Number(defects.criticalDefects) / Number(defects.totalDefects)) * 100).toFixed(1)
                      })}</span>
                      <span>{t("qualityMetrics.majorPercentage", {
                        percentage: ((Number(defects.majorDefects) / Number(defects.totalDefects)) * 100).toFixed(1)
                      })}</span>
                      <span>{t("qualityMetrics.minorPercentage", {
                        percentage: ((Number(defects.minorDefects) / Number(defects.totalDefects)) * 100).toFixed(1)
                      })}</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Quality Analysis */}
      <Tabs defaultValue="inspections" className="space-y-4">
        <TabsList>
          <TabsTrigger value="inspections">{t("qualityMetrics.inspectionTypes")}</TabsTrigger>
          <TabsTrigger value="products">{t("qualityMetrics.productQuality")}</TabsTrigger>
          <TabsTrigger value="trends">{t("qualityMetrics.qualityTrends")}</TabsTrigger>
          <TabsTrigger value="recent">{t("qualityMetrics.recentInspections")}</TabsTrigger>
        </TabsList>

        <TabsContent value="inspections" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("qualityMetrics.qualityInspectionsByType")}</CardTitle>
              <CardDescription>{t("qualityMetrics.performanceMetricsDifferentInspectionTypes")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t("qualityMetrics.inspectionType")}</TableHead>
                      <TableHead className="text-right">{t("qualityMetrics.totalInspections")}</TableHead>
                      <TableHead className="text-right">{t("qualityMetrics.passRate")}</TableHead>
                      <TableHead className="text-right">{t("qualityMetrics.performance")}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {inspectionsByType.map((type) => {
                      const passRate = Number(type.passRate || 0)
                      return (
                        <TableRow key={type.inspectionType}>
                          <TableCell className="font-medium capitalize">
                            {type.inspectionType?.replace('_', ' ') || t("qualityMetrics.unknown")}
                          </TableCell>
                          <TableCell className="text-right">{Number(type.count)}</TableCell>
                          <TableCell className="text-right">{passRate.toFixed(1)}%</TableCell>
                          <TableCell className="text-right">
                            <Badge variant={passRate >= 95 ? 'default' : passRate >= 85 ? 'secondary' : 'destructive'}>
                              {passRate >= 95 ? t("qualityMetrics.excellent") : passRate >= 85 ? t("qualityMetrics.good") : t("qualityMetrics.needsImprovement")}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="products" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("qualityMetrics.productQualityPerformance")}</CardTitle>
              <CardDescription>{t("qualityMetrics.qualityMetricsByProduct")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t("qualityMetrics.product")}</TableHead>
                      <TableHead>{t("qualityMetrics.sku")}</TableHead>
                      <TableHead className="text-right">{t("qualityMetrics.inspections")}</TableHead>
                      <TableHead className="text-right">{t("qualityMetrics.passRate")}</TableHead>
                      <TableHead className="text-right">{t("qualityMetrics.qualityScore")}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {productQuality.map((product) => {
                      const passRate = Number(product.passRate || 0)
                      return (
                        <TableRow key={product.productId}>
                          <TableCell className="font-medium">{product.productName || t("qualityMetrics.unknownProduct")}</TableCell>
                          <TableCell className="text-muted-foreground">{product.productSku || 'N/A'}</TableCell>
                          <TableCell className="text-right">{Number(product.totalInspections)}</TableCell>
                          <TableCell className="text-right">{passRate.toFixed(1)}%</TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center gap-2">
                              <Progress value={passRate} className="w-16 h-2" />
                              <Badge variant={passRate >= 95 ? 'default' : passRate >= 85 ? 'secondary' : 'destructive'} className="text-xs">
                                {passRate >= 95 ? 'A' : passRate >= 85 ? 'B' : 'C'}
                              </Badge>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("qualityMetrics.qualityTrendsLast6Months")}</CardTitle>
              <CardDescription>{t("qualityMetrics.historicalQualityPerformanceAnalysis")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {qualityTrends.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {qualityTrends.map((trend, index) => (
                      <div key={index} className="text-center p-4 border rounded-lg">
                        <div className="text-lg font-semibold">
                          {new Date(trend.month as string).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}
                        </div>
                        <div className="text-2xl font-bold text-emerald-600">{Number(trend.passRate || 0).toFixed(1)}%</div>
                        <div className="text-sm text-muted-foreground">{t("qualityMetrics.inspectionsCount", { count: Number(trend.totalInspections) })}</div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>{t("qualityMetrics.noQualityTrendData")}</p>
                    <p className="text-sm">{t("qualityMetrics.qualityTrendsWillAppear")}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recent" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("qualityMetrics.recentQualityInspections")}</CardTitle>
              <CardDescription>{t("qualityMetrics.latestInspectionResultsStatus")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t("qualityMetrics.inspectionDate")}</TableHead>
                      <TableHead>{t("qualityMetrics.type")}</TableHead>
                      <TableHead>{t("qualityMetrics.product")}</TableHead>
                      <TableHead>{t("qualityMetrics.inspector")}</TableHead>
                      <TableHead>{t("qualityMetrics.status")}</TableHead>
                      <TableHead>{t("qualityMetrics.defects")}</TableHead>
                      <TableHead>{t("qualityMetrics.certificates")}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {recentInspections.slice(0, 10).map((inspection) => (
                      <TableRow key={inspection.id}>
                        <TableCell className="text-sm">
                          {new Date(inspection.inspection_date).toLocaleDateString()}
                        </TableCell>
                        <TableCell className="capitalize">
                          {inspection.inspection_type?.replace('_', ' ')}
                        </TableCell>
                        <TableCell>{t("qualityMetrics.workOrderNumber", { number: inspection.work_order_id?.slice(-8) || 'N/A' })}</TableCell>
                        <TableCell>{inspection.inspector}</TableCell>
                        <TableCell>
                          <Badge variant={
                            inspection.status === 'passed' ? 'default' :
                              inspection.status === 'failed' ? 'destructive' :
                                inspection.status === 'quarantined' ? 'secondary' :
                                  'outline'
                          }>
                            {t(`qualityMetrics.status.${inspection.status}`) || inspection.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-center">
                          <CheckCircle className="h-4 w-4 text-green-600 mx-auto" />
                        </TableCell>
                        <TableCell className="text-center">
                          <Clock className="h-4 w-4 text-muted-foreground mx-auto" />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Quality Data Integration Notice */}
      <Card className="border-emerald-200 bg-emerald-50/50">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-emerald-600" />
            {t("qualityMetrics.qualityDataIntegration")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm">
            {t("qualityMetrics.dataSourceDescription")}
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
