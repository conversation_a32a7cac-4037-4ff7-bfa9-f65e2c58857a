/**
 * Manufacturing ERP - Quality Metrics Report
 * Comprehensive quality report integrating inspection results, defect rates, compliance metrics, and supplier quality performance
 * 
 * This report provides:
 * - Inspection results analytics and defect rate analysis
 * - Compliance tracking and certificate status overview
 * - Supplier quality performance metrics
 * - Quality trend analysis and improvement insights
 * - Integration with production workflow
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import Link from "next/link"
import { QualityMetricsReportClient } from "./quality-metrics-client"
import { db } from "@/lib/db"
import {
  qualityInspections,
  qualityDefects,
  qualityCertificates,
  qualityStandards,
  workOrders,
  products,
  suppliers,
  customers
} from "@/lib/schema-postgres"
import { eq, and, count, sum, sql, desc, avg, gte, lte } from "drizzle-orm"

export default async function QualityMetricsReportPage() {
  // ✅ REQUIRED: Tenant context validation
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  // ✅ REAL DATA: Fetch comprehensive quality data
  const [
    qualityOverview,
    inspectionsByType,
    defectAnalysis,
    certificateStatus,
    recentInspections,
    qualityTrends,
    supplierQuality,
    productQuality
  ] = await Promise.all([
    // Overall quality metrics
    db.select({
      totalInspections: count(),
      passedInspections: count(sql`CASE WHEN ${qualityInspections.status} = 'passed' THEN 1 END`),
      failedInspections: count(sql`CASE WHEN ${qualityInspections.status} = 'failed' THEN 1 END`),
      pendingInspections: count(sql`CASE WHEN ${qualityInspections.status} = 'pending' THEN 1 END`),
      quarantinedInspections: count(sql`CASE WHEN ${qualityInspections.status} = 'quarantined' THEN 1 END`),
    }).from(qualityInspections)
      .leftJoin(workOrders, eq(qualityInspections.work_order_id, workOrders.id))
      .where(eq(workOrders.company_id, context.companyId)),

    // Inspections by type
    db.select({
      inspectionType: qualityInspections.inspection_type,
      count: count(),
      passRate: avg(sql`CASE WHEN ${qualityInspections.status} = 'passed' THEN 100.0 ELSE 0.0 END`),
    }).from(qualityInspections)
      .leftJoin(workOrders, eq(qualityInspections.work_order_id, workOrders.id))
      .where(eq(workOrders.company_id, context.companyId))
      .groupBy(qualityInspections.inspection_type),

    // Defect analysis
    db.select({
      totalDefects: count(),
      criticalDefects: count(sql`CASE WHEN ${qualityDefects.severity} = 'critical' THEN 1 END`),
      majorDefects: count(sql`CASE WHEN ${qualityDefects.severity} = 'major' THEN 1 END`),
      minorDefects: count(sql`CASE WHEN ${qualityDefects.severity} = 'minor' THEN 1 END`),
    }).from(qualityDefects).where(eq(qualityDefects.company_id, context.companyId)),

    // Certificate status
    db.select({
      totalCertificates: count(),
      validCertificates: count(sql`CASE WHEN ${qualityCertificates.valid_until}::date > CURRENT_DATE THEN 1 END`),
      expiredCertificates: count(sql`CASE WHEN ${qualityCertificates.valid_until}::date <= CURRENT_DATE THEN 1 END`),
    }).from(qualityCertificates)
      .leftJoin(qualityInspections, eq(qualityCertificates.inspection_id, qualityInspections.id))
      .leftJoin(workOrders, eq(qualityInspections.work_order_id, workOrders.id))
      .where(eq(workOrders.company_id, context.companyId)),

    // Recent inspections
    db.select({
      id: qualityInspections.id,
      inspection_date: qualityInspections.inspection_date,
      inspection_type: qualityInspections.inspection_type,
      status: qualityInspections.status,
      inspector: qualityInspections.inspector,
      work_order_id: qualityInspections.work_order_id,
      created_at: qualityInspections.created_at,
    }).from(qualityInspections)
      .where(eq(qualityInspections.company_id, context.companyId))
      .orderBy(desc(qualityInspections.created_at))
      .limit(20),

    // Quality trends (last 6 months)
    db.select({
      month: sql`DATE_TRUNC('month', ${qualityInspections.inspection_date}::date)`,
      totalInspections: count(),
      passedInspections: count(sql`CASE WHEN ${qualityInspections.status} = 'passed' THEN 1 END`),
      passRate: avg(sql`CASE WHEN ${qualityInspections.status} = 'passed' THEN 100.0 ELSE 0.0 END`),
    }).from(qualityInspections)
      .leftJoin(workOrders, eq(qualityInspections.work_order_id, workOrders.id))
      .where(and(
        eq(workOrders.company_id, context.companyId),
        sql`${qualityInspections.inspection_date}::date >= CURRENT_DATE - INTERVAL '6 months'`
      ))
      .groupBy(sql`DATE_TRUNC('month', ${qualityInspections.inspection_date}::date)`)
      .orderBy(sql`DATE_TRUNC('month', ${qualityInspections.inspection_date}::date)`),

    // Supplier quality performance (placeholder - would need supplier relationship)
    db.select({
      supplierId: sql`'placeholder'`,
      supplierName: sql`'Supplier Analysis'`,
      totalInspections: count(),
      passRate: avg(sql`CASE WHEN ${qualityInspections.status} = 'passed' THEN 100.0 ELSE 0.0 END`),
    }).from(qualityInspections)
      .leftJoin(workOrders, eq(qualityInspections.work_order_id, workOrders.id))
      .where(eq(workOrders.company_id, context.companyId))
      .limit(1),

    // Product quality performance
    db.select({
      productId: workOrders.product_id,
      productName: products.name,
      productSku: products.sku,
      totalInspections: count(),
      passedInspections: count(sql`CASE WHEN ${qualityInspections.status} = 'passed' THEN 1 END`),
      passRate: avg(sql`CASE WHEN ${qualityInspections.status} = 'passed' THEN 100.0 ELSE 0.0 END`),
    }).from(qualityInspections)
      .leftJoin(workOrders, eq(qualityInspections.work_order_id, workOrders.id))
      .leftJoin(products, eq(workOrders.product_id, products.id))
      .where(eq(workOrders.company_id, context.companyId))
      .groupBy(workOrders.product_id, products.name, products.sku)
      .orderBy(desc(count()))
      .limit(10)
  ])

  // Calculate key metrics
  const overview = qualityOverview[0] || { totalInspections: 0, passedInspections: 0, failedInspections: 0, pendingInspections: 0, quarantinedInspections: 0 }
  const defects = defectAnalysis[0] || { totalDefects: 0, criticalDefects: 0, majorDefects: 0, minorDefects: 0 }
  const certificates = certificateStatus[0] || { totalCertificates: 0, validCertificates: 0, expiredCertificates: 0 }

  const overallPassRate = overview.totalInspections > 0 ? (Number(overview.passedInspections) / Number(overview.totalInspections)) * 100 : 0
  const defectRate = overview.totalInspections > 0 ? (Number(defects.totalDefects) / Number(overview.totalInspections)) * 100 : 0
  const certificateValidityRate = certificates.totalCertificates > 0 ? (Number(certificates.validCertificates) / Number(certificates.totalCertificates)) * 100 : 0

  return (
    <AppShell>
      <QualityMetricsReportClient
        overview={overview}
        defects={defects}
        certificates={certificates}
        overallPassRate={overallPassRate}
        defectRate={defectRate}
        certificateValidityRate={certificateValidityRate}
        inspectionsByType={inspectionsByType}
        recentInspections={recentInspections}
        qualityTrends={qualityTrends}
        supplierQuality={supplierQuality}
        productQuality={productQuality}
      />
    </AppShell>
  )
}
