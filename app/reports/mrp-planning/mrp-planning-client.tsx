"use client"

/**
 * Manufacturing ERP - MRP Planning Report Client Component
 * Professional MRP dashboard with comprehensive bilingual localization
 */

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  BarChart3,
  ArrowLeft,
  ExternalLink,
  TrendingUp,
  Package,
  Truck,
  Users,
  Target,
  Activity,
  CheckCircle
} from "lucide-react"
import Link from "next/link"
import { useI18n } from "@/components/i18n-provider"

interface MRPPlanningReportClientProps {
  totalForecasts: number
  totalPlans: number
  averageMargin: number
  totalSuppliers: number
  totalProfit: number
}

export function MRPPlanningReportClient({
  totalForecasts,
  totalPlans,
  averageMargin,
  totalSuppliers,
  totalProfit
}: MRPPlanningReportClientProps) {
  const { t } = useI18n()

  return (
    <div className="space-y-6">
      {/* Professional Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/reports">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t("mrpPlanning.backToReports")}
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <BarChart3 className="h-8 w-8 text-blue-600" />
              {t("mrpPlanning.title")}
            </h1>
            <p className="text-muted-foreground">
              {t("mrpPlanning.subtitle")}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="flex items-center gap-1">
            <Activity className="h-3 w-3" />
            {t("mrpPlanning.realTimeData")}
          </Badge>
          <Button asChild>
            <Link href="/planning" className="flex items-center gap-2">
              <ExternalLink className="h-4 w-4" />
              {t("mrpPlanning.fullMRPDashboard")}
            </Link>
          </Button>
        </div>
      </div>

      {/* Key Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("mrpPlanning.activeForecasts")}</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalForecasts}</div>
            <p className="text-xs text-muted-foreground">
              {t("mrpPlanning.demandForecastingPipeline")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("mrpPlanning.procurementPlans")}</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalPlans}</div>
            <p className="text-xs text-muted-foreground">
              {t("mrpPlanning.materialProcurementPlanning")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("mrpPlanning.averageMargin")}</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{averageMargin.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              {t("mrpPlanning.bomProfitabilityAnalysis")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("mrpPlanning.supplierPartners")}</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalSuppliers}</div>
            <p className="text-xs text-muted-foreground">
              {t("mrpPlanning.leadTimeOptimization")}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* MRP Integration Notice */}
      <Card className="border-blue-200 bg-blue-50/50">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-blue-600" />
            {t("mrpPlanning.provenMRPIntegration")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <p className="text-sm">
              {t("mrpPlanning.integrationDescription")}
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <strong>✅ {t("mrpPlanning.provenDataFlow")}:</strong>
                <ul className="mt-1 space-y-1 text-muted-foreground">
                  <li>• {t("mrpPlanning.dataFlow1")}</li>
                  <li>• {t("mrpPlanning.dataFlow2")}</li>
                  <li>• {t("mrpPlanning.dataFlow3")}</li>
                </ul>
              </div>
              <div>
                <strong>✅ {t("mrpPlanning.enterpriseFeatures")}:</strong>
                <ul className="mt-1 space-y-1 text-muted-foreground">
                  <li>• {t("mrpPlanning.feature1")}</li>
                  <li>• {t("mrpPlanning.feature2")}</li>
                  <li>• {t("mrpPlanning.feature3")}</li>
                </ul>
              </div>
            </div>
            <div className="pt-2">
              <Button asChild>
                <Link href="/planning" className="flex items-center gap-2">
                  <ExternalLink className="h-4 w-4" />
                  {t("mrpPlanning.accessFullDashboard")}
                </Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              {t("mrpPlanning.demandForecasting")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">
              {t("mrpPlanning.demandForecastingDesc")}
            </p>
            <Button asChild size="sm" className="w-full">
              <Link href="/planning/forecasting">{t("mrpPlanning.viewForecasts")}</Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <Package className="h-4 w-4" />
              {t("mrpPlanning.procurementPlanningTitle")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">
              {t("mrpPlanning.procurementPlanningDesc")}
            </p>
            <Button asChild size="sm" className="w-full">
              <Link href="/planning/procurement">{t("mrpPlanning.viewPlans")}</Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <Truck className="h-4 w-4" />
              {t("mrpPlanning.containerOptimization")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">
              {t("mrpPlanning.containerOptimizationDesc")}
            </p>
            <Button asChild size="sm" className="w-full">
              <Link href="/planning/container-optimization">{t("mrpPlanning.optimize")}</Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <Users className="h-4 w-4" />
              {t("mrpPlanning.supplierPerformance")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">
              {t("mrpPlanning.supplierPerformanceDesc")}
            </p>
            <Button asChild size="sm" className="w-full">
              <Link href="/suppliers">{t("mrpPlanning.viewSuppliers")}</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
