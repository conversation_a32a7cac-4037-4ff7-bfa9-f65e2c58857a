"use client"

/**
 * Manufacturing ERP - Reports Page Client Component
 * Professional reporting dashboard with comprehensive bilingual localization
 */

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  BarChart3,
  DollarSign,
  Factory,
  CheckCircle,
  Package,
  TrendingUp,
  ArrowRight,
  Activity,
  Target,
  Zap
} from "lucide-react"
import Link from "next/link"
import { useI18n } from "@/components/i18n-provider"

interface ReportsPageClientProps {
  essentialReports: any[]
  arData: any
  apData: any
  production: any
  quality: any
  inventory: any
}

// Icon mapping for client-side rendering
const iconMap = {
  BarChart3,
  DollarSign,
  Factory,
  CheckCircle,
  Package,
  TrendingUp
} as const

type IconName = keyof typeof iconMap

export function ReportsPageClient({
  essentialReports,
  arData,
  apData,
  production,
  quality,
  inventory
}: ReportsPageClientProps) {
  const { t } = useI18n()

  return (
    <div className="space-y-8">
      {/* Professional Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t("reports.title")}</h1>
          <p className="text-muted-foreground">
            {t("reports.subtitle")}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="flex items-center gap-1">
            <Zap className="h-3 w-3" />
            {t("reports.simplifiedArchitecture")}
          </Badge>
          <Badge variant="outline">{t("reports.essentialReportsBadge")}</Badge>
        </div>
      </div>

      {/* Executive Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("reports.totalRevenue")}</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${Number(arData.total || 0).toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {t("reports.from")} {Number(arData.count || 0)} {t("reports.arInvoices")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("reports.productionEfficiency")}</CardTitle>
            <Factory className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {production?.total > 0 ? Math.round((Number(production.completed) / Number(production.total)) * 100) : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              {Number(production?.completed || 0)} {t("reports.of")} {Number(production?.total || 0)} {t("reports.ordersCompleted")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("reports.qualityPassRate")}</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {quality?.total > 0 ? Math.round((Number(quality.passed) / Number(quality.total)) * 100) : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              {Number(quality?.passed || 0)} {t("reports.of")} {Number(quality?.total || 0)} {t("reports.inspectionsPassed")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("reports.inventoryValue")}</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${Number(inventory?.value || 0).toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {t("reports.across")} {Number(inventory?.total || 0)} {t("reports.stockItems")}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 6 Essential Reports Grid */}
      <div>
        <h2 className="text-xl font-semibold mb-6 flex items-center gap-2">
          <Target className="h-5 w-5" />
          {t("reports.essentialReports")}
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {essentialReports.map((report) => {
            const IconComponent = iconMap[report.iconName as IconName]
            return (
              <Card key={report.id} className="hover:shadow-lg transition-all duration-200 group">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className={`p-2 rounded-lg ${report.color}`}>
                      {IconComponent && <IconComponent className="h-6 w-6" />}
                    </div>
                    <Badge variant="secondary">{t(`reports.status.${report.status}`)}</Badge>
                  </div>
                  <CardTitle className="text-lg">{t(`reports.${report.id}.title`)}</CardTitle>
                  <CardDescription className="text-sm leading-relaxed">
                    {t(`reports.${report.id}.description`)}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {report.keyMetrics.map((metric: any, index: number) => (
                      <div key={index} className="flex justify-between items-center text-sm">
                        <span className="text-muted-foreground">{t(`reports.${report.id}.metrics.${index}.label`)}</span>
                        <span className="font-medium">{metric.value}</span>
                      </div>
                    ))}
                  </div>
                  <Button asChild className="w-full mt-4 group-hover:bg-primary/90 transition-colors">
                    <Link href={report.href} className="flex items-center justify-center gap-2">
                      {t("reports.viewReport")}
                      <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>


    </div>
  )
}
