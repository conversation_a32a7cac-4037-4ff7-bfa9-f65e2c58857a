"use client"

/**
 * Manufacturing ERP - Financial Performance Report Client Component
 * Professional financial dashboard with comprehensive bilingual localization
 */

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import {
  DollarSign,
  ArrowLeft,
  TrendingUp,
  TrendingDown,
  CreditCard,
  Target,
  CheckCircle
} from "lucide-react"
import Link from "next/link"
import { useI18n } from "@/components/i18n-provider"

interface FinancialPerformanceReportClientProps {
  currentRevenueValue: number
  currentExpensesValue: number
  currentProfit: number
  currentMargin: number
  revenueGrowth: number
  profitGrowth: number
  arAging: any
  apAging: any
  totalArOutstanding: number
  totalApOutstanding: number
  recentArInvoices: any[]
  recentApInvoices: any[]
  contractProfitability: any
  currentRevenue: any
  currentExpenses: any
}

export function FinancialPerformanceReportClient({
  currentRevenueValue,
  currentExpensesValue,
  currentProfit,
  currentMargin,
  revenueGrowth,
  profitGrowth,
  arAging,
  apAging,
  totalArOutstanding,
  totalApOutstanding,
  recentArInvoices,
  recentApInvoices,
  contractProfitability,
  currentRevenue,
  currentExpenses
}: FinancialPerformanceReportClientProps) {
  const { t } = useI18n()

  return (
    <div className="space-y-6">
      {/* Professional Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/reports">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t("financialPerformance.backToReports")}
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <DollarSign className="h-8 w-8 text-green-600" />
              {t("financialPerformance.title")}
            </h1>
            <p className="text-muted-foreground">
              {t("financialPerformance.subtitle")}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="flex items-center gap-1">
            <CheckCircle className="h-3 w-3" />
            {t("financialPerformance.realData")}
          </Badge>
          <Badge variant="outline">
            {new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
          </Badge>
        </div>
      </div>

      {/* Key Financial Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("financialPerformance.totalRevenue")}</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${currentRevenueValue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground flex items-center gap-1">
              {revenueGrowth >= 0 ? (
                <TrendingUp className="h-3 w-3 text-green-600" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-600" />
              )}
              {Math.abs(revenueGrowth).toFixed(1)}% {t("financialPerformance.fromLastMonth")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("financialPerformance.netProfit")}</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${currentProfit.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground flex items-center gap-1">
              {profitGrowth >= 0 ? (
                <TrendingUp className="h-3 w-3 text-green-600" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-600" />
              )}
              {Math.abs(profitGrowth).toFixed(1)}% {t("financialPerformance.fromLastMonth")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("financialPerformance.profitMargin")}</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{currentMargin.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              {t("financialPerformance.currentPeriod")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("financialPerformance.outstandingAR")}</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalArOutstanding.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {t("financialPerformance.accountsReceivableAging")}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Financial Analysis */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">{t("financialPerformance.overview")}</TabsTrigger>
          <TabsTrigger value="ar-aging">{t("financialPerformance.arAging")}</TabsTrigger>
          <TabsTrigger value="ap-aging">{t("financialPerformance.apAging")}</TabsTrigger>
          <TabsTrigger value="cash-flow">{t("financialPerformance.cashFlow")}</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* P&L Summary */}
            <Card>
              <CardHeader>
                <CardTitle>{t("financialPerformance.profitLossSummary")}</CardTitle>
                <CardDescription>{t("financialPerformance.currentVsPrevious")}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">{t("financialPerformance.revenue")}</span>
                    <div className="text-right">
                      <div className="font-bold">${currentRevenueValue.toLocaleString()}</div>
                      <div className="text-xs text-muted-foreground">
                        {t("financialPerformance.currentPeriod")}
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">{t("financialPerformance.expenses")}</span>
                    <div className="text-right">
                      <div className="font-bold">${currentExpensesValue.toLocaleString()}</div>
                      <div className="text-xs text-muted-foreground">
                        {t("financialPerformance.currentPeriod")}
                      </div>
                    </div>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">{t("financialPerformance.netProfit")}</span>
                      <div className="text-right">
                        <div className={`font-bold ${currentProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          ${currentProfit.toLocaleString()}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {t("financialPerformance.currentPeriod")}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Transactions */}
            <Card>
              <CardHeader>
                <CardTitle>{t("financialPerformance.recentArInvoices")}</CardTitle>
                <CardDescription>{t("financialPerformance.latestReceivableTransactions")}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {recentArInvoices.slice(0, 5).map((invoice) => (
                    <div key={invoice.id} className="flex justify-between items-center text-sm">
                      <div>
                        <div className="font-medium">{invoice.customer?.name || t("financialPerformance.unknownCustomer")}</div>
                        <div className="text-muted-foreground">{invoice.invoice_number}</div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">${Number(invoice.amount).toLocaleString()}</div>
                        <Badge variant={invoice.status === 'paid' ? 'default' : 'secondary'} className="text-xs">
                          {t(`financialPerformance.status.${invoice.status}`)}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="ar-aging" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("financialPerformance.accountsReceivableAging")}</CardTitle>
              <CardDescription>{t("financialPerformance.outstandingCustomerPayments")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">${Number(arAging.current || 0).toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">{t("financialPerformance.current")}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">${Number(arAging.overdue30 || 0).toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">{t("financialPerformance.days1to30")}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">${Number(arAging.overdue60 || 0).toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">{t("financialPerformance.days31to60")}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">${Number(arAging.overdue90 || 0).toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">{t("financialPerformance.days60Plus")}</div>
                  </div>
                </div>

                {totalArOutstanding > 0 && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>{t("financialPerformance.currentPercentage", {
                        percentage: ((Number(arAging.current || 0) / totalArOutstanding) * 100).toFixed(1)
                      })}</span>
                      <span>${Number(arAging.current || 0).toLocaleString()}</span>
                    </div>
                    <Progress value={(Number(arAging.current || 0) / totalArOutstanding) * 100} className="h-2" />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ap-aging" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("financialPerformance.accountsPayableAging")}</CardTitle>
              <CardDescription>{t("financialPerformance.outstandingSupplierPayments")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">${Number(apAging.current || 0).toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">{t("financialPerformance.current")}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">${Number(apAging.overdue30 || 0).toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">{t("financialPerformance.days1to30")}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">${Number(apAging.overdue60 || 0).toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">{t("financialPerformance.days31to60")}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">${Number(apAging.overdue90 || 0).toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">{t("financialPerformance.days60Plus")}</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cash-flow" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("financialPerformance.cashFlowAnalysis")}</CardTitle>
              <CardDescription>{t("financialPerformance.projectedCashFlow")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">{t("financialPerformance.expectedInflows")}</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>{t("financialPerformance.thisMonth")}</span>
                        <span className="font-medium text-green-600">${Number(arAging.current || 0).toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>{t("financialPerformance.next30Days")}</span>
                        <span className="font-medium">${Number(arAging.overdue30 || 0).toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">{t("financialPerformance.expectedOutflows")}</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>{t("financialPerformance.thisMonth")}</span>
                        <span className="font-medium text-red-600">${Number(apAging.current || 0).toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>{t("financialPerformance.next30Days")}</span>
                        <span className="font-medium">${Number(apAging.overdue30 || 0).toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="border-t pt-4">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">{t("financialPerformance.netCashFlowProjection")}</span>
                    <span className={`font-bold text-lg ${(Number(arAging.current || 0) - Number(apAging.current || 0)) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      ${(Number(arAging.current || 0) - Number(apAging.current || 0)).toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Data Source Notice */}
      <Card className="border-green-200 bg-green-50/50">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            {t("financialPerformance.realFinancialDataIntegration")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm">
            {t("financialPerformance.dataSourceDescription", {
              arCount: Number(currentRevenue?.count || 0),
              apCount: Number(currentExpenses?.count || 0)
            })}
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
