"use client"

/**
 * Manufacturing ERP - Production Analytics Report Client Component
 * Professional production dashboard with comprehensive bilingual localization
 */

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Progress } from "@/components/ui/progress"
import {
  Factory,
  ArrowLeft,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertTriangle,
  Target,
  Activity,
  BarChart3,
  Users,
  Package,
  Zap
} from "lucide-react"
import Link from "next/link"
import { useI18n } from "@/components/i18n-provider"

interface ProductionAnalyticsReportClientProps {
  metrics: any
  quality: any
  completionRate: number
  qualityPassRate: number
  averageEfficiency: number
  workOrdersFormatted: any[]
  workOrdersCount: number
  productionByStatus: any[]
  efficiencyData: any[]
  topProducts: any[]
}

export function ProductionAnalyticsReportClient({
  metrics,
  quality,
  completionRate,
  qualityPassRate,
  averageEfficiency,
  workOrdersFormatted,
  workOrdersCount,
  productionByStatus,
  efficiencyData,
  topProducts
}: ProductionAnalyticsReportClientProps) {
  const { t } = useI18n()

  return (
    <div className="space-y-6">
      {/* Professional Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/reports">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t("productionAnalytics.backToReports")}
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Factory className="h-8 w-8 text-orange-600" />
              {t("productionAnalytics.title")}
            </h1>
            <p className="text-muted-foreground">
              {t("productionAnalytics.subtitle")}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="flex items-center gap-1">
            <Activity className="h-3 w-3" />
            {t("productionAnalytics.realTimeData")}
          </Badge>
          <Badge variant="outline">
            {workOrdersCount} {t("productionAnalytics.workOrders")}
          </Badge>
        </div>
      </div>

      {/* Key Production Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("productionAnalytics.completionRate")}</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completionRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              {Number(metrics.completed)} {t("reports.of")} {Number(metrics.total)} {t("productionAnalytics.ordersCompleted")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("productionAnalytics.averageEfficiency")}</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{averageEfficiency.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              {t("productionAnalytics.productionEfficiencyMetric")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("productionAnalytics.qualityPassRate")}</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{qualityPassRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              {Number(quality.passedInspections)} {t("reports.of")} {Number(quality.totalInspections)} {t("productionAnalytics.inspectionsPassed")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("productionAnalytics.inProgress")}</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Number(metrics.inProgress)}</div>
            <p className="text-xs text-muted-foreground">
              {t("productionAnalytics.activeWorkOrders")}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Production Status Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>{t("productionAnalytics.productionStatusBreakdown")}</CardTitle>
            <CardDescription>{t("productionAnalytics.workOrdersByStatus")}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {productionByStatus.map((status) => {
                const percentage = metrics.total > 0 ? (Number(status.count) / Number(metrics.total)) * 100 : 0
                const statusColors = {
                  completed: 'text-green-600 bg-green-100',
                  in_progress: 'text-blue-600 bg-blue-100',
                  pending: 'text-yellow-600 bg-yellow-100',
                  delayed: 'text-red-600 bg-red-100',
                }
                const statusColor = statusColors[status.status as keyof typeof statusColors] || 'text-gray-600 bg-gray-100'

                return (
                  <div key={status.status} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className={statusColor}>
                          {t(`productionAnalytics.status.${status.status}`) || status.status?.replace('_', ' ').toUpperCase()}
                        </Badge>
                        <span className="text-sm">{Number(status.count)} {t("productionAnalytics.orders")}</span>
                      </div>
                      <span className="text-sm font-medium">{percentage.toFixed(1)}%</span>
                    </div>
                    <Progress value={percentage} className="h-2" />
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t("productionAnalytics.productionOverview")}</CardTitle>
            <CardDescription>{t("productionAnalytics.comprehensiveMetrics")}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">{Number(metrics.total)}</div>
                <div className="text-sm text-muted-foreground">{t("productionAnalytics.totalWorkOrders")}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">{Number(metrics.completed)}</div>
                <div className="text-sm text-muted-foreground">{t("productionAnalytics.completedOrders")}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-orange-600">{Number(metrics.inProgress)}</div>
                <div className="text-sm text-muted-foreground">{t("productionAnalytics.inProgressOrders")}</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Production Analysis */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">{t("productionAnalytics.overview")}</TabsTrigger>
          <TabsTrigger value="efficiency">{t("productionAnalytics.efficiencyAnalysis")}</TabsTrigger>
          <TabsTrigger value="quality">{t("productionAnalytics.qualityIntegration")}</TabsTrigger>
          <TabsTrigger value="recent">{t("productionAnalytics.recentOrders")}</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("productionAnalytics.productionOverview")}</CardTitle>
              <CardDescription>{t("productionAnalytics.comprehensiveMetricsInsights")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">{Number(metrics.total)}</div>
                  <div className="text-sm text-muted-foreground">{t("productionAnalytics.totalWorkOrders")}</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">{Number(metrics.completed)}</div>
                  <div className="text-sm text-muted-foreground">{t("productionAnalytics.completedOrders")}</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-orange-600">{Number(metrics.inProgress)}</div>
                  <div className="text-sm text-muted-foreground">{t("productionAnalytics.inProgressOrders")}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="efficiency" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("productionAnalytics.efficiencyAnalysisByProduct")}</CardTitle>
              <CardDescription>{t("productionAnalytics.productionEfficiencyMetricsProduct")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t("productionAnalytics.product")}</TableHead>
                      <TableHead className="text-right">{t("productionAnalytics.avgEfficiency")}</TableHead>
                      <TableHead className="text-right">{t("productionAnalytics.totalOrders")}</TableHead>
                      <TableHead className="text-right">{t("productionAnalytics.completed")}</TableHead>
                      <TableHead className="text-right">{t("productionAnalytics.completionRate")}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {efficiencyData.slice(0, 10).map((item) => {
                      const completionRate = Number(item.totalOrders) > 0 ? (Number(item.completedOrders) / Number(item.totalOrders)) * 100 : 0
                      return (
                        <TableRow key={item.productId}>
                          <TableCell className="font-medium">{item.productName || t("productionAnalytics.unknownProduct")}</TableCell>
                          <TableCell className="text-right">
                            <Badge variant={Number(item.avgEfficiency || 0) >= 80 ? 'default' : 'secondary'}>
                              {Number(item.avgEfficiency || 0).toFixed(1)}%
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">{Number(item.totalOrders)}</TableCell>
                          <TableCell className="text-right">{Number(item.completedOrders)}</TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center gap-2">
                              <Progress value={completionRate} className="w-16 h-2" />
                              <span className="text-sm">{completionRate.toFixed(0)}%</span>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="quality" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("productionAnalytics.qualityIntegrationMetrics")}</CardTitle>
              <CardDescription>{t("productionAnalytics.productionQualityControlIntegration")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-3">{t("productionAnalytics.qualityInspectionStatus")}</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">{t("productionAnalytics.passedInspections")}</span>
                      <Badge variant="default" className="bg-green-600">
                        {Number(quality.passedInspections)}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">{t("productionAnalytics.failedInspections")}</span>
                      <Badge variant="destructive">
                        {Number(quality.failedInspections)}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">{t("productionAnalytics.pendingInspections")}</span>
                      <Badge variant="secondary">
                        {Number(quality.pendingInspections)}
                      </Badge>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-3">{t("productionAnalytics.qualityIntegrationRate")}</h4>
                  <div className="text-center">
                    <div className="text-4xl font-bold text-green-600">{qualityPassRate.toFixed(1)}%</div>
                    <div className="text-sm text-muted-foreground">{t("productionAnalytics.overallPassRate")}</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recent" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("productionAnalytics.recentWorkOrders")}</CardTitle>
              <CardDescription>{t("productionAnalytics.latestProductionOrders")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t("productionAnalytics.workOrder")}</TableHead>
                      <TableHead>{t("productionAnalytics.product")}</TableHead>
                      <TableHead>{t("productionAnalytics.customer")}</TableHead>
                      <TableHead className="text-right">{t("productionAnalytics.progress")}</TableHead>
                      <TableHead>{t("productionAnalytics.status")}</TableHead>
                      <TableHead>{t("productionAnalytics.quality")}</TableHead>
                      <TableHead>{t("productionAnalytics.stock")}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {workOrdersFormatted.slice(0, 10).map((wo) => {
                      const progress = wo.quantity > 0 ? (wo.completed / wo.quantity) * 100 : 0
                      return (
                        <TableRow key={wo.id}>
                          <TableCell className="font-mono text-sm font-medium">{wo.woNumber}</TableCell>
                          <TableCell>{wo.product}</TableCell>
                          <TableCell>{wo.customer}</TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center gap-2">
                              <Progress value={progress} className="w-16 h-2" />
                              <span className="text-sm">{progress.toFixed(0)}%</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant={wo.status === 'completed' ? 'default' : wo.status === 'in_progress' ? 'secondary' : 'outline'}>
                              {t(`productionAnalytics.status.${wo.status}`) || wo.status?.replace('_', ' ')}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {wo.hasQualityInspections ? (
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            ) : (
                              <Clock className="h-4 w-4 text-muted-foreground" />
                            )}
                          </TableCell>
                          <TableCell>
                            {wo.stockLotsGenerated ? (
                              <Package className="h-4 w-4 text-blue-600" />
                            ) : (
                              <Clock className="h-4 w-4 text-muted-foreground" />
                            )}
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Data Integration Notice */}
      <Card className="border-orange-200 bg-orange-50/50">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-orange-600" />
            {t("productionAnalytics.productionDataIntegration")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm">
            {t("productionAnalytics.dataSourceDescription")}
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
