/**
 * Manufacturing ERP - Bill of Materials Management Page
 * 
 * Professional BOM management interface with comprehensive CRUD operations
 * Provides centralized view of all product BOMs with search and filtering
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - BOM Management Implementation
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { BOMOverviewClient } from "@/components/bom/bom-overview-client"
import { BOMHeader } from "@/components/bom/bom-header"

interface BOMOverviewItem {
  productId: string
  productName: string
  productSku: string
  bomItemCount: number
  totalEstimatedCost: number
  hasIncompleteBOM: boolean
  lastUpdated: string | Date
  categories?: string[]
  suppliers?: string[]
}

export default async function BOMManagementPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  // ✅ PROFESSIONAL: Tenant context validation
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const params = await searchParams
  const search = typeof params.search === 'string' ? params.search : ''
  const status = typeof params.status === 'string' ? params.status : 'all'

  // ✅ PROFESSIONAL: Initialize with empty data (client will fetch)
  const bomData: BOMOverviewItem[] = []
  const stats = {
    totalProducts: 0,
    productsWithBOM: 0,
    productsWithoutBOM: 0,
    incompleteBOMs: 0,
    totalBOMItems: 0,
    totalEstimatedValue: 0,
    averageBOMComplexity: 0,
  }

  return (
    <AppShell>
      <div className="space-y-6">
        {/* ✅ PROFESSIONAL: Page header with i18n support */}
        <BOMHeader />

        {/* ✅ PROFESSIONAL: Client-side BOM management */}
        <BOMOverviewClient initialData={bomData} initialStats={stats} />
      </div>
    </AppShell>
  )
}
