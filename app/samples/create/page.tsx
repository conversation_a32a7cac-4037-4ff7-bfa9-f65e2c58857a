"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { AppShell } from "@/components/app-shell"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, Save, Package } from "lucide-react"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"
import { CustomerSelect } from "@/components/forms/customer-select"
import { ProductSelect } from "@/components/forms/product-select"
import { SupplierSelect } from "@/components/forms/supplier-select"
import Link from "next/link"

export default function CreateSamplePage() {
  const router = useRouter()
  const { success: toastSuccess, error: toastError, info: toastInfo } = useSafeToast()
  const { t } = useI18n()

  const [loading, setLoading] = useState(false)
  const [customers, setCustomers] = useState<any[]>([])
  const [products, setProducts] = useState<any[]>([])
  const [suppliers, setSuppliers] = useState<any[]>([])
  const [formData, setFormData] = useState({
    // ✅ BASIC INFORMATION
    code: "",
    name: "",
    date: new Date().toISOString().split('T')[0],
    sample_type: "development",
    priority: "normal",
    status: "active",

    // ✅ BIDIRECTIONAL WORKFLOW FIELDS
    sample_direction: "outbound", // "outbound" | "inbound" | "internal"
    sample_purpose: "customer_evaluation", // Dynamic based on direction
    sender_type: "", // "customer" | "supplier" | "internal"
    receiver_type: "", // "customer" | "supplier" | "internal"

    // ✅ RELATIONSHIP FIELDS (Context-Aware)
    customer_id: "",
    product_id: "",
    supplier_id: "",

    // ✅ INBOUND SAMPLE FIELDS
    received_date: "",
    testing_status: "not_started",
    testing_results: "",
    quote_requested: false,
    quote_provided: false,

    // ✅ SPECIFICATION FIELDS
    quantity: "",
    unit: "",
    specifications: "",
    quality_requirements: "",
    delivery_date: "",
    cost: "",
    currency: "USD",
    notes: "",
  })

  // ✅ FETCH RELATIONSHIP DATA
  useEffect(() => {
    const fetchRelationshipData = async () => {
      try {
        const [customersRes, productsRes, suppliersRes] = await Promise.all([
          fetch('/api/customers'),
          fetch('/api/products'),
          fetch('/api/suppliers'),
        ])

        if (customersRes.ok) {
          const customersData = await customersRes.json()
          setCustomers(Array.isArray(customersData) ? customersData : [])
        }

        if (productsRes.ok) {
          const productsData = await productsRes.json()
          setProducts(Array.isArray(productsData) ? productsData : [])
        }

        if (suppliersRes.ok) {
          const suppliersData = await suppliersRes.json()
          setSuppliers(Array.isArray(suppliersData) ? suppliersData : [])
        }
      } catch (error) {
        console.error('Error fetching relationship data:', error)
        toastError(
          "Warning",
          "Could not load customers, products, or suppliers for selection."
        )
      }
    }

    fetchRelationshipData()
  }, [toastError])

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // ✅ DYNAMIC FORM LOGIC - Update related fields when direction changes
  const handleDirectionChange = (direction: string) => {
    setFormData(prev => ({
      ...prev,
      sample_direction: direction,
      // Reset context-dependent fields
      sample_purpose: direction === "outbound" ? "customer_evaluation" :
        direction === "inbound" ? "manufacturing_quote" : "quality_control",
      sender_type: direction === "outbound" ? "internal" : "",
      receiver_type: direction === "outbound" ? "customer" : "internal",
      // Clear relationship fields to avoid confusion
      customer_id: "",
      supplier_id: "",
      // Clear product_id when switching to inbound (since it becomes optional)
      product_id: direction === "inbound" ? "" : prev.product_id,
    }))

    // ✅ SHOW BUSINESS RULE NOTIFICATION
    if (direction === "outbound") {
      toastInfo(
        "Product Required",
        "📤 Outbound samples require product selection (sampling our own product)"
      )
    } else if (direction === "inbound") {
      toastInfo(
        "Product Optional",
        "📥 Product selection is optional (sample may not exist in our catalog yet)"
      )
    }
  }



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    console.log("🚀 Form submission started")
    console.log("📝 Form data:", formData)

    // ✅ CLIENT-SIDE VALIDATION: Outbound samples require product
    if (formData.sample_direction === "outbound" && (!formData.product_id || formData.product_id === "")) {
      toastError(
        "Product Required",
        "Outbound samples require product selection (we are sampling our own product)"
      )
      setLoading(false)
      return
    }

    try {
      console.log("📡 Making POST request to /api/samples")
      const response = await fetch('/api/samples', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      console.log("📡 Response received:", response.status, response.statusText)

      if (!response.ok) {
        throw new Error('Failed to create sample')
      }

      const result = await response.json()
      console.log("✅ Sample created successfully:", result)

      toastSuccess(
        "Sample Created",
        `Sample ${formData.code || formData.name} has been created successfully.`
      )

      // ✅ REDIRECT TO SAMPLES LIST PAGE (ALWAYS WORKS)
      console.log("🔄 Redirecting to samples list...")
      router.push('/samples')

    } catch (error) {
      console.error('Error creating sample:', error)
      toastError(
        "Creation Failed",
        "Failed to create sample. Please try again."
      )
    } finally {
      setLoading(false)
    }
  }

  return (
    <AppShell>
      <div className="space-y-6">
        {/* ✅ HEADER */}
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/samples">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t("samples.create.backToSamples")}
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{t("samples.create.title")}</h1>
            <p className="text-muted-foreground">{t("samples.create.description")}</p>
          </div>
        </div>

        {/* ✅ PROFESSIONAL ERP FORM LAYOUT */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* ✅ CARD 1: SAMPLE CLASSIFICATION & BASIC INFO */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  {t("samples.create.classification.title")}
                </CardTitle>
                <CardDescription>
                  {t("samples.create.classification.description")}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* ✅ SAMPLE DIRECTION - CRITICAL BUSINESS LOGIC */}
                <div>
                  <Label htmlFor="sample_direction">{t("samples.create.sampleDirectionRequired")}</Label>
                  <Select
                    value={formData.sample_direction}
                    onValueChange={handleDirectionChange}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="outbound">{t("samples.create.outbound")}</SelectItem>
                      <SelectItem value="inbound">{t("samples.create.inbound")}</SelectItem>
                      <SelectItem value="internal">{t("samples.create.internal")}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="code">{t("samples.create.sampleCodeRequired")}</Label>
                    <Input
                      id="code"
                      value={formData.code}
                      onChange={(e) => handleInputChange('code', e.target.value)}
                      placeholder={formData.sample_direction === "inbound" ? t("samples.create.placeholders.inboundCode") : t("samples.create.placeholders.outboundCode")}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="date">
                      {formData.sample_direction === "inbound" ? t("samples.create.receivedDateRequired") : t("samples.create.sampleDateRequired")}
                    </Label>
                    <Input
                      id="date"
                      type="date"
                      value={formData.date}
                      onChange={(e) => handleInputChange('date', e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="name">{t("samples.create.sampleNameRequired")}</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder={formData.sample_direction === "inbound" ? t("samples.create.placeholders.customerFabric") : t("samples.create.placeholders.cottonFabric")}
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="sample_type">{t("samples.create.sampleType")}</Label>
                    <Select
                      value={formData.sample_type}
                      onValueChange={(value) => handleInputChange('sample_type', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="development">{t("samples.create.options.development")}</SelectItem>
                        <SelectItem value="production">{t("samples.create.options.production")}</SelectItem>
                        <SelectItem value="quality">{t("samples.create.options.quality")}</SelectItem>
                        <SelectItem value="prototype">{t("samples.create.options.prototype")}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="priority">{t("samples.create.priority")}</Label>
                    <Select
                      value={formData.priority}
                      onValueChange={(value) => handleInputChange('priority', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">{t("samples.create.options.low")}</SelectItem>
                        <SelectItem value="normal">{t("samples.create.options.normal")}</SelectItem>
                        <SelectItem value="high">{t("samples.create.options.high")}</SelectItem>
                        <SelectItem value="urgent">{t("samples.create.options.urgent")}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* ✅ CARD 2: PRODUCT INFORMATION */}
            <Card>
              <CardHeader>
                <CardTitle>{t("samples.create.productInfo.title")}</CardTitle>
                <CardDescription>
                  {t("samples.create.productInfo.description")}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="product_id">
                    {formData.sample_direction === "outbound" ? t("samples.create.productRequired") : t("samples.create.product")}
                  </Label>
                  <ProductSelect
                    products={products}
                    value={formData.product_id}
                    onValueChange={(value) => handleInputChange('product_id', value)}
                    placeholder={
                      formData.sample_direction === "outbound"
                        ? t("samples.create.placeholders.searchProducts")
                        : t("samples.create.placeholders.searchProductsOptional")
                    }
                    showAddNew={false}
                    onProductAdded={(newProduct) => {
                      setProducts(prev => [...prev, newProduct])
                    }}
                  />
                  {formData.sample_direction === "outbound" && (
                    <p className="text-sm text-muted-foreground mt-1">
                      {t("samples.create.help.outboundProduct")}
                    </p>
                  )}
                  {formData.sample_direction === "inbound" && (
                    <p className="text-sm text-muted-foreground mt-1">
                      {t("samples.create.help.inboundProduct")}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="quantity">{t("samples.create.quantity")}</Label>
                    <Input
                      id="quantity"
                      value={formData.quantity}
                      onChange={(e) => handleInputChange('quantity', e.target.value)}
                      placeholder={t("samples.create.placeholders.quantity")}
                    />
                  </div>
                  <div>
                    <Label htmlFor="unit">{t("samples.create.unit")}</Label>
                    <Input
                      id="unit"
                      value={formData.unit}
                      onChange={(e) => handleInputChange('unit', e.target.value)}
                      placeholder={t("samples.create.placeholders.unit")}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="specifications">{t("samples.create.technicalSpecs")}</Label>
                  <Textarea
                    id="specifications"
                    value={formData.specifications}
                    onChange={(e) => handleInputChange('specifications', e.target.value)}
                    placeholder={t("samples.create.placeholders.techSpecs")}
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="quality_requirements">{t("samples.create.qualityRequirements")}</Label>
                  <Textarea
                    id="quality_requirements"
                    value={formData.quality_requirements}
                    onChange={(e) => handleInputChange('quality_requirements', e.target.value)}
                    placeholder={t("samples.create.placeholders.qualityStandards")}
                    rows={2}
                  />
                </div>
              </CardContent>
            </Card>

          </div>

          {/* ✅ SECOND ROW: BUSINESS RELATIONS & COMMERCIAL DETAILS */}
          <div className="grid gap-6 md:grid-cols-2">
            {/* ✅ CARD 3: DYNAMIC BUSINESS RELATIONS */}
            <Card>
              <CardHeader>
                <CardTitle>
                  {formData.sample_direction === "outbound" ? t("samples.create.customerDelivery") :
                    formData.sample_direction === "inbound" ? t("samples.create.sampleSource") : t("samples.create.internalRelations")}
                </CardTitle>
                <CardDescription>
                  {formData.sample_direction === "outbound" ? t("samples.create.customerReceiving") :
                    formData.sample_direction === "inbound" ? t("samples.create.whoSentSample") : t("samples.create.internalDepartment")}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* ✅ OUTBOUND: Customer receiving sample */}
                {formData.sample_direction === "outbound" && (
                  <>
                    <div>
                      <Label htmlFor="customer_id">{t("samples.create.customerRequired")}</Label>
                      <CustomerSelect
                        customers={customers}
                        value={formData.customer_id}
                        onValueChange={(value) => handleInputChange('customer_id', value)}
                        placeholder={t("samples.create.placeholders.searchCustomers")}
                        onCustomerAdded={(newCustomer) => {
                          setCustomers(prev => [...prev, newCustomer])
                        }}
                      />
                    </div>
                    <div>
                      <Label htmlFor="sample_purpose">{t("samples.create.samplePurpose")}</Label>
                      <Select
                        value={formData.sample_purpose}
                        onValueChange={(value) => handleInputChange('sample_purpose', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={t("samples.create.placeholders.whySending")} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="customer_evaluation">{t("samples.create.options.customerEvaluation")}</SelectItem>
                          <SelectItem value="marketing_demo">{t("samples.create.options.marketingDemo")}</SelectItem>
                          <SelectItem value="trade_show">{t("samples.create.options.tradeShow")}</SelectItem>
                          <SelectItem value="sales_presentation">{t("samples.create.options.salesPresentation")}</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="notes">{t("samples.create.deliveryInstructions")}</Label>
                      <Textarea
                        id="notes"
                        value={formData.notes}
                        onChange={(e) => handleInputChange('notes', e.target.value)}
                        placeholder={t("samples.create.placeholders.deliveryInstructions")}
                        rows={3}
                      />
                    </div>
                  </>
                )}

                {/* ✅ INBOUND: Customer/supplier sending sample to us */}
                {formData.sample_direction === "inbound" && (
                  <>
                    <div>
                      <Label htmlFor="sender_type">{t("samples.create.senderTypeRequired")}</Label>
                      <Select
                        value={formData.sender_type}
                        onValueChange={(value) => {
                          handleInputChange('sender_type', value)
                          // Clear the other relationship when sender type changes
                          if (value === "customer") {
                            handleInputChange('supplier_id', "")
                          } else if (value === "supplier") {
                            handleInputChange('customer_id', "")
                          }
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={t("samples.create.placeholders.whoSent")} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="customer">{t("samples.create.options.customer")}</SelectItem>
                          <SelectItem value="supplier">{t("samples.create.options.supplier")}</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Show customer dropdown if sender is customer */}
                    {formData.sender_type === "customer" && (
                      <div>
                        <Label htmlFor="customer_id">{t("samples.create.customerSenderRequired")}</Label>
                        <CustomerSelect
                          customers={customers}
                          value={formData.customer_id}
                          onValueChange={(value) => handleInputChange('customer_id', value)}
                          placeholder={t("samples.create.placeholders.searchCustomerSender")}
                          onCustomerAdded={(newCustomer) => {
                            setCustomers(prev => [...prev, newCustomer])
                          }}
                        />
                      </div>
                    )}

                    {/* Show supplier dropdown if sender is supplier */}
                    {formData.sender_type === "supplier" && (
                      <div>
                        <Label htmlFor="supplier_id">{t("samples.create.supplierSenderRequired")}</Label>
                        <SupplierSelect
                          suppliers={suppliers}
                          value={formData.supplier_id}
                          onValueChange={(value) => handleInputChange('supplier_id', value)}
                          placeholder={t("samples.create.placeholders.searchSupplierSender")}
                          onSupplierAdded={(newSupplier) => {
                            setSuppliers(prev => [...prev, newSupplier])
                          }}
                        />
                      </div>
                    )}

                    <div>
                      <Label htmlFor="sample_purpose">{t("samples.create.samplePurpose")}</Label>
                      <Select
                        value={formData.sample_purpose}
                        onValueChange={(value) => handleInputChange('sample_purpose', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={t("samples.create.placeholders.whySent")} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="manufacturing_quote">{t("samples.create.options.manufacturingQuote")}</SelectItem>
                          <SelectItem value="material_testing">{t("samples.create.options.materialTesting")}</SelectItem>
                          <SelectItem value="reverse_engineering">{t("samples.create.options.reverseEngineering")}</SelectItem>
                          <SelectItem value="quality_comparison">{t("samples.create.options.qualityComparison")}</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="notes">{t("samples.create.sampleCondition")}</Label>
                      <Textarea
                        id="notes"
                        value={formData.notes}
                        onChange={(e) => handleInputChange('notes', e.target.value)}
                        placeholder={t("samples.create.placeholders.sampleCondition")}
                        rows={3}
                      />
                    </div>
                  </>
                )}

                {/* ✅ INTERNAL: Internal testing/R&D */}
                {formData.sample_direction === "internal" && (
                  <>
                    <div>
                      <Label htmlFor="sample_purpose">{t("samples.create.internalPurpose")}</Label>
                      <Select
                        value={formData.sample_purpose}
                        onValueChange={(value) => handleInputChange('sample_purpose', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={t("samples.create.placeholders.internalPurpose")} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="quality_control">{t("samples.create.options.qualityControl")}</SelectItem>
                          <SelectItem value="r_and_d">{t("samples.create.options.rAndD")}</SelectItem>
                          <SelectItem value="process_improvement">{t("samples.create.options.processImprovement")}</SelectItem>
                          <SelectItem value="product_development">{t("samples.create.options.productDevelopment")}</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="notes">{t("samples.create.internalRequirements")}</Label>
                      <Textarea
                        id="notes"
                        value={formData.notes}
                        onChange={(e) => handleInputChange('notes', e.target.value)}
                        placeholder={t("samples.create.placeholders.internalRequirements")}
                        rows={3}
                      />
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* ✅ CARD 4: DYNAMIC COMMERCIAL/TESTING DETAILS */}
            <Card>
              <CardHeader>
                <CardTitle>{t("samples.create.commercialDetails.title")}</CardTitle>
                <CardDescription>
                  {t("samples.create.commercialDetails.description")}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* ✅ OUTBOUND SAMPLE FIELDS */}
                {formData.sample_direction === "outbound" && (
                  <>
                    <div>
                      <Label htmlFor="delivery_date">{t("samples.create.deliveryDate")}</Label>
                      <Input
                        id="delivery_date"
                        type="date"
                        value={formData.delivery_date}
                        onChange={(e) => handleInputChange('delivery_date', e.target.value)}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="cost">{t("samples.create.sampleCost")}</Label>
                        <Input
                          id="cost"
                          value={formData.cost}
                          onChange={(e) => handleInputChange('cost', e.target.value)}
                          placeholder={t("samples.create.placeholders.sampleCost")}
                        />
                      </div>
                      <div>
                        <Label htmlFor="currency">{t("samples.create.currency")}</Label>
                        <Select
                          value={formData.currency}
                          onValueChange={(value) => handleInputChange('currency', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="USD">USD</SelectItem>
                            <SelectItem value="EUR">EUR</SelectItem>
                            <SelectItem value="CNY">CNY</SelectItem>
                            <SelectItem value="GBP">GBP</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </>
                )}

                {/* ✅ INBOUND SAMPLE FIELDS */}
                {formData.sample_direction === "inbound" && (
                  <>
                    <div>
                      <Label htmlFor="testing_status">{t("samples.create.testingStatus")}</Label>
                      <Select
                        value={formData.testing_status}
                        onValueChange={(value) => handleInputChange('testing_status', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="not_started">{t("samples.create.options.notStarted")}</SelectItem>
                          <SelectItem value="in_progress">{t("samples.create.options.inProgress")}</SelectItem>
                          <SelectItem value="completed">{t("samples.create.options.completed")}</SelectItem>
                          <SelectItem value="failed">{t("samples.create.options.failed")}</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="testing_results">{t("samples.create.testingResults")}</Label>
                      <Textarea
                        id="testing_results"
                        value={formData.testing_results}
                        onChange={(e) => handleInputChange('testing_results', e.target.value)}
                        placeholder={t("samples.create.placeholders.testingResults")}
                        rows={3}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="quote_requested"
                          checked={formData.quote_requested}
                          onChange={(e) => handleInputChange('quote_requested', e.target.checked)}
                          className="rounded border-gray-300"
                          aria-label="Quote Requested"
                        />
                        <Label htmlFor="quote_requested">{t("samples.create.quoteRequested")}</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="quote_provided"
                          checked={formData.quote_provided}
                          onChange={(e) => handleInputChange('quote_provided', e.target.checked)}
                          className="rounded border-gray-300"
                          aria-label="Quote Provided"
                        />
                        <Label htmlFor="quote_provided">{t("samples.create.quoteProvided")}</Label>
                      </div>
                    </div>
                  </>
                )}

                {/* ✅ INTERNAL SAMPLE FIELDS */}
                {formData.sample_direction === "internal" && (
                  <div>
                    <Label htmlFor="delivery_date">{t("samples.create.targetCompletion")}</Label>
                    <Input
                      id="delivery_date"
                      type="date"
                      value={formData.delivery_date}
                      onChange={(e) => handleInputChange('delivery_date', e.target.value)}
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          </div>



          {/* ✅ ACTIONS */}
          <div className="flex items-center gap-4">
            <Button type="submit" disabled={loading}>
              <Save className="h-4 w-4 mr-2" />
              {loading ? t("samples.create.saving") : t("samples.create.save")}
            </Button>
            <Button type="button" variant="outline" asChild>
              <Link href="/samples">{t("samples.create.cancel")}</Link>
            </Button>
          </div>
        </form>
      </div>
    </AppShell>
  )
}
