"use client"

import { Suspense } from "react"
import { useI18n } from "@/components/i18n-provider"
import { QualityInspectionView } from "./quality-inspection-view"

interface QualityInspectionPageClientProps {
  inspection: any
}

export function QualityInspectionPageClient({ inspection }: QualityInspectionPageClientProps) {
  const { t } = useI18n()

  return (
    <Suspense fallback={<div>{t('quality.loading_quality_inspection')}</div>}>
      <QualityInspectionView inspection={inspection} />
    </Suspense>
  )
}
