import { Suspense } from "react"
import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect, notFound } from "next/navigation"
import { db } from "@/lib/db"
import { qualityInspections, workOrders } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { QualityInspectionPageClient } from "./quality-inspection-page-client"

async function getQualityInspection(id: string, companyId: string) {
  try {
    // ✅ STEP 1: Get the inspection with archive fields
    const inspection = await db.select().from(qualityInspections)
      .where(and(
        eq(qualityInspections.id, id),
        eq(qualityInspections.company_id, companyId)
      ))
      .limit(1)
      .then(rows => rows[0] || null)

    if (!inspection) return null

    // ✅ STEP 2: Get work order details using direct fallback (API doesn't exist yet)
    let workOrderData = null
    if (inspection.work_order_id) {
      workOrderData = await getWorkOrderDirect(inspection.work_order_id, companyId)
    }

    return {
      ...inspection,
      workOrder: workOrderData
    }
  } catch (error) {
    console.error("Error fetching quality inspection:", error)
    return null
  }
}

// ✅ FIXED: Proper database query for work order details
async function getWorkOrderDirect(workOrderId: string, companyId: string) {
  try {
    // Query the actual work orders table with relations
    const workOrder = await db.query.workOrders.findFirst({
      where: and(
        eq(workOrders.id, workOrderId),
        eq(workOrders.company_id, companyId)
      ),
      with: {
        product: true,
        salesContract: {
          with: {
            customer: true
          }
        }
      }
    })

    return workOrder
  } catch (error) {
    console.error("Direct work order query failed:", error)
    return null
  }
}

interface QualityInspectionPageProps {
  params: Promise<{ id: string }>
}

export default async function QualityInspectionPage({ params }: QualityInspectionPageProps) {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const { id } = await params
  const inspection = await getQualityInspection(id, context.companyId)

  if (!inspection) {
    notFound()
  }

  return (
    <AppShell>
      <QualityInspectionPageClient inspection={inspection} />
    </AppShell>
  )
}
