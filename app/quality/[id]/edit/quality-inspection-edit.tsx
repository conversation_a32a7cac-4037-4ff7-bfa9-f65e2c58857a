"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"
import { AttachmentManager } from "@/components/quality/attachment-manager"
import {
  ArrowLeft,
  Save,
  RefreshCw,
  FileText,
  Package
} from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"

interface QualityInspectionEditProps {
  inspection: any
  workOrders: any[]
}

export function QualityInspectionEdit({ inspection, workOrders }: QualityInspectionEditProps) {
  const { t } = useI18n()
  const { success: toastSuccess, error: toastError } = useSafeToast()
  const router = useRouter()
  const [loading, setLoading] = useState(false)

  // ✅ FORM STATE
  const [formData, setFormData] = useState({
    work_order_id: inspection.work_order_id || "",
    inspector: inspection.inspector || "",
    inspection_type: inspection.inspection_type || "final",
    status: inspection.status || "pending",
    scheduled_date: inspection.scheduled_date ?
      new Date(inspection.scheduled_date).toISOString().split('T')[0] :
      new Date().toISOString().split('T')[0],
    completed_date: inspection.completed_date ?
      new Date(inspection.completed_date).toISOString().split('T')[0] : "",
    notes: inspection.notes || ""
  })

  // ✅ PROFESSIONAL ERP: HANDLE FORM CHANGES
  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  // ✅ PROFESSIONAL ERP: SAVE CHANGES
  const handleSave = async () => {
    if (!formData.work_order_id || !formData.inspector) {
      toastError(t('quality.fill_required_fields'))
      return
    }

    try {
      setLoading(true)
      const response = await fetch(`/api/quality/inspections/${inspection.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      if (response.ok) {
        toastSuccess(t('quality.inspection_updated_successfully'))
        router.push(`/quality/${inspection.id}`)
      } else {
        const error = await response.text()
        toastError(t('quality.failed_to_update_inspection', { error }))
      }
    } catch (error) {
      console.error('Save inspection error:', error)
      toastError(t('quality.failed_to_update_quality_inspection'))
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* ✅ PROFESSIONAL HEADER WITH BREADCRUMB */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/quality/${inspection.id}`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t('quality.back_to_inspection')}
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('quality.edit_quality_inspection')}</h1>
            <p className="text-muted-foreground">
              {t('quality.update_inspection_details')}
            </p>
          </div>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* ✅ INSPECTION FORM */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {t('quality.inspection_details')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="work_order">{t('quality.work_order_required')}</Label>
              <Select
                value={formData.work_order_id}
                onValueChange={(value) => handleChange('work_order_id', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('quality.select_work_order')} />
                </SelectTrigger>
                <SelectContent>
                  {workOrders.map((wo) => (
                    <SelectItem key={wo.id} value={wo.id}>
                      {wo.number} - {wo.product?.name || 'Unknown Product'}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="inspector">{t('quality.inspector_required')}</Label>
              <Input
                id="inspector"
                value={formData.inspector}
                onChange={(e) => handleChange('inspector', e.target.value)}
                placeholder={t('quality.inspector_name')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="inspection_type">{t('quality.inspection_type')}</Label>
              <Select
                value={formData.inspection_type}
                onValueChange={(value) => handleChange('inspection_type', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="incoming">{t('quality.incoming_material')}</SelectItem>
                  <SelectItem value="in_process">{t('quality.in_process')}</SelectItem>
                  <SelectItem value="final">{t('quality.final_inspection')}</SelectItem>
                  <SelectItem value="pre_shipment">{t('quality.pre_shipment')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">{t('quality.status')}</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleChange('status', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">{t('quality.pending')}</SelectItem>
                  <SelectItem value="in_progress">{t('quality.in_progress')}</SelectItem>
                  <SelectItem value="passed">{t('quality.passed')}</SelectItem>
                  <SelectItem value="failed">{t('quality.failed')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="scheduled_date">{t('quality.scheduled_date')}</Label>
                <Input
                  id="scheduled_date"
                  type="date"
                  value={formData.scheduled_date}
                  onChange={(e) => handleChange('scheduled_date', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="completed_date">{t('quality.completed_date')}</Label>
                <Input
                  id="completed_date"
                  type="date"
                  value={formData.completed_date}
                  onChange={(e) => handleChange('completed_date', e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">{t('quality.notes')}</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => handleChange('notes', e.target.value)}
                placeholder={t('quality.inspection_notes')}
                rows={4}
              />
            </div>
          </CardContent>
        </Card>

        {/* ✅ WORK ORDER CONTEXT */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              {t('quality.work_order_context')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {formData.work_order_id ? (
              (() => {
                const selectedWorkOrder = workOrders.find(wo => wo.id === formData.work_order_id)
                return selectedWorkOrder ? (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-muted-foreground">{t('quality.work_order')}</span>
                      <span className="font-medium">{selectedWorkOrder.number}</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-muted-foreground">{t('quality.product')}</span>
                      <div className="text-right">
                        <div className="font-medium">{selectedWorkOrder.product?.sku || 'N/A'}</div>
                        <div className="text-sm text-muted-foreground">
                          {selectedWorkOrder.product?.name || 'N/A'}
                        </div>
                      </div>
                    </div>

                    {selectedWorkOrder.salesContract?.customer && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-muted-foreground">{t('quality.customer')}</span>
                        <span className="font-medium">
                          {selectedWorkOrder.salesContract.customer.name}
                        </span>
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-muted-foreground">{t('quality.quantity')}</span>
                      <span className="font-medium">{selectedWorkOrder.qty || 'N/A'}</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-muted-foreground">{t('quality.wo_status')}</span>
                      <span className="capitalize font-medium">
                        {selectedWorkOrder.status?.replace('_', ' ') || t('quality.unknown')}
                      </span>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-4 text-muted-foreground">
                    {t('quality.work_order_not_found')}
                  </div>
                )
              })()
            ) : (
              <div className="text-center py-4 text-muted-foreground">
                {t('quality.select_work_order_details')}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* ✅ ATTACHMENT MANAGEMENT */}
      <AttachmentManager
        inspectionId={inspection.id}
        attachments={inspection.attachments ? JSON.parse(inspection.attachments) : []}
        photos={inspection.photos ? JSON.parse(inspection.photos) : []}
        onAttachmentsUpdate={() => {
          // Refresh to show updated attachments
          window.location.reload()
        }}
      />

      {/* ✅ ACTION BUTTONS */}
      <div className="flex items-center gap-4">
        <Button onClick={handleSave} disabled={loading}>
          {loading ? (
            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <Save className="mr-2 h-4 w-4" />
          )}
          {t('quality.save_changes')}
        </Button>
        <Button variant="outline" asChild>
          <Link href={`/quality/${inspection.id}`}>
            {t('quality.cancel')}
          </Link>
        </Button>
      </div>
    </div>
  )
}
