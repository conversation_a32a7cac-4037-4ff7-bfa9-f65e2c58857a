"use client"

import { Suspense } from "react"
import { useI18n } from "@/components/i18n-provider"
import { QualityInspectionEdit } from "./quality-inspection-edit"

interface QualityInspectionEditPageClientProps {
  inspection: any
  workOrders: any[]
}

export function QualityInspectionEditPageClient({ inspection, workOrders }: QualityInspectionEditPageClientProps) {
  const { t } = useI18n()

  return (
    <Suspense fallback={<div>{t('quality.loading_quality_inspection_editor')}</div>}>
      <QualityInspectionEdit inspection={inspection} workOrders={workOrders} />
    </Suspense>
  )
}
