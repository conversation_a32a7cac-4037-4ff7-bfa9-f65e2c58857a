"use client"

import { Suspense } from "react"
import { QualityInspectionsContent } from "./quality-inspections-content"
import { QualityAnalyticsDashboard } from "@/components/quality/quality-analytics-dashboard"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useI18n } from "@/components/i18n-provider"

interface QualityPageClientProps {
  inspections: any[]
  workOrders: any[]
  companyId: string
}

export function QualityPageClient({ inspections, workOrders, companyId }: QualityPageClientProps) {
  const { t } = useI18n()

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{t('quality.quality_control')}</h1>
        <p className="text-muted-foreground">
          {t('quality.comprehensive_quality_management')}
        </p>
      </div>

      <Tabs defaultValue="inspections" className="space-y-6">
        <TabsList>
          <TabsTrigger value="inspections">{t('quality.quality_inspections')}</TabsTrigger>
          <TabsTrigger value="analytics">{t('quality.analytics_dashboard')}</TabsTrigger>
        </TabsList>

        <TabsContent value="inspections" className="space-y-6">
          <Suspense fallback={<div>{t('quality.loading_quality_inspections')}</div>}>
            <QualityInspectionsContent
              initialInspections={inspections}
              workOrders={workOrders}
            />
          </Suspense>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Suspense fallback={<div>{t('quality.loading_quality_analytics')}</div>}>
            <QualityAnalyticsDashboard companyId={companyId} />
          </Suspense>
        </TabsContent>
      </Tabs>
    </div>
  )
}
