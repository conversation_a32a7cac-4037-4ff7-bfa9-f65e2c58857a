"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { LanguageSwitcher } from "@/components/language-switcher"
import { useI18n } from "@/components/i18n-provider"
import { FCChinaLogo } from "@/components/fc-china-logo"
import { ThemeToggle } from "@/components/theme-toggle"
import Link from "next/link"
import {
  ArrowRight,
  ArrowLeft,
  Users,
  Package,
  Package2,
  Factory,
  CheckCircle,
  Ship,
  BarChart3,
  FileText,
  Truck,
  Globe,
  DollarSign,
  PlayCircle,
  BookOpen,
  Target,
  Zap,
  Building,
  Settings,
  TrendingUp
} from "lucide-react"
import { useState } from "react"

interface TutorialStep {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  content: string[]
  expectedOutcome: string
  nextAction: string
  moduleUrl?: string
}

export default function TutorialPage() {
  const { t } = useI18n()
  const [currentStep, setCurrentStep] = useState(0)

  const tutorialSteps: TutorialStep[] = [
    {
      id: "setup",
      title: t("tutorial.steps.setup.title"),
      description: t("tutorial.steps.setup.description"),
      icon: <Settings className="h-8 w-8" />,
      content: [
        t("tutorial.steps.setup.content.1"),
        t("tutorial.steps.setup.content.2"),
        t("tutorial.steps.setup.content.3")
      ],
      expectedOutcome: t("tutorial.steps.setup.outcome"),
      nextAction: t("tutorial.steps.setup.next"),
      moduleUrl: "/dashboard"
    },
    {
      id: "customers",
      title: t("tutorial.steps.customers.title"),
      description: t("tutorial.steps.customers.description"),
      icon: <Users className="h-8 w-8" />,
      content: [
        t("tutorial.steps.customers.content.1"),
        t("tutorial.steps.customers.content.2"),
        t("tutorial.steps.customers.content.3")
      ],
      expectedOutcome: t("tutorial.steps.customers.outcome"),
      nextAction: t("tutorial.steps.customers.next"),
      moduleUrl: "/customers"
    },
    {
      id: "suppliers",
      title: t("tutorial.steps.suppliers.title"),
      description: t("tutorial.steps.suppliers.description"),
      icon: <Building className="h-8 w-8" />,
      content: [
        t("tutorial.steps.suppliers.content.1"),
        t("tutorial.steps.suppliers.content.2"),
        t("tutorial.steps.suppliers.content.3")
      ],
      expectedOutcome: t("tutorial.steps.suppliers.outcome"),
      nextAction: t("tutorial.steps.suppliers.next"),
      moduleUrl: "/suppliers"
    },
    {
      id: "products",
      title: t("tutorial.steps.products.title"),
      description: t("tutorial.steps.products.description"),
      icon: <Package className="h-8 w-8" />,
      content: [
        t("tutorial.steps.products.content.1"),
        t("tutorial.steps.products.content.2"),
        t("tutorial.steps.products.content.3")
      ],
      expectedOutcome: t("tutorial.steps.products.outcome"),
      nextAction: t("tutorial.steps.products.next"),
      moduleUrl: "/products"
    },
    {
      id: "raw-materials",
      title: t("tutorial.steps.rawMaterials.title"),
      description: t("tutorial.steps.rawMaterials.description"),
      icon: <Package2 className="h-8 w-8" />,
      content: [
        t("tutorial.steps.rawMaterials.content.1"),
        t("tutorial.steps.rawMaterials.content.2"),
        t("tutorial.steps.rawMaterials.content.3")
      ],
      expectedOutcome: t("tutorial.steps.rawMaterials.outcome"),
      nextAction: t("tutorial.steps.rawMaterials.next"),
      moduleUrl: "/raw-materials"
    },
    {
      id: "samples",
      title: t("tutorial.steps.samples.title"),
      description: t("tutorial.steps.samples.description"),
      icon: <Target className="h-8 w-8" />,
      content: [
        t("tutorial.steps.samples.content.1"),
        t("tutorial.steps.samples.content.2"),
        t("tutorial.steps.samples.content.3")
      ],
      expectedOutcome: t("tutorial.steps.samples.outcome"),
      nextAction: t("tutorial.steps.samples.next"),
      moduleUrl: "/samples"
    },
    {
      id: "contracts",
      title: t("tutorial.steps.contracts.title"),
      description: t("tutorial.steps.contracts.description"),
      icon: <FileText className="h-8 w-8" />,
      content: [
        t("tutorial.steps.contracts.content.1"),
        t("tutorial.steps.contracts.content.2"),
        t("tutorial.steps.contracts.content.3")
      ],
      expectedOutcome: t("tutorial.steps.contracts.outcome"),
      nextAction: t("tutorial.steps.contracts.next"),
      moduleUrl: "/sales-contracts"
    },
    {
      id: "purchase-contracts",
      title: t("tutorial.steps.purchaseContracts.title"),
      description: t("tutorial.steps.purchaseContracts.description"),
      icon: <FileText className="h-8 w-8" />,
      content: [
        t("tutorial.steps.purchaseContracts.content.1"),
        t("tutorial.steps.purchaseContracts.content.2"),
        t("tutorial.steps.purchaseContracts.content.3")
      ],
      expectedOutcome: t("tutorial.steps.purchaseContracts.outcome"),
      nextAction: t("tutorial.steps.purchaseContracts.next"),
      moduleUrl: "/purchase-contracts"
    },
    {
      id: "mrp-planning",
      title: t("tutorial.steps.mrpPlanning.title"),
      description: t("tutorial.steps.mrpPlanning.description"),
      icon: <TrendingUp className="h-8 w-8" />,
      content: [
        t("tutorial.steps.mrpPlanning.content.1"),
        t("tutorial.steps.mrpPlanning.content.2"),
        t("tutorial.steps.mrpPlanning.content.3")
      ],
      expectedOutcome: t("tutorial.steps.mrpPlanning.outcome"),
      nextAction: t("tutorial.steps.mrpPlanning.next"),
      moduleUrl: "/planning"
    },
    {
      id: "production",
      title: t("tutorial.steps.production.title"),
      description: t("tutorial.steps.production.description"),
      icon: <Factory className="h-8 w-8" />,
      content: [
        t("tutorial.steps.production.content.1"),
        t("tutorial.steps.production.content.2"),
        t("tutorial.steps.production.content.3")
      ],
      expectedOutcome: t("tutorial.steps.production.outcome"),
      nextAction: t("tutorial.steps.production.next"),
      moduleUrl: "/production"
    },
    {
      id: "quality",
      title: t("tutorial.steps.quality.title"),
      description: t("tutorial.steps.quality.description"),
      icon: <CheckCircle className="h-8 w-8" />,
      content: [
        t("tutorial.steps.quality.content.1"),
        t("tutorial.steps.quality.content.2"),
        t("tutorial.steps.quality.content.3")
      ],
      expectedOutcome: t("tutorial.steps.quality.outcome"),
      nextAction: t("tutorial.steps.quality.next"),
      moduleUrl: "/quality"
    },
    {
      id: "inventory",
      title: t("tutorial.steps.inventory.title"),
      description: t("tutorial.steps.inventory.description"),
      icon: <Package className="h-8 w-8" />,
      content: [
        t("tutorial.steps.inventory.content.1"),
        t("tutorial.steps.inventory.content.2"),
        t("tutorial.steps.inventory.content.3")
      ],
      expectedOutcome: t("tutorial.steps.inventory.outcome"),
      nextAction: t("tutorial.steps.inventory.next"),
      moduleUrl: "/inventory"
    },
    {
      id: "shipping",
      title: t("tutorial.steps.shipping.title"),
      description: t("tutorial.steps.shipping.description"),
      icon: <Ship className="h-8 w-8" />,
      content: [
        t("tutorial.steps.shipping.content.1"),
        t("tutorial.steps.shipping.content.2"),
        t("tutorial.steps.shipping.content.3")
      ],
      expectedOutcome: t("tutorial.steps.shipping.outcome"),
      nextAction: t("tutorial.steps.shipping.next"),
      moduleUrl: "/shipping"
    },
    {
      id: "export",
      title: t("tutorial.steps.export.title"),
      description: t("tutorial.steps.export.description"),
      icon: <Globe className="h-8 w-8" />,
      content: [
        t("tutorial.steps.export.content.1"),
        t("tutorial.steps.export.content.2"),
        t("tutorial.steps.export.content.3")
      ],
      expectedOutcome: t("tutorial.steps.export.outcome"),
      nextAction: t("tutorial.steps.export.next"),
      moduleUrl: "/export"
    },
    {
      id: "finance",
      title: t("tutorial.steps.finance.title"),
      description: t("tutorial.steps.finance.description"),
      icon: <DollarSign className="h-8 w-8" />,
      content: [
        t("tutorial.steps.finance.content.1"),
        t("tutorial.steps.finance.content.2"),
        t("tutorial.steps.finance.content.3")
      ],
      expectedOutcome: t("tutorial.steps.finance.outcome"),
      nextAction: t("tutorial.steps.finance.next"),
      moduleUrl: "/finance"
    },
    {
      id: "analytics",
      title: t("tutorial.steps.analytics.title"),
      description: t("tutorial.steps.analytics.description"),
      icon: <BarChart3 className="h-8 w-8" />,
      content: [
        t("tutorial.steps.analytics.content.1"),
        t("tutorial.steps.analytics.content.2"),
        t("tutorial.steps.analytics.content.3")
      ],
      expectedOutcome: t("tutorial.steps.analytics.outcome"),
      nextAction: t("tutorial.steps.analytics.next"),
      moduleUrl: "/reports"
    }
  ]

  const currentStepData = tutorialSteps[currentStep]

  const nextStep = () => {
    if (currentStep < tutorialSteps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const goToStep = (stepIndex: number) => {
    setCurrentStep(stepIndex)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800">
      {/* Header */}
      <header className="relative border-b border-slate-200/60 bg-background/80 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <FCChinaLogo size="md" href="/" />
          <div className="flex items-center gap-2">
            <ThemeToggle />
            <LanguageSwitcher />
            <Button asChild variant="outline">
              <Link href="/">{t("tutorial.backToHome")}</Link>
            </Button>
            <Button asChild className="bg-slate-900 hover:bg-slate-800 text-white">
              <Link href="/api/auth/login">{t("landing.login")}</Link>
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative py-16 overflow-hidden">
        <div className="absolute inset-0 pointer-events-none bg-[linear-gradient(rgba(15,23,42,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(15,23,42,0.03)_1px,transparent_1px)] dark:bg-[linear-gradient(rgba(255,255,255,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.03)_1px,transparent_1px)] bg-[size:32px_32px]" />

        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 bg-slate-100 dark:bg-slate-800 rounded-full px-4 py-2 mb-6">
              <BookOpen className="h-4 w-4 text-slate-600 dark:text-slate-400" />
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                {t("tutorial.badge")}
              </span>
            </div>

            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-slate-900 dark:text-white leading-tight mb-6">
              {t("tutorial.hero.title")}
            </h1>

            <p className="text-xl text-slate-600 dark:text-slate-300 leading-relaxed max-w-3xl mx-auto mb-8">
              {t("tutorial.hero.subtitle")}
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" onClick={() => setCurrentStep(0)} className="bg-slate-900 hover:bg-slate-800 text-white text-lg px-8 py-3">
                <PlayCircle className="mr-2 h-5 w-5" />
                {t("tutorial.startTour")}
              </Button>
              <Button size="lg" variant="outline" asChild className="text-lg px-8 py-3">
                <Link href="/api/auth/login?screen_hint=signup">
                  {t("tutorial.skipToLogin")} <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Tutorial Content */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">

            {/* Progress Bar */}
            <div className="mb-12">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-2xl font-bold text-slate-900 dark:text-white">
                  {t("tutorial.progress.title")}
                </h2>
                <span className="text-sm text-slate-600 dark:text-slate-400">
                  {currentStep + 1} {t("tutorial.progress.of")} {tutorialSteps.length}
                </span>
              </div>

              <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2 mb-6">
                <div
                  className="bg-slate-900 dark:bg-slate-100 h-2 rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${Math.round(((currentStep + 1) / tutorialSteps.length) * 100)}%` }}
                />
              </div>

              {/* Step Navigation */}
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2 mb-8">
                {tutorialSteps.map((step, index) => (
                  <button
                    key={step.id}
                    type="button"
                    onClick={() => goToStep(index)}
                    className={`p-3 rounded-lg border text-sm font-medium transition-all duration-200 ${index === currentStep
                      ? 'bg-slate-900 text-white border-slate-900 dark:bg-slate-100 dark:text-slate-900 dark:border-slate-100'
                      : index < currentStep
                        ? 'bg-slate-100 text-slate-700 border-slate-200 hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-300 dark:border-slate-700 dark:hover:bg-slate-700'
                        : 'bg-white text-slate-500 border-slate-200 hover:bg-slate-50 dark:bg-slate-900 dark:text-slate-500 dark:border-slate-700 dark:hover:bg-slate-800'
                      }`}
                  >
                    <div className="flex items-center justify-center mb-2">
                      {step.icon}
                    </div>
                    <div className="truncate">{step.title}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* Current Step Content */}
            <div className="grid lg:grid-cols-3 gap-8">

              {/* Main Content */}
              <div className="lg:col-span-2">
                <Card className="h-full">
                  <CardHeader>
                    <div className="flex items-center gap-4 mb-4">
                      <div className="p-3 bg-slate-100 dark:bg-slate-800 rounded-lg">
                        {currentStepData.icon}
                      </div>
                      <div>
                        <CardTitle className="text-2xl">{currentStepData.title}</CardTitle>
                        <CardDescription className="text-lg mt-2">
                          {currentStepData.description}
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-6">
                    {/* Step Content */}
                    <div>
                      <h3 className="text-lg font-semibold mb-4 text-slate-900 dark:text-white">
                        {t("tutorial.stepByStep")}
                      </h3>
                      <div className="space-y-4">
                        {currentStepData.content.map((item, index) => (
                          <div key={index} className="flex items-start gap-3">
                            <div className="flex-shrink-0 w-6 h-6 bg-slate-900 dark:bg-slate-100 text-white dark:text-slate-900 rounded-full flex items-center justify-center text-sm font-medium mt-0.5">
                              {index + 1}
                            </div>
                            <p className="text-slate-700 dark:text-slate-300 leading-relaxed">
                              {item}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Expected Outcome */}
                    <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                      <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2 flex items-center gap-2">
                        <Target className="h-4 w-4" />
                        {t("tutorial.expectedOutcome")}
                      </h4>
                      <p className="text-green-800 dark:text-green-200">
                        {currentStepData.expectedOutcome}
                      </p>
                    </div>

                    {/* Next Action */}
                    <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                      <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2 flex items-center gap-2">
                        <Zap className="h-4 w-4" />
                        {t("tutorial.nextAction")}
                      </h4>
                      <p className="text-blue-800 dark:text-blue-200">
                        {currentStepData.nextAction}
                      </p>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t border-slate-200 dark:border-slate-700">
                      {currentStepData.moduleUrl && (
                        <Button asChild className="bg-slate-900 hover:bg-slate-800 text-white">
                          <Link href={currentStepData.moduleUrl} target="_blank">
                            <Building className="mr-2 h-4 w-4" />
                            {t("tutorial.tryModule")}
                          </Link>
                        </Button>
                      )}

                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          onClick={prevStep}
                          disabled={currentStep === 0}
                          className="flex-1 sm:flex-none"
                        >
                          <ArrowLeft className="mr-2 h-4 w-4" />
                          {t("tutorial.previous")}
                        </Button>

                        <Button
                          onClick={nextStep}
                          disabled={currentStep === tutorialSteps.length - 1}
                          className="flex-1 sm:flex-none"
                        >
                          {t("tutorial.next")}
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Sidebar */}
              <div className="space-y-6">

                {/* Workflow Overview */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5" />
                      {t("tutorial.workflow.title")}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {tutorialSteps.map((step, index) => (
                        <div
                          key={step.id}
                          className={`flex items-center gap-3 p-2 rounded-lg transition-all duration-200 ${index === currentStep
                            ? 'bg-slate-100 dark:bg-slate-800'
                            : index < currentStep
                              ? 'opacity-60'
                              : 'opacity-40'
                            }`}
                        >
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${index === currentStep
                            ? 'bg-slate-900 text-white dark:bg-slate-100 dark:text-slate-900'
                            : index < currentStep
                              ? 'bg-green-500 text-white'
                              : 'bg-slate-200 text-slate-500 dark:bg-slate-700 dark:text-slate-400'
                            }`}>
                            {index < currentStep ? '✓' : index + 1}
                          </div>
                          <span className="text-sm font-medium">{step.title}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Quick Tips */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BookOpen className="h-5 w-5" />
                      {t("tutorial.tips.title")}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3 text-sm text-slate-600 dark:text-slate-400">
                      <p>{t("tutorial.tips.tip1")}</p>
                      <p>{t("tutorial.tips.tip2")}</p>
                      <p>{t("tutorial.tips.tip3")}</p>
                    </div>
                  </CardContent>
                </Card>

                {/* Get Started */}
                <Card className="bg-slate-900 dark:bg-slate-100 text-white dark:text-slate-900">
                  <CardHeader>
                    <CardTitle>{t("tutorial.getStarted.title")}</CardTitle>
                    <CardDescription className="text-slate-300 dark:text-slate-600">
                      {t("tutorial.getStarted.description")}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button asChild variant="secondary" className="w-full">
                      <Link href="/api/auth/login?screen_hint=signup">
                        {t("tutorial.getStarted.button")}
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative border-t border-slate-200 dark:border-slate-800 bg-slate-50 dark:bg-slate-900 py-12">
        <div className="absolute inset-0 pointer-events-none bg-[linear-gradient(rgba(15,23,42,0.06)_1px,transparent_1px),linear-gradient(90deg,rgba(15,23,42,0.06)_1px,transparent_1px)] dark:bg-[linear-gradient(rgba(255,255,255,0.06)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.06)_1px,transparent_1px)] bg-[size:32px_32px]" />
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <FCChinaLogo size="sm" href="/" />
            </div>
            <p className="text-sm text-slate-600 dark:text-slate-400">
              {t("landing.footer.copyright")}
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
