"use client"

import { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"

import { useI18n } from "@/components/i18n-provider"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { ArrowLeft, Save, X } from "lucide-react"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

// Form validation schema
const formSchema = z.object({
  number: z.string().min(1, "Invoice number is required"),
  supplier_id: z.string().min(1, "Supplier is required"),
  purchase_contract_id: z.string().optional(),
  amount: z.string().min(1, "Amount is required").refine((val) => !isNaN(parseFloat(val)) && parseFloat(val) > 0, {
    message: "Amount must be a valid positive number"
  }),
  paid: z.string().refine((val) => !isNaN(parseFloat(val)) && parseFloat(val) >= 0, {
    message: "Paid amount must be a valid non-negative number"
  }).optional().default("0"),
  currency: z.string().min(1, "Currency is required"),
  date: z.string().min(1, "Invoice date is required"),
  due_date: z.string().optional(),
  payment_terms: z.string().min(1, "Payment terms are required"),
  status: z.enum(["draft", "approved", "paid", "overdue", "cancelled", "deposit_paid", "partial_paid"]),
  notes: z.string().optional(),
})

interface APInvoiceEditProps {
  invoice: {
    id: string
    number: string
    supplier_id: string
    purchase_contract_id?: string
    amount: string
    paid: string
    currency: string
    status: string
    date: string
    due_date: string
    payment_terms: string
    notes?: string
    supplier?: {
      id: string
      name: string
    }
    purchaseContract?: {
      id: string
      number: string
      title: string
    }
  }
  suppliers: Array<{
    id: string
    name: string
    email: string
  }>
  purchaseContracts: Array<{
    id: string
    number: string
    title: string
    supplier: {
      id: string
      name: string
    }
  }>
  companyId: string
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function APInvoiceEditClient({ invoice, suppliers, purchaseContracts, companyId }: APInvoiceEditProps) {
  const { t } = useI18n()
  const { toast } = useSafeToast()
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      number: invoice.number,
      supplier_id: invoice.supplier_id,
      purchase_contract_id: invoice.purchase_contract_id || "none",
      amount: invoice.amount,
      paid: invoice.paid || "0",
      currency: invoice.currency || "USD",
      date: invoice.date,
      due_date: invoice.due_date || "",
      payment_terms: invoice.payment_terms || "TT",
      status: invoice.status as any,
      notes: invoice.notes || "",
    },
  })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true)
    try {
      const response = await fetch(`/api/finance/ap/${invoice.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...values,
          purchase_contract_id: values.purchase_contract_id === "none" ? null : values.purchase_contract_id,
          due_date: values.due_date || null,
          notes: values.notes || null,
        }),
      })

      if (response.ok) {
        toast({
          title: "Success",
          description: "AP invoice updated successfully!",
          variant: "default"
        })
        router.push(`/finance/ap/${invoice.id}`)
        router.refresh()
      } else {
        const errorData = await response.json()
        toast({
          title: "Error",
          description: errorData.message || "Failed to update AP invoice",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error updating AP invoice:", error)
      toast({
        title: "Error",
        description: "Failed to update AP invoice",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Filter purchase contracts by selected supplier
  const selectedSupplierId = form.watch("supplier_id")
  const filteredContracts = purchaseContracts.filter(contract =>
    contract.supplier.id === selectedSupplierId
  )

  return (
    <div className="space-y-6">
      {/* ✅ PROFESSIONAL HEADER WITH BREADCRUMB */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/finance/ap/${invoice.id}`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t("finance.ap.backToInvoice")}
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t("finance.ap.editTitle")}</h1>
            <p className="text-muted-foreground">
              {t("finance.ap.editDescription")}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" asChild>
            <Link href={`/finance/ap/${invoice.id}`}>
              <X className="mr-2 h-4 w-4" />
              {t("finance.ap.cancel")}
            </Link>
          </Button>
          <Button
            onClick={form.handleSubmit(onSubmit)}
            disabled={isSubmitting}
          >
            <Save className="mr-2 h-4 w-4" />
            {isSubmitting ? t("finance.ap.saving") : t("finance.ap.saveChanges")}
          </Button>
        </div>
      </div>

      {/* ✅ EDIT FORM */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Invoice Information */}
            <Card>
              <CardHeader>
                <CardTitle>{t("finance.ap.invoiceInformation")}</CardTitle>
                <CardDescription>
                  {t("finance.ap.basicInvoiceDetails")}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("finance.ap.invoiceNumber")}</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="BILL-2024-001" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("finance.ap.status")}</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t("finance.ap.selectStatus")} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="draft">{t("finance.status.draft")}</SelectItem>
                          <SelectItem value="approved">{t("finance.status.approved")}</SelectItem>
                          <SelectItem value="deposit_paid">{t("finance.status.depositPaid")}</SelectItem>
                          <SelectItem value="partial_paid">{t("finance.status.partialPaid")}</SelectItem>
                          <SelectItem value="paid">{t("finance.status.paid")}</SelectItem>
                          <SelectItem value="overdue">{t("finance.status.overdue")}</SelectItem>
                          <SelectItem value="cancelled">{t("finance.status.cancelled")}</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("finance.ap.invoiceDate")}</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="due_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("finance.ap.dueDate")}</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="payment_terms"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("finance.ap.paymentTerms")}</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t("finance.ap.selectPaymentTerms")} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="TT">TT (Telegraphic Transfer)</SelectItem>
                          <SelectItem value="DP">DP (Documents against Payment)</SelectItem>
                          <SelectItem value="LC">LC (Letter of Credit)</SelectItem>
                          <SelectItem value="deposit">Deposit</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Financial Information */}
            <Card>
              <CardHeader>
                <CardTitle>{t("finance.ap.financialInformation")}</CardTitle>
                <CardDescription>
                  {t("finance.ap.invoiceAmountsAndCurrency")}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("finance.ap.amount")}</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            {...field}
                            placeholder="0.00"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="paid"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("finance.ap.paid")}</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            {...field}
                            placeholder="0.00"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="currency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("finance.ap.currency")}</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t("finance.ap.selectCurrency")} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="USD">USD - US Dollar</SelectItem>
                          <SelectItem value="EUR">EUR - Euro</SelectItem>
                          <SelectItem value="CNY">CNY - Chinese Yuan</SelectItem>
                          <SelectItem value="GBP">GBP - British Pound</SelectItem>
                          <SelectItem value="JPY">JPY - Japanese Yen</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </div>

          {/* Supplier and Contract Information */}
          <Card>
            <CardHeader>
              <CardTitle>{t("finance.ap.supplierAndContract")}</CardTitle>
              <CardDescription>
                {t("finance.ap.selectSupplierAndContract")}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="supplier_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("finance.ap.supplier")}</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t("finance.ap.selectSupplier")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {suppliers.map((supplier) => (
                          <SelectItem key={supplier.id} value={supplier.id}>
                            {supplier.name} ({supplier.email})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="purchase_contract_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("finance.ap.purchaseContractOptional")}</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t("finance.ap.selectPurchaseContract")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="none">{t("finance.ap.noContract")}</SelectItem>
                        {filteredContracts.map((contract) => (
                          <SelectItem key={contract.id} value={contract.id}>
                            {contract.number} - {contract.title}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Contract Context Info */}
              {(() => {
                const selectedContractId = form.watch("purchase_contract_id")
                const selectedContract = filteredContracts.find(c => c.id === selectedContractId)

                if (selectedContract && selectedContractId !== "none") {
                  return (
                    <div className="mt-3 p-3 bg-muted/50 rounded-md border">
                      <div className="text-xs font-medium text-muted-foreground mb-1">
                        {t("finance.ap.selectedContract")}
                      </div>
                      <div className="text-sm">
                        <span className="font-mono">{selectedContract.number}</span> - {selectedContract.title}
                      </div>
                      {selectedContract.supplier && (
                        <div className="text-xs text-muted-foreground mt-1">
                          {t("finance.ap.supplier")}: {selectedContract.supplier.name}
                        </div>
                      )}
                    </div>
                  )
                }
                return null
              })()}
            </CardContent>
          </Card>



          {/* Notes */}
          <Card>
            <CardHeader>
              <CardTitle>{t("finance.ap.additionalInformation")}</CardTitle>
              <CardDescription>
                {t("finance.ap.optionalNotes")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("finance.ap.notes")}</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder={t("finance.ap.enterNotes")}
                        rows={4}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>
        </form>
      </Form>
    </div>
  )
}
