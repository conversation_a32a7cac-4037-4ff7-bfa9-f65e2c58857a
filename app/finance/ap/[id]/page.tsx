import { notFound, redirect } from "next/navigation"
import { getTenantContext } from "@/lib/tenant-utils"
import { db } from "@/lib/db"
import { apInvoices, products, rawMaterials } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { AppShell } from "@/components/app-shell"
import { APInvoiceViewClient } from "./ap-invoice-view-client"

interface APInvoiceViewPageProps {
  params: Promise<{ id: string }>
}

export default async function APInvoiceViewPage({ params }: APInvoiceViewPageProps) {
  const { id } = await params
  const context = await getTenantContext()

  if (!context) {
    redirect('/api/auth/login')
  }

  // 🛡️ SECURE: Fetch the AP invoice with related data and multi-tenant isolation
  const invoice = await db.query.apInvoices.findFirst({
    where: and(
      eq(apInvoices.id, id),
      eq(apInvoices.company_id, context.companyId)
    ),
    with: {
      supplier: true,
      purchaseContract: {
        with: {
          items: true // ✅ FIXED: Remove product relation since items can reference products or raw materials
        }
      }
    }
  })

  if (!invoice) {
    notFound()
  }

  // ✅ ENHANCED: Fetch products and raw materials for proper item display
  const allProducts = await db.query.products.findMany({
    where: eq(products.company_id, context.companyId),
    orderBy: (products, { asc }) => [asc(products.name)],
  })

  const allRawMaterials = await db.query.rawMaterials.findMany({
    where: eq(rawMaterials.company_id, context.companyId),
    orderBy: (rawMaterials, { asc }) => [asc(rawMaterials.name)],
  })

  return (
    <AppShell>
      <APInvoiceViewClient
        invoice={invoice}
        products={allProducts}
        rawMaterials={allRawMaterials}
        companyId={context.companyId}
      />
    </AppShell>
  )
}
