"use client"

import { useI18n } from "@/components/i18n-provider"
import { AddSupplierForm } from "../add-supplier-form"

export function AddSupplierPageClient() {
  const { t } = useI18n()

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{t("suppliers.add.title")}</h1>
        <p className="text-muted-foreground">
          {t("suppliers.add.description")}
        </p>
      </div>

      <div className="max-w-2xl">
        <AddSupplierForm />
      </div>
    </div>
  )
}
