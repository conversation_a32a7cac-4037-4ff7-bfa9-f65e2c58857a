import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { AddSupplierPageClient } from "./add-supplier-page-client"

export default async function AddSupplierPage() {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  return (
    <AppShell>
      <AddSupplierPageClient />
    </AppShell>
  )
}
