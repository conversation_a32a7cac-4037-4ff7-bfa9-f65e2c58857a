"use client"

/**
 * Manufacturing ERP - BOM Management Component
 * Professional Bill of Materials management with CRUD operations
 */

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Plus, Edit, Trash2, Package } from "lucide-react"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { useI18n } from "@/components/i18n-provider"

interface BOMItem {
  id: string
  raw_material_id: string
  qty_required: string
  unit: string
  waste_factor: string
  status: string
  rawMaterial?: {
    id: string
    name: string
    sku: string
    unit: string
    standard_cost?: string
    primarySupplier?: {
      name: string
    }
  }
}

interface BOMManagementProps {
  productId: string
  productName: string
  bomItems: BOMItem[]
}

export function BOMManagement({ productId, productName, bomItems }: BOMManagementProps) {
  const router = useRouter()
  const { t } = useI18n()
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [rawMaterials, setRawMaterials] = useState<any[]>([])
  const [editingItem, setEditingItem] = useState<BOMItem | null>(null)
  const [formData, setFormData] = useState({
    raw_material_id: "",
    qty_required: "",
    unit: "",
    waste_factor: "0.05",
  })

  // Load raw materials when dialog opens
  const loadRawMaterials = async () => {
    try {
      console.log("Fetching raw materials from API...")
      const response = await fetch("/api/raw-materials")
      console.log("API Response status:", response.status)

      if (response.ok) {
        const data = await response.json()
        console.log("Raw API response:", data)

        // Handle both array and object responses
        const materialsArray = Array.isArray(data) ? data : (data.materials || data.rawMaterials || [])
        console.log("Processed materials array:", materialsArray)
        setRawMaterials(materialsArray)
      } else {
        console.error("API response not OK:", response.status, response.statusText)
        setRawMaterials([])
      }
    } catch (error) {
      console.error("Failed to load raw materials:", error)
      setRawMaterials([]) // Set empty array on error
    }
  }

  const handleAddBOMItem = async () => {
    if (!formData.raw_material_id || !formData.qty_required || !formData.unit) {
      toast.error("Please fill in all required fields")
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch(`/api/products/${productId}/bom`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        toast.success("BOM item added successfully")
        setIsAddDialogOpen(false)
        setFormData({
          raw_material_id: "",
          qty_required: "",
          unit: "",
          waste_factor: "0.05",
        })
        router.refresh()
      } else {
        const error = await response.json()
        toast.error(error.message || "Failed to add BOM item")
      }
    } catch (error) {
      toast.error("Network error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  const handleEditBOMItem = (item: BOMItem) => {
    setEditingItem(item)
    setFormData({
      raw_material_id: item.raw_material_id,
      qty_required: item.qty_required,
      unit: item.unit,
      waste_factor: item.waste_factor,
    })
    loadRawMaterials()
    setIsEditDialogOpen(true)
  }

  const handleUpdateBOMItem = async () => {
    if (!editingItem || !formData.qty_required || !formData.unit) {
      toast.error("Please fill in all required fields")
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch(`/api/products/${productId}/bom/${editingItem.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          qty_required: formData.qty_required,
          unit: formData.unit,
          waste_factor: formData.waste_factor,
        }),
      })

      if (response.ok) {
        toast.success("BOM item updated successfully")
        setIsEditDialogOpen(false)
        setEditingItem(null)
        setFormData({
          raw_material_id: "",
          qty_required: "",
          unit: "",
          waste_factor: "0.05",
        })
        router.refresh()
      } else {
        const error = await response.json()
        toast.error(error.message || "Failed to update BOM item")
      }
    } catch (error) {
      toast.error("Network error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteBOMItem = async (bomId: string, materialName: string) => {
    if (!confirm(`Are you sure you want to remove ${materialName} from the BOM?`)) {
      return
    }

    try {
      const response = await fetch(`/api/products/${productId}/bom/${bomId}`, {
        method: "DELETE",
      })

      if (response.ok) {
        toast.success("BOM item removed successfully")
        router.refresh()
      } else {
        const error = await response.json()
        toast.error(error.message || "Failed to remove BOM item")
      }
    } catch (error) {
      toast.error("Network error occurred")
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{t("bom.title")}</CardTitle>
            <p className="text-sm text-muted-foreground mt-1">
              {t("bom.description")} {productName}
            </p>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button
                onClick={() => {
                  console.log("Loading raw materials...") // Debug log
                  loadRawMaterials()
                  setIsAddDialogOpen(true)
                }}
              >
                <Plus className="h-4 w-4 mr-2" />
                {t("bom.addMaterial")}
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{t("bom.addMaterialDialog.title")}</DialogTitle>
                <DialogDescription>
                  {t("bom.addMaterialDialog.description")} {productName}.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="material">{t("bom.form.rawMaterial")} *</Label>
                  <Select
                    value={formData.raw_material_id}
                    onValueChange={(value) => {
                      setFormData({ ...formData, raw_material_id: value })
                      // Auto-fill unit from selected material
                      const selectedMaterial = rawMaterials.find(m => m.id === value)
                      if (selectedMaterial) {
                        setFormData(prev => ({ ...prev, unit: selectedMaterial.unit }))
                      }
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t("bom.selectMaterial")} />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.isArray(rawMaterials) && rawMaterials.length > 0 ? (
                        rawMaterials.map((material) => (
                          <SelectItem key={material.id} value={material.id}>
                            {material.name} ({material.sku})
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no-materials" disabled>
                          {t("bom.noMaterialsAvailable")}
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="qty_required">{t("bom.form.qtyRequired")} *</Label>
                  <Input
                    id="qty_required"
                    type="number"
                    step="0.01"
                    placeholder="e.g., 2.5"
                    value={formData.qty_required}
                    onChange={(e) => setFormData({ ...formData, qty_required: e.target.value })}
                  />
                </div>

                <div>
                  <Label htmlFor="unit">{t("bom.form.unit")} *</Label>
                  <Input
                    id="unit"
                    placeholder="e.g., meters, kg, pieces"
                    value={formData.unit}
                    onChange={(e) => setFormData({ ...formData, unit: e.target.value })}
                  />
                </div>

                <div>
                  <Label htmlFor="waste_factor">{t("bom.form.wasteFactor")}</Label>
                  <Input
                    id="waste_factor"
                    type="number"
                    step="0.01"
                    placeholder="0.05 (5%)"
                    value={formData.waste_factor}
                    onChange={(e) => setFormData({ ...formData, waste_factor: e.target.value })}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {t("bom.form.wasteFactorDescription")}
                  </p>
                </div>

                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                    {t("common.cancel")}
                  </Button>
                  <Button onClick={handleAddBOMItem} disabled={isLoading}>
                    {isLoading ? t("bom.adding") : t("bom.addMaterial")}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          {/* ✅ ENHANCED: Edit BOM Item Dialog */}
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit BOM Item</DialogTitle>
                <DialogDescription>
                  Update the material requirements for {editingItem?.rawMaterial?.name || "this material"}.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label>Raw Material</Label>
                  <div className="p-3 bg-muted rounded-md">
                    <p className="font-medium">{editingItem?.rawMaterial?.name || "Unknown"}</p>
                    <p className="text-sm text-muted-foreground">{editingItem?.rawMaterial?.sku || "-"}</p>
                  </div>
                </div>

                <div>
                  <Label htmlFor="edit_qty_required">Quantity Required *</Label>
                  <Input
                    id="edit_qty_required"
                    type="number"
                    step="0.01"
                    placeholder="e.g., 2.5"
                    value={formData.qty_required}
                    onChange={(e) => setFormData({ ...formData, qty_required: e.target.value })}
                  />
                </div>

                <div>
                  <Label htmlFor="edit_unit">Unit *</Label>
                  <Input
                    id="edit_unit"
                    placeholder="e.g., meters, kg, pieces"
                    value={formData.unit}
                    onChange={(e) => setFormData({ ...formData, unit: e.target.value })}
                  />
                </div>

                <div>
                  <Label htmlFor="edit_waste_factor">Waste Factor</Label>
                  <Input
                    id="edit_waste_factor"
                    type="number"
                    step="0.01"
                    placeholder="0.05 (5%)"
                    value={formData.waste_factor}
                    onChange={(e) => setFormData({ ...formData, waste_factor: e.target.value })}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Percentage of material waste (e.g., 0.05 = 5%)
                  </p>
                </div>

                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleUpdateBOMItem} disabled={isLoading}>
                    {isLoading ? "Updating..." : "Update Material"}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t("bom.table.material")}</TableHead>
                <TableHead>{t("bom.table.sku")}</TableHead>
                <TableHead>{t("bom.table.qtyRequired")}</TableHead>
                <TableHead>{t("bom.table.unit")}</TableHead>
                <TableHead>{t("bom.table.wasteFactor")}</TableHead>
                <TableHead>{t("bom.table.cost")}</TableHead>
                <TableHead>{t("bom.table.status")}</TableHead>
                <TableHead>{t("bom.table.actions")}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {bomItems.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                    {t("bom.empty")}
                  </TableCell>
                </TableRow>
              ) : (
                bomItems.map((item) => {
                  const qty = parseFloat(item.qty_required || "0")
                  const wasteFactor = parseFloat(item.waste_factor || "0.05")
                  const totalQty = qty * (1 + wasteFactor)
                  const unitCost = parseFloat(item.rawMaterial?.standard_cost || "0")
                  const totalCost = totalQty * unitCost

                  return (
                    <TableRow key={item.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Package className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="font-medium">{item.rawMaterial?.name || t("bom.unknown")}</p>
                            <p className="text-xs text-muted-foreground">
                              {item.rawMaterial?.primarySupplier?.name || t("bom.noSupplier")}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-sm">
                        {item.rawMaterial?.sku || "-"}
                      </TableCell>
                      <TableCell>{qty.toFixed(2)}</TableCell>
                      <TableCell>{item.unit}</TableCell>
                      <TableCell>{(wasteFactor * 100).toFixed(1)}%</TableCell>
                      <TableCell>${totalCost.toFixed(2)}</TableCell>
                      <TableCell>
                        <Badge variant={item.status === "active" ? "default" : "secondary"}>
                          {item.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditBOMItem(item)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteBOMItem(item.id, item.rawMaterial?.name || "Unknown")}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  )
                })
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}
