import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { AddProductPageClient } from "./add-product-page-client"

export default async function AddProductPage() {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  return (
    <AppShell>
      <AddProductPageClient />
    </AppShell>
  )
}
