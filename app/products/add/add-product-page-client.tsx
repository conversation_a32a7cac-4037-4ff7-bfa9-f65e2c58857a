"use client"

import { useI18n } from "@/components/i18n-provider"
import { AddProductForm } from "../add-product-form"

export function AddProductPageClient() {
  const { t } = useI18n()

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{t("products.add.title")}</h1>
        <p className="text-muted-foreground">
          {t("products.add.description")}
        </p>
      </div>

      <div className="max-w-2xl">
        <AddProductForm />
      </div>
    </div>
  )
}
