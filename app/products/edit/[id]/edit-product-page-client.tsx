"use client"

import { useI18n } from "@/components/i18n-provider"
import { EditProductForm } from "../../edit-product-form"
import type { products } from "@/lib/schema-postgres"

type Product = typeof products.$inferSelect

interface EditProductPageClientProps {
  product: Product
}

export function EditProductPageClient({ product }: EditProductPageClientProps) {
  const { t } = useI18n()

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{t("products.edit.title")}</h1>
        <p className="text-muted-foreground">
          {t("products.edit.description")}
        </p>
      </div>

      <div className="max-w-2xl">
        <EditProductForm product={product} />
      </div>
    </div>
  )
}
