import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect, notFound } from "next/navigation"
import { db } from "@/lib/db"
import { products } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { EditProductPageClient } from "./edit-product-page-client"

export default async function EditProductPage({
  params,
}: {
  params: Promise<{ id: string }>
}) {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const { id } = await params

  // Fetch the product to edit
  const product = await db.query.products.findFirst({
    where: and(
      eq(products.id, id),
      eq(products.company_id, context.companyId)
    ),
  })

  if (!product) {
    notFound()
  }

  return (
    <AppShell>
      <EditProductPageClient product={product} />
    </AppShell>
  )
}
