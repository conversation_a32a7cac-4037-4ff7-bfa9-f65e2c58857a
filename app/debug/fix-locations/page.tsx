"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function FixLocationsPage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const handleFix = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/debug/fix-raw-material-locations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      const data = await response.json()
      setResult(data)
    } catch (error) {
      console.error('Fix failed:', error)
      setResult({ error: 'Fix failed' })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle>Fix Raw Material Lot Locations</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={handleFix} disabled={loading}>
            {loading ? 'Fixing...' : 'Fix Location References'}
          </Button>
          
          {result && (
            <div className="mt-4">
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
