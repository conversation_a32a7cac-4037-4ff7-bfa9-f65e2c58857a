"use client"

import { useState, useEffect, Suspense } from "react"
import { AppShell } from "@/components/app-shell"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { useSearchParams } from "next/navigation"
import { safeJson } from "@/lib/safe-fetch"
import { useI18n } from "@/components/i18n-provider"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { ExportStats } from "@/components/export/export-stats"
import { InlineStatusEditor } from "@/components/export/inline-status-editor"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  AlertDialog<PERSON><PERSON>le,
} from "@/components/ui/alert-dialog"
import Link from "next/link"
import { Plus, Search, Globe, Eye, Edit, Trash2 } from "lucide-react"

function ExportPageContent() {
  const { t } = useI18n()
  const [declarations, setDeclarations] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const searchParams = useSearchParams()
  const statusFilter = searchParams.get('status')

  // Load declarations data
  useEffect(() => {
    async function loadDeclarations() {
      setLoading(true)
      try {
        const data = await safeJson("/api/export/declarations", [])
        setDeclarations(data)
      } catch (error) {
        console.error("Failed to load declarations:", error)
      } finally {
        setLoading(false)
      }
    }
    loadDeclarations()
  }, [])

  // Calculate statistics
  const stats = {
    total: declarations.length,
    draft: declarations.filter(d => d.status === 'draft').length,
    submitted: declarations.filter(d => d.status === 'submitted').length,
    processing: declarations.filter(d => d.status === 'processing').length,
    approved: declarations.filter(d => d.status === 'approved').length,
    cleared: declarations.filter(d => d.status === 'cleared').length,
    rejected: declarations.filter(d => d.status === 'rejected').length,
  }

  // Filter declarations based on status
  const filteredDeclarations = statusFilter
    ? declarations.filter(d => d.status === statusFilter)
    : declarations

  return (
    <AppShell>
      <div className="space-y-6">
        {/* Professional Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center">
              <Globe className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">{t("export.main.title")}</h1>
              <p className="text-muted-foreground">{t("export.main.description")}</p>
            </div>
          </div>
          <Button asChild>
            <Link href="/export/create">
              <Plus className="mr-2 h-4 w-4" />
              {t("export.main.new_declaration")}
            </Link>
          </Button>
        </div>

        {/* Statistics Dashboard */}
        <ExportStats stats={stats} />

        {/* Main Content */}
        <div className="grid gap-6">
          <DeclarationsCard
            declarations={filteredDeclarations}
            loading={loading}
            onRefresh={() => window.location.reload()}
          />
        </div>
      </div>
    </AppShell>
  )
}

interface DeclarationsCardProps {
  declarations: any[]
  loading: boolean
  onRefresh: () => void
}

function DeclarationsCard({ declarations, loading, onRefresh }: DeclarationsCardProps) {
  const { t } = useI18n()
  const { toast } = useSafeToast()
  const [searchTerm, setSearchTerm] = useState("")
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [declarationToDelete, setDeclarationToDelete] = useState<string | null>(null)

  // ✅ PROFESSIONAL ERP: Modal dialog confirmation for delete
  function handleDelete(id: string) {
    setDeclarationToDelete(id)
    setDeleteDialogOpen(true)
  }

  // ✅ PROFESSIONAL ERP: Execute delete after confirmation
  async function executeDelete() {
    if (!declarationToDelete) return

    try {
      const response = await fetch(`/api/export/declarations/${declarationToDelete}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to delete declaration")
      }

      toast({
        title: t("common.success"),
        description: t("export.delete.success"),
      })

      onRefresh()
    } catch (error) {
      console.error("Delete error:", error)
      toast({
        title: t("common.error"),
        description: error instanceof Error ? error.message : t("export.delete.error"),
        variant: "destructive",
      })
    } finally {
      setDeleteDialogOpen(false)
      setDeclarationToDelete(null)
    }
  }

  // Filter declarations based on search term
  const filteredDeclarations = declarations.filter(declaration =>
    declaration.number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    declaration.status.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder={t("export.table.search_placeholder")}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Declarations Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t("export.table.title")}</CardTitle>
          <CardDescription>
            {filteredDeclarations.length} {t("common.of")} {declarations.length} {t("export.table.description")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8 text-muted-foreground">{t("export.table.loading")}</div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("export.table.declaration_no")}</TableHead>
                    <TableHead>{t("export.table.status")}</TableHead>
                    <TableHead>{t("export.table.contract")}</TableHead>
                    <TableHead>{t("export.table.items")}</TableHead>
                    <TableHead>{t("export.table.documents")}</TableHead>
                    <TableHead>{t("export.table.created")}</TableHead>
                    <TableHead>{t("table.actions")}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredDeclarations.map((declaration) => (
                    <TableRow key={declaration.id} className="group">
                      <TableCell>
                        <Link href={`/export/${declaration.id}`} className="hover:underline font-medium">
                          {declaration.number}
                        </Link>
                      </TableCell>
                      <TableCell>
                        <InlineStatusEditor
                          declarationId={declaration.id}
                          currentStatus={declaration.status}
                          declarationNumber={declaration.number}
                          onStatusChange={() => onRefresh()}
                        />
                      </TableCell>
                      <TableCell>
                        {declaration.salesContract ? (
                          <div className="text-sm">
                            <div className="font-medium">{declaration.salesContract.number}</div>
                            <div className="text-muted-foreground">{declaration.salesContract.customer?.name}</div>
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">{t("export.manual_entry")}</span>
                        )}
                      </TableCell>
                      <TableCell>{declaration.items?.length || 0}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {declaration.documents?.length || 0} {t("export.files")}
                        </div>
                      </TableCell>
                      <TableCell className="text-muted-foreground">
                        {new Date(declaration.created_at).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/export/${declaration.id}`}>
                              <Eye className="h-4 w-4" />
                            </Link>
                          </Button>
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/export/${declaration.id}/edit`}>
                              <Edit className="h-4 w-4" />
                            </Link>
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(declaration.id)}
                            className="text-destructive hover:text-destructive"
                            disabled={declaration.status === 'cleared'}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                  {filteredDeclarations.length === 0 && !loading && (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                        {searchTerm ? t("export.table.no_match") : t("export.empty")}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* ✅ PROFESSIONAL: Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("export.delete.title")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("export.delete.description")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDeleteDialogOpen(false)}>
              {t("export.delete.cancel")}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={executeDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {t("export.delete.confirm")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

export default function ExportPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ExportPageContent />
    </Suspense>
  )
}
