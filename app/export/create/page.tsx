/**
 * Manufacturing ERP - Create Export Declaration Page
 * Professional form with breadcrumb navigation and ERP standards
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { CreateDeclarationForm } from "@/components/export/create-declaration-form"
import { CreateDeclarationPageContent } from "@/components/export/create-declaration-page-content"

export default async function CreateDeclarationPage() {
  // 🛡️ CRITICAL: Ensure proper tenant authentication
  const context = await getTenantContext()

  if (!context) {
    redirect('/api/auth/login')
  }

  return (
    <AppShell>
      <CreateDeclarationPageContent />
    </AppShell>
  )
}
