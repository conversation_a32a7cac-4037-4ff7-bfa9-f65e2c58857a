"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import Link from "next/link"
import { ArrowLeft, Edit, FileText, Package, Truck, Eye, Download } from "lucide-react"
import { useI18n } from "@/components/i18n-provider"
import { DocumentPreviewModal } from "@/components/export/document-preview-modal"
import { useSafeToast } from "@/hooks/use-safe-toast"

interface DeclarationDetailsViewProps {
  declaration: any
}

export function DeclarationDetailsView({ declaration }: DeclarationDetailsViewProps) {
  const { t } = useI18n()
  const { toast } = useSafeToast()
  const [previewDoc, setPreviewDoc] = useState<any>(null)

  // ✅ PROFESSIONAL ERP: File type icon helper
  const getFileIcon = (filename: string) => {
    const extension = filename.split('.').pop()?.toLowerCase()

    switch (extension) {
      case 'pdf':
        return <FileText className="h-4 w-4 text-red-500" />
      case 'doc':
      case 'docx':
        return <FileText className="h-4 w-4 text-blue-500" />
      case 'xls':
      case 'xlsx':
        return <FileText className="h-4 w-4 text-green-500" />
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <FileText className="h-4 w-4 text-purple-500" />
      default:
        return <FileText className="h-4 w-4 text-muted-foreground" />
    }
  }

  // ✅ PROFESSIONAL ERP: Download handler
  const handleDownload = async (doc: any) => {
    try {
      const response = await fetch(`/api/export/declarations/${declaration.id}/files/${doc.filename}`)

      if (!response.ok) {
        throw new Error('Download failed')
      }

      const blob = await response.blob()
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = doc.filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      toast({
        title: t("export.documents.download_success"),
        description: `${doc.filename} ${t("export.documents.download_success_desc")}`,
      })
    } catch (error) {
      toast({
        title: t("export.documents.download_failed"),
        description: error instanceof Error ? error.message : t("export.documents.download_failed_desc"),
        variant: "destructive",
      })
    }
  }

  return (
    <div className="space-y-6">
      {/* ✅ PROFESSIONAL HEADER WITH BREADCRUMB */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/export">
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t('export.back_to_declarations')}
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('export.declaration_details')}</h1>
            <p className="text-muted-foreground">
              {declaration.declaration_number} - {declaration.status}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" asChild>
            <Link href={`/export/${declaration.id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              {t('export.edit_declaration')}
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* ✅ DECLARATION INFORMATION */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {t('export.declaration_information')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">{t('export.declaration_number')}</span>
                <span className="font-medium">{declaration.declaration_number}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">{t('export.status')}</span>
                <Badge variant={declaration.status === 'draft' ? 'secondary' : 'default'}>
                  {declaration.status}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">{t('export.created_date')}</span>
                <span className="text-sm">{new Date(declaration.created_at).toLocaleDateString()}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">{t('export.last_updated')}</span>
                <span className="text-sm">{new Date(declaration.updated_at).toLocaleDateString()}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* ✅ SUMMARY */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              {t('export.summary')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">{t('export.total_items')}</span>
                <span className="font-medium">{declaration.items?.length || 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">{t('export.declaration_id')}</span>
                <span className="text-xs font-mono text-muted-foreground">{declaration.id}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* ✅ DECLARATION ITEMS */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            {t('export.declaration_items')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('export.product')}</TableHead>
                  <TableHead>{t('export.sku')}</TableHead>
                  <TableHead>{t('export.quantity')}</TableHead>
                  <TableHead>{t('export.hs_code')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {declaration.items && declaration.items.length > 0 ? (
                  declaration.items.map((item: any) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{item.product?.name || t("export.unknown_product")}</TableCell>
                      <TableCell>{item.product?.sku || t("common.na")}</TableCell>
                      <TableCell>{item.qty}</TableCell>
                      <TableCell>{item.hs_code}</TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center text-muted-foreground">
                      {t("export.no_items_found")}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* ✅ LINKED SHIPMENTS (READ-ONLY) */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            {t('export.linked_shipments')}
          </CardTitle>
          <CardDescription>
            {t('export.linked_shipments_description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {declaration.linkedShipments && declaration.linkedShipments.length > 0 ? (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('export.shipment_number')}</TableHead>
                    <TableHead>{t('export.customer')}</TableHead>
                    <TableHead>{t('export.status')}</TableHead>
                    <TableHead>{t('export.ship_date')}</TableHead>
                    <TableHead>{t('export.items')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {declaration.linkedShipments.map((shipment: any) => (
                    <TableRow key={shipment.id}>
                      <TableCell className="font-medium">{shipment.shipment_number}</TableCell>
                      <TableCell>{shipment.customer?.name || t("export.unknown_customer")}</TableCell>
                      <TableCell>
                        <Badge variant="default">{shipment.status}</Badge>
                      </TableCell>
                      <TableCell>{shipment.ship_date}</TableCell>
                      <TableCell>{shipment.items?.length || 0} {t("export.items_count")}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <p className="text-sm text-muted-foreground">{t('export.no_shipments_linked')}</p>
          )}
        </CardContent>
      </Card>

      {/* ✅ EXPORT DOCUMENTS (READ-ONLY) */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {t('export.documents.title')}
          </CardTitle>
          <CardDescription>
            {t('export.documents.view_description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {declaration.documents && declaration.documents.length > 0 ? (
            <div className="space-y-2">
              {declaration.documents.map((doc: any) => (
                <div key={doc.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getFileIcon(doc.filename)}
                    <div className="flex flex-col">
                      <span className="text-sm font-medium">{doc.filename}</span>
                      <span className="text-xs text-muted-foreground">
                        {doc.filetype || t("export.unknown_type")}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="sm" onClick={() => setPreviewDoc(doc)}>
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleDownload(doc)}>
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-muted-foreground">{t('export.documents.no_documents')}</p>
          )}
        </CardContent>
      </Card>

      {/* ✅ DOCUMENT PREVIEW MODAL */}
      {previewDoc && (
        <DocumentPreviewModal
          isOpen={!!previewDoc}
          onClose={() => setPreviewDoc(null)}
          filename={previewDoc.filename}
          declarationId={declaration.id}
          fileType={previewDoc.filetype || undefined}
        />
      )}
    </div>
  )
}
