"use client"

import { useState, useRef } from "react"
import type { PutBlobResult } from "@vercel/blob"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import type { declarations, documents } from "@/lib/schema-postgres"
import {
  File,
  Trash2,
  UploadCloud,
  FileText,
  FileSpreadsheet,
  Image as ImageIcon,
  Download,
  Eye,
  Paperclip
} from "lucide-react"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"
import { DocumentPreviewModal } from "@/components/export/document-preview-modal"

type DeclarationWithDocs = typeof declarations.$inferSelect & {
  documents: Array<typeof documents.$inferSelect>
}

export function DocumentManager({ declaration }: { declaration: DeclarationWithDocs }) {
  const { t } = useI18n()
  const { toast } = useSafeToast()
  const router = useRouter()
  const inputFileRef = useRef<HTMLInputElement>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [docToDelete, setDocToDelete] = useState<(typeof documents.$inferSelect) | null>(null)
  const [previewDoc, setPreviewDoc] = useState<(typeof documents.$inferSelect) | null>(null)

  // ✅ PROFESSIONAL ERP: Enhanced file upload handler following quality module patterns
  const handleUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    event.preventDefault()
    if (!inputFileRef.current?.files) {
      return
    }

    const files = Array.from(inputFileRef.current.files)
    if (files.length === 0) return

    setIsUploading(true)

    try {
      // Process each file
      for (const file of files) {
        const response = await fetch(`/api/export/declarations/${declaration.id}/documents`, {
          method: "POST",
          headers: { "x-vercel-filename": file.name },
          body: file,
        })

        if (!response.ok) {
          const error = await response.json()
          throw new Error(error.message || "Upload failed")
        }
      }

      toast({
        title: t("export.documents.upload_success"),
        description: `${files.length} ${t("export.documents.upload_success_desc")}`,
      })

      // Clear the input
      if (inputFileRef.current) {
        inputFileRef.current.value = ""
      }

      router.refresh()
    } catch (error) {
      toast({
        title: t("export.documents.upload_failed"),
        description: error instanceof Error ? error.message : t("export.documents.upload_failed_desc"),
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
    }
  }

  // ✅ PROFESSIONAL ERP: Enhanced delete handler with proper error handling
  const handleDelete = async () => {
    if (!docToDelete) return

    try {
      const response = await fetch(`/api/export/declarations/${declaration.id}/documents`, {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ documentId: docToDelete.id, url: docToDelete.url }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Delete failed")
      }

      toast({
        title: t("export.documents.delete_success"),
        description: t("export.documents.delete_success_desc"),
      })

      router.refresh()
    } catch (error) {
      toast({
        title: t("export.documents.delete_failed"),
        description: error instanceof Error ? error.message : t("export.documents.delete_failed_desc"),
        variant: "destructive",
      })
    } finally {
      setDocToDelete(null)
    }
  }

  // ✅ PROFESSIONAL ERP: File type icon helper following quality module patterns
  const getFileIcon = (filename: string) => {
    const extension = filename.split('.').pop()?.toLowerCase()

    switch (extension) {
      case 'pdf':
        return <FileText className="h-5 w-5 text-red-500" />
      case 'doc':
      case 'docx':
        return <FileText className="h-5 w-5 text-blue-500" />
      case 'xls':
      case 'xlsx':
        return <FileSpreadsheet className="h-5 w-5 text-green-500" />
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <ImageIcon className="h-5 w-5 text-purple-500" />
      default:
        return <File className="h-5 w-5 text-muted-foreground" />
    }
  }

  // ✅ PROFESSIONAL ERP: Download handler following quality module patterns
  const handleDownload = async (doc: typeof documents.$inferSelect) => {
    try {
      const response = await fetch(`/api/export/declarations/${declaration.id}/files/${doc.filename}`)

      if (!response.ok) {
        throw new Error('Download failed')
      }

      const blob = await response.blob()
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = doc.filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      toast({
        title: t("export.documents.download_success"),
        description: `${doc.filename} ${t("export.documents.download_success_desc")}`,
      })
    } catch (error) {
      toast({
        title: t("export.documents.download_failed"),
        description: error instanceof Error ? error.message : t("export.documents.download_failed_desc"),
        variant: "destructive",
      })
    }
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Paperclip className="h-5 w-5" />
            {t("export.documents.title")}
          </CardTitle>
          <CardDescription>{t("export.documents.description")}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* ✅ PROFESSIONAL ERP: Enhanced upload interface following quality module patterns */}
          <div>
            <Label htmlFor="documents">{t("export.documents.upload_label")}</Label>
            <div className="mt-2">
              <Input
                ref={inputFileRef}
                id="documents"
                type="file"
                multiple
                accept=".pdf,.doc,.docx,.xls,.xlsx,.txt,.jpg,.jpeg,.png,.gif"
                onChange={handleUpload}
                disabled={isUploading}
              />
              <p className="text-xs text-muted-foreground mt-1">
                {t("export.documents.upload_formats")}
              </p>
            </div>
          </div>

          {/* ✅ PROFESSIONAL ERP: Enhanced document list with file type icons */}
          <div className="space-y-2">
            <h4 className="font-semibold">{t("export.documents.uploaded_title")}</h4>
            {declaration.documents.length === 0 ? (
              <p className="text-sm text-muted-foreground">{t("export.documents.no_documents")}</p>
            ) : (
              <div className="space-y-2">
                {declaration.documents.map((doc) => (
                  <div key={doc.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50">
                    <div className="flex items-center gap-3">
                      {getFileIcon(doc.filename)}
                      <div className="flex flex-col">
                        <span className="text-sm font-medium">{doc.filename}</span>
                        <span className="text-xs text-muted-foreground">
                          {doc.filetype || 'Unknown type'}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="sm" onClick={() => setPreviewDoc(doc)}>
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => handleDownload(doc)}>
                        <Download className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => setDocToDelete(doc)}>
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* ✅ PROFESSIONAL ERP: Upload progress indicator */}
          {isUploading && (
            <div className="flex items-center justify-center p-4 bg-muted rounded-lg">
              <UploadCloud className="h-4 w-4 animate-pulse mr-2" />
              <span className="text-sm">{t("export.documents.uploading")}</span>
            </div>
          )}
        </CardContent>
      </Card>

      <AlertDialog open={!!docToDelete} onOpenChange={(open) => !open && setDocToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the document "{docToDelete?.filename}". This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* ✅ PROFESSIONAL ERP: Document preview modal */}
      {previewDoc && (
        <DocumentPreviewModal
          isOpen={!!previewDoc}
          onClose={() => setPreviewDoc(null)}
          filename={previewDoc.filename}
          declarationId={declaration.id}
          fileType={previewDoc.filetype || undefined}
        />
      )}
    </>
  )
}
