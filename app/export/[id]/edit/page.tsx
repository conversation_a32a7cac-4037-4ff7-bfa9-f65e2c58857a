/**
 * Manufacturing ERP - Export Declaration Edit Page
 * Professional edit form following ERP standards
 */

import { db } from "@/lib/db"
import { declarations } from "@/lib/schema-postgres"
import { shipments } from "@/lib/schema-shipping"
import { eq, and } from "drizzle-orm"
import { notFound, redirect } from "next/navigation"
import { getTenantContext } from "@/lib/tenant-utils"
import { AppShell } from "@/components/app-shell"
import { ComprehensiveDeclarationEditForm } from "@/components/export/comprehensive-declaration-edit-form"
import { EditPageTitle } from "@/components/export/edit-page-title"
import { BackButton } from "@/components/export/back-button"

interface PageProps {
  params: Promise<{ id: string }>
}

export default async function EditDeclarationPage({ params }: PageProps) {
  const { id } = await params

  // ✅ SECURITY: Validate tenant context
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  // ✅ COMPREHENSIVE: Fetch declaration with all relations for editing
  const declaration = await db.query.declarations.findFirst({
    where: and(
      eq(declarations.id, id),
      eq(declarations.company_id, context.companyId)
    ),
    with: {
      items: {
        with: {
          product: true,
        },
      },
      documents: true,
      salesContract: {
        with: {
          customer: true,
        },
      },
    },
  })

  // ✅ COMPREHENSIVE: Load linked shipments separately
  const linkedShipments = declaration ? await db.query.shipments.findMany({
    where: and(
      eq(shipments.export_declaration_id, id),
      eq(shipments.company_id, context.companyId)
    ),
    with: {
      customer: true,
      salesContract: true,
      items: {
        with: {
          product: true
        }
      }
    }
  }) : []

  if (!declaration) {
    notFound()
  }

  return (
    <AppShell>
      <div className="space-y-6">
        {/* ✅ PROFESSIONAL: Navigation breadcrumb */}
        <div className="flex items-center gap-4">
          <BackButton href="/export" />
          <EditPageTitle declarationNumber={declaration.number} />
        </div>

        {/* ✅ COMPREHENSIVE: Edit form component with all relations */}
        <ComprehensiveDeclarationEditForm
          declaration={{
            ...declaration,
            linkedShipments
          }}
        />
      </div>
    </AppShell>
  )
}
