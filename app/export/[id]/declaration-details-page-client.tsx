"use client"

import { Suspense } from "react"
import { useI18n } from "@/components/i18n-provider"
import { DeclarationDetailsView } from "./declaration-details-view"

interface DeclarationDetailsPageClientProps {
  declaration: any
}

export function DeclarationDetailsPageClient({ declaration }: DeclarationDetailsPageClientProps) {
  const { t } = useI18n()

  return (
    <Suspense fallback={<div>{t('export.loading_declaration')}</div>}>
      <DeclarationDetailsView declaration={declaration} />
    </Suspense>
  )
}
