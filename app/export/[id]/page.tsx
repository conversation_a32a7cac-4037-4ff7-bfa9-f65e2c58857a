import { Suspense } from "react"
import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect, notFound } from "next/navigation"
import { db } from "@/lib/db"
import { declarations } from "@/lib/schema-postgres"
import { shipments } from "@/lib/schema-shipping"
import { eq, and } from "drizzle-orm"
import { DeclarationDetailsPageClient } from "./declaration-details-page-client"

interface DeclarationDetailsPageProps {
  params: Promise<{ id: string }>
}

async function getDeclaration(id: string, companyId: string) {
  // ✅ COMPREHENSIVE: Load declaration with all relations
  const declaration = await db.query.declarations.findFirst({
    where: and(
      eq(declarations.id, id),
      eq(declarations.company_id, companyId)
    ),
    with: {
      items: {
        with: {
          product: true
        }
      },
      documents: true,
      salesContract: {
        with: {
          customer: true
        }
      }
    }
  })

  if (!declaration) return null

  // ✅ FIX: Load linked shipments separately (reverse relationship)
  const linkedShipments = await db.query.shipments.findMany({
    where: and(
      eq(shipments.export_declaration_id, id),
      eq(shipments.company_id, companyId)
    ),
    with: {
      customer: true,
      salesContract: true,
      items: {
        with: {
          product: true
        }
      }
    }
  })

  // ✅ COMBINE: Add shipments to declaration object
  return {
    ...declaration,
    linkedShipments
  }
}

export default async function DeclarationDetailsPage({ params }: DeclarationDetailsPageProps) {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const { id } = await params
  const declaration = await getDeclaration(id, context.companyId)

  if (!declaration) {
    notFound()
  }

  return (
    <AppShell>
      <DeclarationDetailsPageClient declaration={declaration} />
    </AppShell>
  )
}