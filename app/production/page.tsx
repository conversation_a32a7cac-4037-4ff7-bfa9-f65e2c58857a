"use client"

import { useState, useEffect, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Plus, Settings, Clock, CheckCircle, XCircle, AlertTriangle, RefreshCw, Eye, Edit, Trash2, X, ChevronDown, ChevronRight } from "lucide-react"
import { AppShell } from "@/components/app-shell"
import { useI18n } from "@/components/i18n-provider"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import Link from "next/link"
import { InlineStatusEditor } from "@/components/work-orders/inline-status-editor"
import { InlineQuantityEditor } from "@/components/work-orders/inline-quantity-editor"
import { InlineDateEditor } from "@/components/work-orders/inline-date-editor"
import { InlinePriorityEditor } from "@/components/work-orders/inline-priority-editor"
import { InlineNotesEditor } from "@/components/work-orders/inline-notes-editor"

// ✅ PROFESSIONAL ERP: COLLAPSIBLE CONTRACT GROUPING
function renderGroupedWorkOrders(
  workOrders: any[],
  salesContracts: any[],
  products: any[],
  handleDelete: (id: string) => void,
  expandedContracts: Set<string>,
  toggleContract: (contractId: string) => void,
  handleStatusChange: (workOrderId: string, newStatus: string) => void,
  handleQuantityChange: (workOrderId: string, newQuantity: string) => void,
  handleDateChange: (workOrderId: string, newDate: string | null) => void,
  handlePriorityChange: (workOrderId: string, newPriority: string) => void,
  handleNotesChange: (workOrderId: string, newNotes: string | null) => void,
  t: (key: string) => string
) {
  // Group work orders by contract
  const groupedByContract = workOrders.reduce((groups, workOrder) => {
    const contractId = workOrder.sales_contract_id || 'no-contract'
    if (!groups[contractId]) {
      groups[contractId] = []
    }
    groups[contractId].push(workOrder)
    return groups
  }, {} as Record<string, any[]>)

  const rows: JSX.Element[] = []

  Object.entries(groupedByContract).forEach(([contractId, contractWorkOrders]) => {
    const contract = salesContracts.find(c => c.id === contractId)
    const isMultiProduct = contractWorkOrders.length > 1
    const isExpanded = expandedContracts.has(contractId)

    // ✅ PROFESSIONAL ERP: ALL CONTRACTS GET CONSISTENT HEADER TREATMENT
    {
      // Contract header row for multi-product contracts
      const completedCount = contractWorkOrders.filter(wo => wo.status === 'completed').length
      const totalCount = contractWorkOrders.length
      const progressPercent = Math.round((completedCount / totalCount) * 100)

      rows.push(
        <TableRow
          key={`contract-${contractId}`}
          className="bg-slate-50 border-l-4 border-l-blue-500 cursor-pointer hover:bg-slate-100"
          onClick={() => toggleContract(contractId)}
        >
          <TableCell colSpan={7} className="font-medium">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Button variant="ghost" size="sm" className="p-0 h-auto">
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </Button>
                <Link
                  href={`/sales-contracts/${contractId}`}
                  className="text-blue-600 hover:underline font-semibold"
                  onClick={(e) => e.stopPropagation()}
                >
                  {contract?.number || "Unknown Contract"}
                </Link>
                <Badge variant="secondary">
                  {contractWorkOrders.length} work orders
                </Badge>
                <div className="text-sm text-muted-foreground">
                  {t("workorders.progress")}: {completedCount}/{totalCount} ({progressPercent}%)
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-24 bg-gray-200 rounded-full h-2">
                  <div
                    className={`bg-blue-600 h-2 rounded-full transition-all duration-300 ${progressPercent === 100 ? 'w-full' :
                      progressPercent >= 75 ? 'w-3/4' :
                        progressPercent >= 50 ? 'w-1/2' :
                          progressPercent >= 25 ? 'w-1/4' :
                            progressPercent > 0 ? 'w-1/12' : 'w-0'
                      }`}
                  />
                </div>
              </div>
            </div>
          </TableCell>
        </TableRow>
      )


    }

    // ✅ PROFESSIONAL ERP: WORK ORDER ROWS (ALWAYS NESTED UNDER CONTRACT)
    if (isExpanded) {
      contractWorkOrders.forEach((workOrder) => {
        const product = products.find(p => p.id === workOrder.product_id)

        rows.push(
          <TableRow key={workOrder.id} className="border-l-4 border-l-blue-200 bg-blue-50/30">
            <TableCell className="pl-8">
              <Link
                href={`/production/${workOrder.id}`}
                className="text-blue-600 hover:underline font-medium"
              >
                {workOrder.number}
              </Link>
            </TableCell>
            <TableCell className="text-muted-foreground text-sm">
              {/* Clean visual hierarchy - no redundant text needed */}
            </TableCell>
            <TableCell>
              <div className="space-y-1">
                <div className="font-medium">{product?.sku || "N/A"}</div>
                <div className="text-sm text-muted-foreground">{product?.name || "N/A"}</div>
              </div>
            </TableCell>
            <TableCell>
              <InlineQuantityEditor
                workOrderId={workOrder.id}
                currentQuantity={workOrder.qty || "0"}
                onQuantityChange={(newQuantity) => handleQuantityChange(workOrder.id, newQuantity)}
              />
            </TableCell>
            <TableCell>
              <InlineDateEditor
                workOrderId={workOrder.id}
                currentDate={workOrder.due_date}
                onDateChange={(newDate) => handleDateChange(workOrder.id, newDate)}
              />
            </TableCell>
            <TableCell>
              <InlinePriorityEditor
                workOrderId={workOrder.id}
                currentPriority={workOrder.priority || "normal"}
                onPriorityChange={(newPriority) => handlePriorityChange(workOrder.id, newPriority)}
              />
            </TableCell>
            <TableCell>
              <InlineNotesEditor
                workOrderId={workOrder.id}
                currentNotes={workOrder.notes}
                onNotesChange={(newNotes) => handleNotesChange(workOrder.id, newNotes)}
              />
            </TableCell>
            <TableCell>
              <div className="flex items-center gap-2">
                <Button variant="ghost" size="sm" asChild>
                  <Link href={`/production/${workOrder.id}`}>
                    <Eye className="h-4 w-4" />
                  </Link>
                </Button>
                <Button variant="ghost" size="sm" asChild>
                  <Link href={`/production/${workOrder.id}/edit`}>
                    <Edit className="h-4 w-4" />
                  </Link>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDelete(workOrder.id)}
                  className="text-destructive hover:text-destructive"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </TableCell>
            <TableCell>
              <InlineStatusEditor
                workOrderId={workOrder.id}
                currentStatus={workOrder.status || "pending"}
                onStatusChange={(newStatus) => handleStatusChange(workOrder.id, newStatus)}
                workOrderNumber={workOrder.number}
                productName={product?.name || "N/A"}
                productSku={product?.sku || "N/A"}
              />
            </TableCell>
          </TableRow>
        )
      })
    }
  })

  return rows
}

export default function WorkOrdersPage() {
  const { t } = useI18n()
  const { success: toastSuccess, error: toastError } = useSafeToast()

  // ✅ STATE MANAGEMENT
  const [workOrders, setWorkOrders] = useState<any[]>([])
  const [salesContracts, setSalesContracts] = useState<any[]>([])
  const [products, setProducts] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    in_progress: 0,
    completed: 0,
  })

  // ✅ FILTERS STATE
  const [filters, setFilters] = useState({
    search: "",
    status: "",
    sales_contract_id: "",
    product_id: "",
  })

  return (
    <AppShell>
      <div className="space-y-6">
        {/* ✅ PROFESSIONAL HEADER */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t("production.title")}</h1>
            <p className="text-muted-foreground">
              {t("production.desc")}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={() => window.location.reload()} disabled={loading}>
              <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              {t("workorders.refresh")}
            </Button>
            <Button asChild>
              <Link href="/production/create">
                <Plus className="mr-2 h-4 w-4" />
                {t("workorders.new_work_order")}
              </Link>
            </Button>
          </div>
        </div>

        <WorkOrdersContent />
      </div>
    </AppShell>
  )
}

function WorkOrdersContent() {
  const { t } = useI18n()
  const { success: toastSuccess, error: toastError } = useSafeToast()

  // ✅ STATE MANAGEMENT
  const [workOrders, setWorkOrders] = useState<any[]>([])
  const [salesContracts, setSalesContracts] = useState<any[]>([])
  const [products, setProducts] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    in_progress: 0,
    completed: 0,
    overdue: 0,
    highPriority: 0,
  })

  // ✅ FILTERS STATE
  const [filters, setFilters] = useState({
    search: "",
    status: "",
    sales_contract_id: "",
    product_id: "",
  })

  // ✅ COLLAPSIBLE CONTRACT STATE - START EXPANDED FOR PROFESSIONAL UX
  const [expandedContracts, setExpandedContracts] = useState<Set<string>>(new Set())

  // ✅ AUTO-EXPAND ALL CONTRACTS ON LOAD FOR PROFESSIONAL ERP UX
  useEffect(() => {
    if (workOrders.length > 0) {
      const contractIds = [...new Set(workOrders.map(wo => wo.sales_contract_id).filter(Boolean))]
      setExpandedContracts(new Set(contractIds))
    }
  }, [workOrders])

  // ✅ DATA LOADING WITH ENHANCED ERROR HANDLING
  const loadData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      console.log("🔄 Loading work orders data...")

      const [workOrdersRes, salesRes, productsRes] = await Promise.all([
        fetch("/api/production/work-orders"),
        fetch("/api/contracts/sales"),
        fetch("/api/products"),
      ])

      console.log("📊 API responses:", {
        workOrders: workOrdersRes.status,
        sales: salesRes.status,
        products: productsRes.status
      })

      if (!workOrdersRes.ok) {
        console.error("❌ Work orders API failed:", workOrdersRes.status, workOrdersRes.statusText)
        throw new Error(`Work orders API failed: ${workOrdersRes.status}`)
      }
      if (!salesRes.ok) {
        console.error("❌ Sales contracts API failed:", salesRes.status, salesRes.statusText)
        throw new Error(`Sales contracts API failed: ${salesRes.status}`)
      }
      if (!productsRes.ok) {
        console.error("❌ Products API failed:", productsRes.status, productsRes.statusText)
        throw new Error(`Products API failed: ${productsRes.status}`)
      }

      const [workOrdersData, salesData, productsData] = await Promise.all([
        workOrdersRes.json(),
        salesRes.json(),
        productsRes.json(),
      ])

      console.log("📦 Data loaded:", {
        workOrders: Array.isArray(workOrdersData) ? workOrdersData.length : workOrdersData?.data?.length || 0,
        sales: Array.isArray(salesData) ? salesData.length : salesData?.data?.length || 0,
        products: Array.isArray(productsData) ? productsData.length : productsData?.data?.length || 0
      })

      const workOrdersList = Array.isArray(workOrdersData) ? workOrdersData : workOrdersData.data || []
      const salesList = Array.isArray(salesData) ? salesData : salesData.data || []
      const productsList = Array.isArray(productsData) ? productsData : productsData.data || []

      setWorkOrders(workOrdersList)
      setSalesContracts(salesList)
      setProducts(productsList)

      // ✅ CALCULATE PROFESSIONAL ERP STATS
      const today = new Date()
      const overdue = workOrdersList.filter((wo: any) =>
        wo.due_date && new Date(wo.due_date) < today && wo.status !== "completed"
      ).length

      const highPriority = workOrdersList.filter((wo: any) =>
        wo.priority === "high" || wo.priority === "urgent"
      ).length

      const stats = {
        total: workOrdersList.length,
        pending: workOrdersList.filter((wo: any) => wo.status === "pending").length,
        in_progress: workOrdersList.filter((wo: any) => wo.status === "in_progress").length,
        completed: workOrdersList.filter((wo: any) => wo.status === "completed").length,
        overdue,
        highPriority,
      }
      setStats(stats)

    } catch (err) {
      console.error("Error loading work orders data:", err)
      setError("Failed to load work orders data")
      toastError("Failed to load work orders data")
    } finally {
      setLoading(false)
    }
  }, [toastError])

  useEffect(() => {
    loadData()
  }, [loadData])

  // ✅ FILTER HANDLERS
  const handleFiltersChange = useCallback((newFilters: typeof filters) => {
    setFilters(newFilters)
  }, [])

  const handleClearFilters = useCallback(() => {
    setFilters({
      search: "",
      status: "",
      sales_contract_id: "",
      product_id: "",
    })
  }, [])

  const handleRefresh = useCallback(() => {
    loadData()
  }, [loadData])

  // ✅ TOGGLE CONTRACT EXPANSION
  const toggleContract = useCallback((contractId: string) => {
    setExpandedContracts(prev => {
      const newSet = new Set(prev)
      if (newSet.has(contractId)) {
        newSet.delete(contractId)
      } else {
        newSet.add(contractId)
      }
      return newSet
    })
  }, [])

  // ✅ PROFESSIONAL ERP: COMPREHENSIVE INLINE UPDATE HANDLERS
  const updateWorkOrderAndStats = useCallback((workOrderId: string, updates: any) => {
    setWorkOrders(prev => prev.map(wo =>
      wo.id === workOrderId ? { ...wo, ...updates } : wo
    ))

    // ✅ UPDATE STATS IMMEDIATELY FOR PROFESSIONAL UX
    const updatedWorkOrders = workOrders.map(wo =>
      wo.id === workOrderId ? { ...wo, ...updates } : wo
    )

    const today = new Date()
    const overdue = updatedWorkOrders.filter((wo: any) =>
      wo.due_date && new Date(wo.due_date) < today && wo.status !== "completed"
    ).length

    const highPriority = updatedWorkOrders.filter((wo: any) =>
      wo.priority === "high" || wo.priority === "urgent"
    ).length

    const newStats = {
      total: updatedWorkOrders.length,
      pending: updatedWorkOrders.filter((wo: any) => wo.status === "pending").length,
      in_progress: updatedWorkOrders.filter((wo: any) => wo.status === "in_progress").length,
      completed: updatedWorkOrders.filter((wo: any) => wo.status === "completed").length,
      overdue,
      highPriority,
    }
    setStats(newStats)
  }, [workOrders])

  const handleStatusChange = useCallback((workOrderId: string, newStatus: string) => {
    updateWorkOrderAndStats(workOrderId, { status: newStatus })
  }, [updateWorkOrderAndStats])

  const handleQuantityChange = useCallback((workOrderId: string, newQuantity: string) => {
    updateWorkOrderAndStats(workOrderId, { qty: newQuantity })
  }, [updateWorkOrderAndStats])

  const handleDateChange = useCallback((workOrderId: string, newDate: string | null) => {
    updateWorkOrderAndStats(workOrderId, { due_date: newDate })
  }, [updateWorkOrderAndStats])

  const handlePriorityChange = useCallback((workOrderId: string, newPriority: string) => {
    updateWorkOrderAndStats(workOrderId, { priority: newPriority })
  }, [updateWorkOrderAndStats])

  const handleNotesChange = useCallback((workOrderId: string, newNotes: string | null) => {
    updateWorkOrderAndStats(workOrderId, { notes: newNotes })
  }, [updateWorkOrderAndStats])

  // ✅ DELETE HANDLER
  const handleDelete = useCallback(async (id: string) => {
    try {
      const response = await fetch(`/api/production/work-orders/${id}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        throw new Error("Failed to delete work order")
      }

      toastSuccess("Work order deleted successfully")
      await loadData()
    } catch (err) {
      console.error("Error deleting work order:", err)
      toastError("Failed to delete work order")
    }
  }, [toastSuccess, toastError, loadData])

  // ✅ FILTERED WORK ORDERS
  const filteredWorkOrders = workOrders.filter((workOrder) => {
    const matchesSearch = !filters.search ||
      workOrder.number?.toLowerCase().includes(filters.search.toLowerCase()) ||
      products.find(p => p.id === workOrder.product_id)?.name?.toLowerCase().includes(filters.search.toLowerCase())

    const matchesStatus = !filters.status || workOrder.status === filters.status
    const matchesContract = !filters.sales_contract_id || workOrder.sales_contract_id === filters.sales_contract_id
    const matchesProduct = !filters.product_id || workOrder.product_id === filters.product_id

    return matchesSearch && matchesStatus && matchesContract && matchesProduct
  })

  if (error) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <AlertTriangle className="h-8 w-8 text-destructive mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">{error}</p>
              <Button variant="outline" onClick={handleRefresh} className="mt-2">
                <RefreshCw className="mr-2 h-4 w-4" />
                {t("workorders.try_again")}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* ✅ PROFESSIONAL ERP DASHBOARD CARDS - CLICKABLE */}
      <div className="grid gap-4 md:grid-cols-6">
        <Card
          className="cursor-pointer hover:shadow-md transition-shadow"
          onClick={() => handleFiltersChange({ ...filters, status: "" })}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("workorders.total_orders")}</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">{t("workorders.all_work_orders")}</p>
          </CardContent>
        </Card>

        <Card
          className="cursor-pointer hover:shadow-md transition-shadow"
          onClick={() => handleFiltersChange({ ...filters, status: "pending" })}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("workorders.pending")}</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pending}</div>
            <p className="text-xs text-muted-foreground">{t("workorders.awaiting_start")}</p>
          </CardContent>
        </Card>

        <Card
          className="cursor-pointer hover:shadow-md transition-shadow"
          onClick={() => handleFiltersChange({ ...filters, status: "in_progress" })}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("workorders.in_progress")}</CardTitle>
            <AlertTriangle className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.in_progress}</div>
            <p className="text-xs text-muted-foreground">{t("workorders.currently_active")}</p>
          </CardContent>
        </Card>

        <Card
          className="cursor-pointer hover:shadow-md transition-shadow"
          onClick={() => handleFiltersChange({ ...filters, status: "completed" })}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("workorders.completed")}</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completed}</div>
            <p className="text-xs text-muted-foreground">{t("workorders.finished_orders")}</p>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${stats.overdue > 0 ? 'border-red-200 bg-red-50' : ''}`}
          onClick={() => {
            // Filter for overdue work orders
            const today = new Date().toISOString().split('T')[0]
            setFilters({ ...filters, search: "", status: "", sales_contract_id: "", product_id: "" })
            // Note: This would need custom filtering logic for overdue items
          }}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("workorders.overdue")}</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.overdue}</div>
            <p className="text-xs text-muted-foreground">{t("workorders.past_due_date")}</p>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${stats.highPriority > 0 ? 'border-orange-200 bg-orange-50' : ''}`}
          onClick={() => {
            // Filter for high priority work orders
            setFilters({ ...filters, search: "", status: "", sales_contract_id: "", product_id: "" })
            // Note: This would need custom filtering logic for priority items
          }}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("workorders.high_priority")}</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.highPriority}</div>
            <p className="text-xs text-muted-foreground">{t("workorders.urgent_orders")}</p>
          </CardContent>
        </Card>
      </div>

      {/* ✅ FILTERS */}
      <div className="flex items-center gap-4">
        <div className="flex-1">
          <Input
            placeholder={t("workorders.search_work_orders")}
            value={filters.search}
            onChange={(e) => handleFiltersChange({ ...filters, search: e.target.value })}
            className="max-w-sm"
          />
        </div>
        <Select value={filters.status || undefined} onValueChange={(value) => handleFiltersChange({ ...filters, status: value || "" })}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={t("workorders.all_status")} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="pending">{t("workorders.pending")}</SelectItem>
            <SelectItem value="in_progress">{t("workorders.in_progress")}</SelectItem>
            <SelectItem value="completed">{t("workorders.completed")}</SelectItem>
          </SelectContent>
        </Select>
        <Select value={filters.sales_contract_id || undefined} onValueChange={(value) => handleFiltersChange({ ...filters, sales_contract_id: value || "" })}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder={t("workorders.all_contracts")} />
          </SelectTrigger>
          <SelectContent>
            {salesContracts.map((contract) => (
              <SelectItem key={contract.id} value={contract.id}>
                {contract.number}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={filters.product_id || undefined} onValueChange={(value) => handleFiltersChange({ ...filters, product_id: value || "" })}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder={t("workorders.all_products")} />
          </SelectTrigger>
          <SelectContent>
            {products.map((product) => (
              <SelectItem key={product.id} value={product.id}>
                {product.sku} - {product.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {(filters.search || filters.status || filters.sales_contract_id || filters.product_id) && (
          <Button variant="outline" onClick={handleClearFilters}>
            <X className="mr-2 h-4 w-4" />
            Clear Filters
          </Button>
        )}
      </div>



      {/* ✅ WORK ORDERS TABLE */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold">{t("workorders.work_orders")}</h2>
            <p className="text-sm text-muted-foreground">
              {loading ? "Loading..." : t("workorders.found_work_orders").replace("{count}", filteredWorkOrders.length.toString())}
            </p>
          </div>
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t("workorders.contract_work_order")}</TableHead>
                <TableHead></TableHead>
                <TableHead>{t("workorders.product")}</TableHead>
                <TableHead>{t("workorders.quantity")}</TableHead>
                <TableHead>{t("workorders.due_date")}</TableHead>
                <TableHead>{t("workorders.priority")}</TableHead>
                <TableHead>{t("workorders.notes")}</TableHead>
                <TableHead>{t("workorders.actions")}</TableHead>
                <TableHead>{t("workorders.status")}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <RefreshCw className="h-4 w-4 animate-spin mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">{t("workorders.loading_work_orders")}</p>
                  </TableCell>
                </TableRow>
              ) : filteredWorkOrders.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <Settings className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No work orders found</p>
                  </TableCell>
                </TableRow>
              ) : (
                renderGroupedWorkOrders(
                  filteredWorkOrders,
                  salesContracts,
                  products,
                  handleDelete,
                  expandedContracts,
                  toggleContract,
                  handleStatusChange,
                  handleQuantityChange,
                  handleDateChange,
                  handlePriorityChange,
                  handleNotesChange,
                  t
                )
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  )
}
