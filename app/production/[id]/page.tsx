"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { AppShell } from "@/components/app-shell"
import { WorkOrderDetail } from "@/components/work-orders/work-order-detail"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"
import { AlertTriangle, RefreshCw } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

export default function WorkOrderDetailPage() {
  const params = useParams()
  const { error: toastError } = useSafeToast()
  const { t } = useI18n()

  const [workOrder, setWorkOrder] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const workOrderId = params.id as string

  // ✅ FETCH WORK ORDER DETAIL WITH RELATIONSHIPS
  const fetchWorkOrder = async () => {
    try {
      setLoading(true)
      setError(null)

      // 🔍 DEBUG: Check if workOrderId is valid
      if (!workOrderId) {
        throw new Error('Work order ID is missing')
      }

      console.log('Fetching work order:', workOrderId)
      const url = `/api/production/work-orders/${workOrderId}`
      console.log('Fetch URL:', url)

      const response = await fetch(url)

      if (response.ok) {
        const response_data = await response.json()
        console.log('API Response:', response_data)
        // API returns { success: true, data: workOrder }
        const workOrderData = response_data.data || response_data
        console.log('Work Order Data:', workOrderData)
        setWorkOrder(workOrderData)
      }

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Work order not found')
        }
        if (response.status === 403) {
          throw new Error('Access denied')
        }
        throw new Error('Failed to load work order details')
      }
    } catch (err) {
      console.error('Error fetching work order:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to load work order details'
      setError(errorMessage)
      toastError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (workOrderId) {
      fetchWorkOrder()
    }
  }, [workOrderId])

  const handleRefresh = () => {
    fetchWorkOrder()
  }

  if (loading) {
    return (
      <AppShell>
        <div className="space-y-6">
          <div className="h-8 bg-gray-200 rounded animate-pulse" />
          <div className="grid gap-6 md:grid-cols-2">
            <div className="h-64 bg-gray-200 rounded animate-pulse" />
            <div className="h-64 bg-gray-200 rounded animate-pulse" />
          </div>
        </div>
      </AppShell>
    )
  }

  if (error) {
    return (
      <AppShell>
        <div className="space-y-6">
          <Card>
            <CardContent className="flex items-center justify-center py-8">
              <div className="text-center">
                <AlertTriangle className="h-8 w-8 text-destructive mx-auto mb-2" />
                <p className="text-sm text-muted-foreground mb-4">{error}</p>
                <Button variant="outline" onClick={handleRefresh}>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  {t("workorders.try_again")}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </AppShell>
    )
  }

  if (!workOrder) {
    return (
      <AppShell>
        <div className="space-y-6">
          <Card>
            <CardContent className="flex items-center justify-center py-8">
              <div className="text-center">
                <AlertTriangle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">{t("workorders.work_order_not_found")}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </AppShell>
    )
  }

  return (
    <AppShell>
      <WorkOrderDetail
        workOrder={workOrder}
        onRefresh={handleRefresh}
        loading={loading}
      />
    </AppShell>
  )
}
