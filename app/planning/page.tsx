/**
 * Manufacturing ERP - MRP Planning Dashboard
 * 
 * Professional MRP planning interface with demand forecasting and material requirements
 * Implements enterprise-grade UI/UX with bilingual support and responsive design
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1A MRP Implementation
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { PlanningPageClient } from "./planning-page-client"
import { ForecastProfitabilityService } from "@/lib/services/forecast-profitability"
import { DemandForecastingService } from "@/lib/services/demand-forecasting"
import { ProcurementPlanningService } from "@/lib/services/procurement-planning"
import { SupplierLeadTimeService } from "@/lib/services/supplier-lead-time"

// ✅ PROFESSIONAL: Server component with tenant context validation
export default async function PlanningPage() {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  // ✅ PROFESSIONAL: Fetch actual MRP data from services
  const forecastingService = new DemandForecastingService()
  const procurementService = new ProcurementPlanningService()
  const leadTimeService = new SupplierLeadTimeService()
  const profitabilityService = new ForecastProfitabilityService()

  // Get MRP dashboard data
  const [
    demandForecasts,
    procurementPlans,
    supplierLeadTimes,
    purchaseRecommendations,
    profitabilityOverview
  ] = await Promise.allSettled([
    forecastingService.listDemandForecasts(context.companyId).catch(() => []),
    procurementService.listProcurementPlans(context.companyId).catch(() => []),
    leadTimeService.listSupplierLeadTimes(context.companyId).catch(() => []),
    procurementService.generatePurchaseRecommendations(context.companyId).catch(() => []),
    profitabilityService.getProfitabilityOverview(context.companyId).catch(() => ({
      totalForecasts: 0,
      totalRevenue: 0,
      totalMaterialCost: 0,
      totalProfit: 0,
      averageMargin: 0,
      profitabilityDistribution: { excellent: 0, good: 0, fair: 0, poor: 0 },
      topProfitableForecasts: [],
      lowProfitableForecasts: [],
    }))
  ])

  // Extract successful results
  const rawForecasts = demandForecasts.status === 'fulfilled' ? demandForecasts.value : []
  const plans = procurementPlans.status === 'fulfilled' ? procurementPlans.value : []
  const allLeadTimes = supplierLeadTimes.status === 'fulfilled' ? supplierLeadTimes.value : []
  const recommendations = purchaseRecommendations.status === 'fulfilled' ? purchaseRecommendations.value : []
  const profitabilityData = profitabilityOverview.status === 'fulfilled' ? profitabilityOverview.value : {
    totalForecasts: 0,
    totalRevenue: 0,
    totalMaterialCost: 0,
    totalProfit: 0,
    averageMargin: 0,
    profitabilityDistribution: { excellent: 0, good: 0, fair: 0, poor: 0 },
    topProfitableForecasts: [],
    lowProfitableForecasts: [],
  }

  // ✅ ENHANCED: Add profitability and material count to forecasts
  const forecasts = await Promise.all(
    rawForecasts.map(async (forecast) => {
      try {
        // Get profitability data for this forecast
        const profitability = await profitabilityService.calculateForecastProfitability(
          context.companyId,
          forecast.id
        )

        // Get material count from BOM explosion
        const materialRequirements = await forecastingService.explodeForecastToBOM(
          context.companyId,
          forecast.id
        )

        return {
          ...forecast,
          profitabilityMargin: profitability?.marginPercentage || 0,
          profitabilityRating: profitability?.profitabilityStatus || 'poor',
          materialCount: materialRequirements.length,
        }
      } catch (error) {
        console.error(`Error enriching forecast ${forecast.id}:`, error)
        return {
          ...forecast,
          profitabilityMargin: 0,
          profitabilityRating: 'poor' as const,
          materialCount: 0,
        }
      }
    })
  )



  // ✅ PRIORITY 1: Filter suppliers to show only those relevant to active forecasts
  const getRelevantSuppliers = () => {
    // Get materials from active forecasts via procurement plans
    const activeMaterialIds = new Set(plans.map(p => p.rawMaterialId))

    if (activeMaterialIds.size === 0) {
      // No active materials, show all suppliers for setup purposes
      return allLeadTimes
    }

    // Filter to suppliers that handle materials needed by active forecasts
    const relevantSuppliers = allLeadTimes.filter(lt =>
      // Include if supplier handles a needed material
      activeMaterialIds.has(lt.rawMaterialId) ||
      // Include generic suppliers (no specific material)
      !lt.rawMaterialId
    )

    return relevantSuppliers
  }

  const leadTimes = getRelevantSuppliers()

  // ✅ PROFESSIONAL: Calculate accurate supplier metrics from procurement plans
  const getSupplierMetrics = () => {
    // Get unique suppliers from active procurement plans
    const activeSupplierIds = new Set(
      plans
        .filter(p => p.supplierId) // Only plans with assigned suppliers
        .map(p => p.supplierId)
    )

    // Get supplier performance from lead times
    const supplierPerformance = new Map()
    leadTimes.forEach(lt => {
      if (lt.supplierId && activeSupplierIds.has(lt.supplierId)) {
        if (!supplierPerformance.has(lt.supplierId)) {
          supplierPerformance.set(lt.supplierId, {
            supplierId: lt.supplierId,
            supplierName: lt.supplierName,
            reliability: lt.reliability,
            leadTimeDays: parseInt(lt.leadTimeDays || '30'),
            materialsSupplied: 0
          })
        }
        supplierPerformance.get(lt.supplierId).materialsSupplied++
      }
    })

    return {
      totalSuppliers: activeSupplierIds.size,
      excellentSuppliers: Array.from(supplierPerformance.values())
        .filter(s => s.reliability === 'excellent').length,
      averageLeadTime: supplierPerformance.size > 0
        ? Array.from(supplierPerformance.values())
          .reduce((sum, s) => sum + s.leadTimeDays, 0) / supplierPerformance.size
        : 30
    }
  }

  const supplierMetrics = getSupplierMetrics()

  // ✅ ENHANCED KPIs: Clear, actionable business insights
  const kpis = {
    // Forecast Status
    activeForecasts: forecasts.length,
    approvedForecasts: forecasts.filter(f => f.approvalStatus === 'approved').length,

    // Procurement Status - Clear actionable categories
    totalProcurementPlans: plans.length,
    pendingApproval: plans.filter(p => p.status === 'draft' || p.status === 'pending').length,
    approvedPlans: plans.filter(p => p.status === 'approved').length,
    orderedPlans: plans.filter(p => p.status === 'ordered').length,
    receivedPlans: plans.filter(p => p.status === 'received').length,

    // Time-based Urgency (separate from approval status)
    urgentByTime: plans.filter(p => p.priority === 'urgent').length,
    highPriorityByTime: plans.filter(p => p.priority === 'high').length,

    // Critical Actions - Plans that need immediate attention
    criticalActions: plans.filter(p =>
      (p.status === 'draft' || p.status === 'pending') &&
      (p.priority === 'urgent' || p.priority === 'high')
    ).length,

    // Ready to Order - Approved plans that can be ordered
    readyToOrder: plans.filter(p => p.status === 'approved').length,

    // Overdue Plans - Plans past target date that aren't received
    overduePlans: plans.filter(p => {
      const targetDate = new Date(p.targetDate)
      const today = new Date()
      return targetDate < today && p.status !== 'received'
    }).length,

    // Financial
    totalEstimatedCost: plans.reduce((sum, p) => sum + parseFloat(p.estimatedCost || '0'), 0),
    pendingCost: plans.filter(p => p.status === 'draft' || p.status === 'pending')
      .reduce((sum, p) => sum + parseFloat(p.estimatedCost || '0'), 0),

    // Supplier Metrics
    totalSuppliers: supplierMetrics.totalSuppliers,
    excellentSuppliers: supplierMetrics.excellentSuppliers,
    averageLeadTime: supplierMetrics.averageLeadTime,
  }

  return (
    <AppShell>
      <PlanningPageClient
        forecasts={forecasts}
        plans={plans}
        kpis={kpis}
        supplierMetrics={supplierMetrics}
        profitabilityData={profitabilityData}
      />
    </AppShell>
  )
}
