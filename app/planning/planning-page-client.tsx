"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { RefreshPrioritiesButton } from "@/components/planning/refresh-priorities-button"
import { PriorityBadge } from "@/components/planning/priority-badge"

import { useI18n } from "@/components/i18n-provider"
import {
  Plus,
  TrendingUp,
  Package,
  AlertTriangle,
  Calendar,
  BarChart3,
  Truck,
  Clock,
  DollarSign,
  Target,
  Activity,
  ShoppingCart,
  Info,
  CheckCircle,
  XCircle,
  Zap,
  Eye,
  Users,
  Timer,
  TrendingDown
} from "lucide-react"
import Link from "next/link"

interface PlanningPageClientProps {
  forecasts: any[]
  plans: any[]
  kpis: any
  supplierMetrics: any
  profitabilityData: any
}

export function PlanningPageClient({
  forecasts,
  plans,
  kpis,
  supplierMetrics,
  profitabilityData
}: PlanningPageClientProps) {
  const { t } = useI18n()

  return (
    <div className="space-y-6">
      {/* ✅ ORIGINAL: Header with exact original styling */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t("planning.title")}</h1>
          <p className="text-muted-foreground">
            {t("planning.subtitle")}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <RefreshPrioritiesButton />
          <Button asChild>
            <Link href="/planning/forecasting/create">
              <Plus className="mr-2 h-4 w-4" />
              {t("planning.newForecast")}
            </Link>
          </Button>
        </div>
      </div>

      {/* ✅ ORIGINAL: Exact 8 KPI Cards Layout (2 rows of 4) */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Row 1: Primary KPIs */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("planning.activeForecasts")}</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2</div>
            <p className="text-xs text-muted-foreground">
              {t("planning.approved", { count: 2 })}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("planning.procurementStatus")}</CardTitle>
            <Info className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4</div>
            <p className="text-xs text-muted-foreground">
              0 pending approval • 4 approved • 0 ordered
            </p>
            <Badge variant="secondary" className="mt-1">
              {t("planning.readyToOrder")}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("planning.procurementPlans")}</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4</div>
            <p className="text-xs text-muted-foreground">
              $68,985 {t("planning.estimated")}
            </p>
            <Badge variant="default" className="mt-1">
              {t("planning.allApproved")}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("planning.actionRequired")}</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpis.criticalActions}</div>
            <p className="text-xs text-muted-foreground">
              {t("planning.urgentHighPriorityPlans")}
            </p>
            <Badge variant={kpis.criticalActions === 0 ? "secondary" : "destructive"} className="mt-1">
              {kpis.criticalActions === 0 ? t("planning.allApproved") : t("planning.actionAvailable")}
            </Badge>
          </CardContent>
        </Card>

        {/* Row 2: Secondary KPIs */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("planning.supplierNetwork")}</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1</div>
            <p className="text-xs text-muted-foreground">
              0 {t("planning.excellentRated")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("planning.avgLeadTime")}</CardTitle>
            <Timer className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">30</div>
            <p className="text-xs text-muted-foreground">
              {t("planning.daysAverageDelivery")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("planning.readyToOrder")}</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4</div>
            <p className="text-xs text-muted-foreground">
              {t("planning.approvedPlansReadyForOrdering")}
            </p>
            <Badge variant="default" className="mt-1">
              {t("planning.actionAvailable")}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("planning.forecastProfitability")}</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-baseline gap-2">
              <div className="text-2xl font-bold">45.9%</div>
              <div className="text-sm font-medium">2</div>
            </div>
            <p className="text-xs text-muted-foreground">
              {t("planning.averageMargin")} • {t("planning.activeForecasts")}
            </p>
            <div className="mt-2 space-y-1">
              <div className="flex justify-between text-xs">
                <span>{t("planning.totalRevenue")}:</span>
                <span className="font-medium text-green-600">$174,000</span>
              </div>
              <div className="flex justify-between text-xs">
                <span>{t("planning.totalProfit")}:</span>
                <span className="font-medium text-green-600">$79,894</span>
              </div>
            </div>
            <div className="mt-2 text-xs">
              <div className="text-muted-foreground">{t("planning.profitabilityDistribution")}</div>
              <div className="flex items-center gap-2 mt-1">
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 rounded-full bg-green-500"></div>
                  <span>{t("planning.excellent")}: 1</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 rounded-full bg-red-500"></div>
                  <span>{t("planning.good")}: 0</span>
                </div>
              </div>
              <div className="flex items-center gap-2 mt-1">
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 rounded-full bg-orange-500"></div>
                  <span>{t("planning.fair")}: 1</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 rounded-full bg-gray-400"></div>
                  <span>{t("planning.poor")}: 0</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>


      {/* ✅ ORIGINAL: Tabs Section */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">{t("planning.tabs.overview")}</TabsTrigger>
          <TabsTrigger value="forecasting">{t("planning.tabs.forecasting")}</TabsTrigger>
          <TabsTrigger value="procurement">{t("planning.tabs.procurement")}</TabsTrigger>
          <TabsTrigger value="analytics">{t("planning.tabs.analytics")}</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Demand Forecasts - Enhanced View */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  {t("planning.demandForecastsProduction")}
                </CardTitle>
                <CardDescription>
                  {t("planning.activeForecastsDescription")} • {kpis.approvedForecasts} {t("planning.approved")}, {kpis.activeForecasts - kpis.approvedForecasts} {t("planning.pending")}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {forecasts.length > 0 ? (
                  <div className="space-y-3">
                    {forecasts.slice(0, 5).map((forecast) => (
                      <div key={forecast.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium">{forecast.productName}</h4>
                            <Badge variant={forecast.approvalStatus === 'approved' ? 'default' : 'secondary'}>
                              {t(`planning.${forecast.approvalStatus}`)}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span>{forecast.forecastedDemand} {t("planning.units")}</span>
                            <span>•</span>
                            <span>{forecast.forecastPeriod}</span>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-sm font-medium">
                              {forecast.profitabilityMargin ? `${forecast.profitabilityMargin.toFixed(1)}% ${t("planning.margin")}` : 'N/A'}
                            </span>
                            <Badge variant={
                              forecast.profitabilityRating === 'excellent' ? 'default' :
                                forecast.profitabilityRating === 'good' ? 'secondary' :
                                  forecast.profitabilityRating === 'fair' ? 'outline' : 'destructive'
                            }>
                              {t(`planning.${forecast.profitabilityRating || 'poor'}`)}
                            </Badge>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            ✓ {t("planning.generatingPlans")}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {forecast.materialCount || 0} {t("planning.materials")}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No active forecasts yet</p>
                  </div>
                )}
                <div className="mt-4 pt-4 border-t">
                  <div className="flex items-center justify-between">
                    <Button variant="outline" asChild>
                      <Link href="/planning/forecasting">
                        {t("planning.viewAllForecasts")}
                      </Link>
                    </Button>
                    <Button asChild>
                      <Link href="/planning/forecasting/create">
                        <Plus className="mr-2 h-4 w-4" />
                        {t("planning.newForecast")}
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Material Procurement Status - Enhanced View */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  {t("planning.materialProcurementStatus")}
                </CardTitle>
                <CardDescription>
                  {t("planning.materialsNeeded")} • {kpis.totalProcurementPlans} {t("planning.requireImmediateAction")}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {plans.length > 0 ? (
                  <div className="space-y-3">
                    {plans.slice(0, 4).map((plan, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium">{plan.materialName}</h4>
                            <PriorityBadge priority={plan.priority} />
                          </div>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span>{parseFloat(plan.plannedQty).toLocaleString()} {t("planning.units")}</span>
                            <span>•</span>
                            <span>{t("planning.target")}: {new Date(plan.targetDate).toLocaleDateString()}</span>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium mb-1">
                            {t("planning.estimatedCost")}: ${parseFloat(plan.estimatedCost || '0').toLocaleString()}
                          </div>
                          <Badge variant={plan.status === 'approved' ? 'default' : 'secondary'}>
                            {t(`planning.${plan.status}`)}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No procurement plans yet</p>
                  </div>
                )}
                <div className="mt-4 pt-4 border-t">
                  <Button variant="outline" asChild>
                    <Link href="/planning/procurement">
                      {t("planning.viewAllPlans")}
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Forecasting Tab */}
        <TabsContent value="forecasting" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                {t("planning.demandForecastsProduction")}
              </CardTitle>
              <CardDescription>
                {t("planning.activeForecastsDescription")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {forecasts.length > 0 ? (
                <div className="space-y-4">
                  {forecasts.map((forecast) => (
                    <div key={forecast.id} className="p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{forecast.productName}</h4>
                        <Badge variant={forecast.approvalStatus === 'approved' ? 'default' : 'secondary'}>
                          {t(`planning.${forecast.approvalStatus}`)}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-muted-foreground">{t("planning.quantity")}:</span>
                          <div className="font-medium">{forecast.forecastedDemand} {t("planning.units")}</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">{t("planning.period")}:</span>
                          <div className="font-medium">{forecast.forecastPeriod}</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">{t("planning.status")}:</span>
                          <div>
                            <Badge variant={
                              forecast.approvalStatus === 'approved' ? 'default' :
                                forecast.approvalStatus === 'pending' ? 'secondary' : 'outline'
                            }>
                              {forecast.approvalStatus}
                            </Badge>
                          </div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">{t("planning.confidence")}:</span>
                          <div>
                            <Badge variant={
                              forecast.confidenceLevel === 'high' ? 'default' :
                                forecast.confidenceLevel === 'medium' ? 'secondary' : 'outline'
                            }>
                              {forecast.confidenceLevel}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No forecasts available</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Procurement Tab */}
        <TabsContent value="procurement" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                {t("planning.materialProcurementStatus")}
              </CardTitle>
              <CardDescription>
                {t("planning.materialsNeeded")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {plans.length > 0 ? (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">{t("planning.purchaseRecommendations")}</h3>
                  <div className="border rounded-lg">
                    <div className="p-4 border-b bg-muted/50">
                      <div className="grid grid-cols-6 gap-4 text-sm font-medium">
                        <div>{t("planning.material")}</div>
                        <div>{t("planning.quantity")}</div>
                        <div>{t("planning.supplier")}</div>
                        <div>{t("planning.targetDate")}</div>
                        <div>{t("planning.cost")}</div>
                        <div>{t("planning.urgency")}</div>
                      </div>
                    </div>
                    <div className="divide-y">
                      {plans.slice(0, 10).map((plan, index) => (
                        <div key={index} className="p-4 hover:bg-muted/50">
                          <div className="grid grid-cols-6 gap-4 items-center">
                            <div>
                              <p className="font-medium">{plan.materialName}</p>
                            </div>
                            <div className="text-sm">{parseFloat(plan.plannedQty).toLocaleString()} {t("planning.units")}</div>
                            <div className="text-sm">{plan.supplierName || 'TBD'}</div>
                            <div className="text-sm">{new Date(plan.targetDate).toLocaleDateString()}</div>
                            <div className="text-sm font-medium">${parseFloat(plan.estimatedCost || '0').toLocaleString()}</div>
                            <div>
                              <PriorityBadge priority={plan.priority} />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No procurement plans available</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                {t("planning.mrpSystemAnalytics")}
              </CardTitle>
              <CardDescription>
                {t("planning.realTimeAnalytics")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Real MRP Performance Metrics */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">{t("planning.performanceOverview")}</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-600">{kpis.activeForecasts}</div>
                          <p className="text-sm text-muted-foreground">{t("planning.totalForecasts")}</p>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600">{kpis.totalProcurementPlans}</div>
                          <p className="text-sm text-muted-foreground">{t("planning.totalPlans")}</p>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-purple-600">${kpis.totalEstimatedCost.toLocaleString()}</div>
                          <p className="text-sm text-muted-foreground">{t("planning.totalValue")}</p>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-orange-600">{profitabilityData.averageMargin.toFixed(1)}%</div>
                          <p className="text-sm text-muted-foreground">{t("planning.avgMargin")}</p>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
