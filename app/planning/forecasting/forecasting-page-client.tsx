"use client"

import { useState, useMemo } from "react"
import { useI18n } from "@/components/i18n-provider"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, TrendingUp, Edit, Trash2, Eye, Search, Filter, CheckCircle } from "lucide-react"
import Link from "next/link"

interface ForecastingPageClientProps {
  forecasts: any[]
  supplierList: any[]
  profitabilityMap: Map<string, any>
  deleteForecast: (forecastId: string) => Promise<void>
  approveForecast: (forecastId: string) => Promise<void>
}

export function ForecastingPageClient({
  forecasts,
  supplierList,
  profitabilityMap,
  deleteForecast,
  approveForecast
}: ForecastingPageClientProps) {
  const { t } = useI18n()

  // Filter states
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [methodFilter, setMethodFilter] = useState("all")
  const [confidenceFilter, setConfidenceFilter] = useState("all")

  // Filtered forecasts
  const filteredForecasts = useMemo(() => {
    return forecasts.filter(forecast => {
      // Search filter
      if (searchTerm && !forecast.productName.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !forecast.productSku.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false
      }

      // Status filter
      if (statusFilter !== "all" && forecast.approvalStatus !== statusFilter) {
        return false
      }

      // Method filter
      if (methodFilter !== "all" && forecast.forecastMethod !== methodFilter) {
        return false
      }

      // Confidence filter
      if (confidenceFilter !== "all" && forecast.confidenceLevel !== confidenceFilter) {
        return false
      }

      return true
    })
  }, [forecasts, searchTerm, statusFilter, methodFilter, confidenceFilter])

  return (
    <div className="space-y-6">
      {/* Page header with actions */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t("forecasting.demandForecasting")}</h1>
          <p className="text-muted-foreground">
            {t("forecasting.manageForecastsScenarios")}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" asChild>
            <Link href="/planning">
              <TrendingUp className="mr-2 h-4 w-4" />
              {t("forecasting.backToPlanning")}
            </Link>
          </Button>
          <Button asChild>
            <Link href="/planning/forecasting/create">
              <Plus className="mr-2 h-4 w-4" />
              {t("forecasting.newForecast")}
            </Link>
          </Button>
        </div>
      </div>

      {/* Filters and search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            {t("forecasting.filtersSearch")}
          </CardTitle>
          <CardDescription>
            {t("forecasting.filterSearchForecasts")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">{t("forecasting.search")}</label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t("forecasting.searchForecasts")}
                  className="pl-9"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">{t("forecasting.status")}</label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder={t("forecasting.allStatuses")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t("forecasting.allStatusesSelect")}</SelectItem>
                  <SelectItem value="draft">{t("planning.draft")}</SelectItem>
                  <SelectItem value="pending">{t("planning.pending")}</SelectItem>
                  <SelectItem value="approved">{t("planning.approved")}</SelectItem>
                  <SelectItem value="rejected">{t("planning.rejected")}</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">{t("forecasting.method")}</label>
              <Select value={methodFilter} onValueChange={setMethodFilter}>
                <SelectTrigger>
                  <SelectValue placeholder={t("forecasting.allMethods")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t("forecasting.allMethodsSelect")}</SelectItem>
                  <SelectItem value="pipeline">{t("forecasting.pipelineAnalysis")}</SelectItem>
                  <SelectItem value="manual">{t("forecasting.manualEntry")}</SelectItem>
                  <SelectItem value="historical">{t("forecasting.historicalData")}</SelectItem>
                  <SelectItem value="hybrid">{t("forecasting.hybridMethod")}</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">{t("forecasting.confidence")}</label>
              <Select value={confidenceFilter} onValueChange={setConfidenceFilter}>
                <SelectTrigger>
                  <SelectValue placeholder={t("forecasting.allLevels")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t("forecasting.allLevelsSelect")}</SelectItem>
                  <SelectItem value="low">{t("forecasting.low")}</SelectItem>
                  <SelectItem value="medium">{t("forecasting.medium")}</SelectItem>
                  <SelectItem value="high">{t("forecasting.high")}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Forecasts table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            {t("forecasting.demandForecastsCount")} ({filteredForecasts.length})
          </CardTitle>
          <CardDescription>
            {t("forecasting.allForecastsCompany")}
            {filteredForecasts.length !== forecasts.length && (
              <span className="text-sm text-muted-foreground ml-2">
                ({filteredForecasts.length} of {forecasts.length} shown)
              </span>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredForecasts.length > 0 ? (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("forecasting.product")}</TableHead>
                    <TableHead>{t("forecasting.period")}</TableHead>
                    <TableHead>{t("forecasting.demand")}</TableHead>
                    <TableHead>{t("forecasting.profitMargin")}</TableHead>
                    <TableHead>{t("forecasting.confidence")}</TableHead>
                    <TableHead>{t("forecasting.method")}</TableHead>
                    <TableHead>{t("forecasting.supplierPrefs")}</TableHead>
                    <TableHead>{t("forecasting.status")}</TableHead>
                    <TableHead>{t("forecasting.created")}</TableHead>
                    <TableHead className="text-right">{t("forecasting.actions")}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredForecasts.map((forecast) => (
                    <TableRow key={forecast.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{forecast.productName}</div>
                          <div className="text-sm text-muted-foreground">{forecast.productSku}</div>
                        </div>
                      </TableCell>
                      <TableCell>{forecast.forecastPeriod}</TableCell>
                      <TableCell>
                        <div className="font-medium">{forecast.forecastedDemand.toLocaleString()} {t("forecasting.units")}</div>
                      </TableCell>
                      <TableCell>
                        {(() => {
                          const profitability = profitabilityMap.get(forecast.id)
                          if (profitability && profitability.sellingPrice > 0) {
                            return (
                              <div className="flex items-center gap-2">
                                <span className="text-sm font-medium">
                                  {profitability.marginPercentage.toFixed(1)}%
                                </span>
                                <Badge variant={
                                  profitability.profitabilityStatus === 'excellent' ? 'default' :
                                    profitability.profitabilityStatus === 'good' ? 'secondary' :
                                      profitability.profitabilityStatus === 'fair' ? 'outline' : 'destructive'
                                }>
                                  {t(`planning.${profitability.profitabilityStatus}`)}
                                </Badge>
                              </div>
                            )
                          }
                          return (
                            <span className="text-sm text-muted-foreground">
                              {t("forecasting.noBomPrice")}
                            </span>
                          )
                        })()}
                      </TableCell>
                      <TableCell>
                        <Badge variant={
                          forecast.confidenceLevel === 'high' ? 'default' :
                            forecast.confidenceLevel === 'medium' ? 'secondary' : 'outline'
                        }>
                          {t(`forecasting.${forecast.confidenceLevel}`)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {forecast.forecastMethod}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {(() => {
                          try {
                            const prefs = forecast.supplierPreferences ? JSON.parse(forecast.supplierPreferences) : null
                            if (!prefs || !prefs.preferredSupplierId) {
                              return <span className="text-muted-foreground text-sm">{t("forecasting.auto")}</span>
                            }

                            const supplier = supplierList.find(s => s.id === prefs.preferredSupplierId)
                            const supplierName = supplier ? supplier.name : t("forecasting.unknownSupplier")

                            return (
                              <Badge variant="default" className="text-xs">
                                {supplierName}
                              </Badge>
                            )
                          } catch {
                            return <span className="text-muted-foreground text-sm">{t("forecasting.auto")}</span>
                          }
                        })()}
                      </TableCell>
                      <TableCell>
                        <Badge variant={
                          forecast.approvalStatus === 'approved' ? 'default' :
                            forecast.approvalStatus === 'pending' ? 'secondary' :
                              forecast.approvalStatus === 'rejected' ? 'destructive' : 'outline'
                        }>
                          {t(`planning.${forecast.approvalStatus}`)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {new Date(forecast.createdAt).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/planning/forecasting/${forecast.id}`}>
                              <Eye className="h-4 w-4" />
                            </Link>
                          </Button>
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/planning/forecasting/${forecast.id}/edit`}>
                              <Edit className="h-4 w-4" />
                            </Link>
                          </Button>
                          {forecast.approvalStatus === "draft" && (
                            <form action={approveForecast.bind(null, forecast.id)} className="inline">
                              <Button variant="ghost" size="sm" type="submit" className="text-green-600 hover:text-green-700">
                                <CheckCircle className="h-4 w-4" />
                              </Button>
                            </form>
                          )}
                          <form action={deleteForecast.bind(null, forecast.id)} className="inline">
                            <Button variant="ghost" size="sm" type="submit">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </form>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-12">
              <TrendingUp className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">{t("forecasting.noForecastsCreated")}</h3>
              <p className="text-muted-foreground mb-6">
                {t("forecasting.startCreatingFirst")}
              </p>
              <Button asChild>
                <Link href="/planning/forecasting/create">
                  <Plus className="mr-2 h-4 w-4" />
                  {t("forecasting.createFirstForecast")}
                </Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
