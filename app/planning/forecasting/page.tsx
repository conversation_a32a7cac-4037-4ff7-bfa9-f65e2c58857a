/**
 * Manufacturing ERP - Demand Forecasting List Page
 * 
 * Professional page for viewing and managing all demand forecasts.
 * Includes filtering, editing, and deletion capabilities.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1C MRP Implementation
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DemandForecastingService } from "@/lib/services/demand-forecasting"
import { ForecastProfitabilityService } from "@/lib/services/forecast-profitability"
import { ProfitMarginDisplay } from "@/components/planning/profit-margin-display"
import { ProcurementPlanningService } from "@/lib/services/procurement-planning"
import { db } from "@/lib/db"
import { suppliers } from "@/lib/schema-postgres"
import { eq } from "drizzle-orm"
import { Plus, TrendingUp, Edit, Trash2, Eye, Search, Filter, CheckCircle } from "lucide-react"
import Link from "next/link"
import { ForecastingPageClient } from "./forecasting-page-client"

// ✅ PROFESSIONAL: Delete forecast action
async function deleteForecast(forecastId: string) {
  "use server"

  try {
    // Get tenant context for security
    const context = await getTenantContext()
    if (!context) {
      throw new Error("Unauthorized")
    }

    // Use service directly (more efficient than HTTP call)
    const forecastingService = new DemandForecastingService()
    await forecastingService.deleteDemandForecast(context.companyId, forecastId)

    // Redirect to refresh the page
    redirect("/planning/forecasting")
  } catch (error) {
    console.error("Error deleting forecast:", error)
    throw error
  }
}

// ✅ PROFESSIONAL: Approve forecast action
async function approveForecast(forecastId: string) {
  "use server"

  try {
    // Get tenant context for security
    const context = await getTenantContext()
    if (!context) {
      throw new Error("Unauthorized")
    }

    // 1. Update forecast status to approved
    const forecastingService = new DemandForecastingService()
    await forecastingService.updateDemandForecast(
      context.companyId,
      forecastId,
      { approvalStatus: "approved" }
    )

    // 2. Trigger procurement plan generation automatically
    try {
      const procurementService = new ProcurementPlanningService()
      await procurementService.generateProcurementPlansFromForecast(
        context.companyId,
        forecastId,
        context.userId,
        {
          containerOptimization: true,
          maxLeadTime: 60, // 60 days max lead time
        }
      )
    } catch (procurementError) {
      console.error("Error generating procurement plans:", procurementError)
      // Don't fail the approval if procurement generation fails
    }

    // Redirect to refresh the page
    redirect("/planning/forecasting")
  } catch (error) {
    console.error("Error approving forecast:", error)
    throw error
  }
}

export default async function ForecastingPage() {
  // ✅ PROFESSIONAL: Tenant context validation
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  // ✅ PROFESSIONAL: Fetch demand forecasts, suppliers, and profitability data
  const forecastingService = new DemandForecastingService()
  const profitabilityService = new ForecastProfitabilityService()

  const [forecasts, supplierList] = await Promise.all([
    forecastingService.listDemandForecasts(context.companyId).catch(() => []),
    db.query.suppliers.findMany({
      where: eq(suppliers.company_id, context.companyId),
      columns: { id: true, name: true },
    }).catch(() => [])
  ])

  // Calculate profitability for all forecasts
  const forecastIds = forecasts.map(f => f.id)
  const profitabilityData = await profitabilityService.calculateMultipleForecastsProfitability(
    context.companyId,
    forecastIds
  )

  // Create a map for quick lookup
  const profitabilityMap = new Map(
    profitabilityData.map(p => [p.forecastId, p])
  )

  return (
    <AppShell>
      <ForecastingPageClient
        forecasts={forecasts}
        supplierList={supplierList}
        profitabilityMap={profitabilityMap}
        deleteForecast={deleteForecast}
        approveForecast={approveForecast}
      />
    </AppShell >
  )
}
