"use client"

import { useI18n } from "@/components/i18n-provider"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

import { ArrowLeft, Edit, Package, TrendingUp, CheckCircle } from "lucide-react"
import Link from "next/link"

interface ForecastDetailClientProps {
  forecast: any
  profitability: any
  procurementPlans: any[]
}

export function ForecastDetailClient({
  forecast,
  profitability,
  procurementPlans
}: ForecastDetailClientProps) {
  const { t } = useI18n()

  return (
    <div className="space-y-6">
      {/* Clean header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" asChild>
            <Link href="/planning/forecasting">
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t("forecasting.backToForecasts")}
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{forecast.productName}</h1>
            <p className="text-muted-foreground">
              {forecast.forecastedDemand.toLocaleString()} {t("forecasting.units")} • {forecast.forecastPeriod}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={
            forecast.approvalStatus === 'approved' ? 'default' :
              forecast.approvalStatus === 'pending' ? 'secondary' :
                forecast.approvalStatus === 'rejected' ? 'destructive' : 'outline'
          }>
            {t(`planning.${forecast.approvalStatus}`)}
          </Badge>
          <Button variant="outline" asChild>
            <Link href={`/planning/forecasting/${forecast.id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              {t("forecasting.edit")}
            </Link>
          </Button>
        </div>
      </div>

      {/* Essential information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Forecast Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              {t("forecasting.forecastSummary")}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">{t("forecasting.product")}</p>
                <p className="font-medium">{forecast.productName}</p>
                <p className="text-xs text-muted-foreground">{forecast.productSku}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">{t("forecasting.quantity")}</p>
                <p className="font-medium">{forecast.forecastedDemand.toLocaleString()} {t("forecasting.units")}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">{t("forecasting.period")}</p>
                <p className="font-medium">{forecast.forecastPeriod}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">{t("forecasting.confidence")}</p>
                <Badge variant="outline">{t(`forecasting.${forecast.confidenceLevel}`)}</Badge>
              </div>
            </div>

            {forecast.notes && (
              <div>
                <p className="text-sm text-muted-foreground">{t("forecasting.notes")}</p>
                <p className="text-sm">{forecast.notes}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Profit Margin Analysis */}
        {profitability && profitability.sellingPrice > 0 ? (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                {t("forecasting.forecastProfitability")}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">{t("forecasting.profitMargin")}</span>
                  <div className="flex items-center gap-2">
                    <span className="text-lg font-bold">
                      {profitability.marginPercentage.toFixed(1)}%
                    </span>
                    <Badge variant={
                      profitability.profitabilityStatus === 'excellent' ? 'default' :
                        profitability.profitabilityStatus === 'good' ? 'secondary' :
                          profitability.profitabilityStatus === 'fair' ? 'outline' : 'destructive'
                    }>
                      {t(`planning.${profitability.profitabilityStatus}`)}
                    </Badge>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Revenue</p>
                    <p className="font-medium">{profitability.currency} {profitability.totalRevenue.toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Material Cost</p>
                    <p className="font-medium">{profitability.currency} {profitability.totalMaterialCost.toLocaleString()}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                {t("forecasting.profitMarginAnalysis")}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-4">
                <p className="text-sm text-muted-foreground">
                  {t("forecasting.profitMarginUnavailable")}
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  {t("forecasting.needsBomPrice")}
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <div className="grid grid-cols-1 gap-6">
        {/* Status & Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              {t("forecasting.statusActions")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">{t("forecasting.currentStatus")}</p>
                <Badge variant={
                  forecast.approvalStatus === 'approved' ? 'default' :
                    forecast.approvalStatus === 'pending' ? 'secondary' :
                      forecast.approvalStatus === 'rejected' ? 'destructive' : 'outline'
                }>
                  {t(`planning.${forecast.approvalStatus}`)}
                </Badge>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">{t("forecasting.lastUpdated")}</p>
                <p className="text-sm">{new Date(forecast.updatedAt).toLocaleDateString()}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">{t("forecasting.createdBy")}</p>
                <p className="text-sm">
                  {(() => {
                    if (!forecast.createdBy) return t("common.system")
                    if (forecast.createdBy.includes('google-oauth2|') ||
                      forecast.createdBy.includes('auth0|') ||
                      forecast.createdBy.length > 50) {
                      return t("common.user")
                    }
                    return forecast.createdBy
                  })()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Procurement Plans */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              {t("forecasting.procurementPlans")}
            </CardTitle>
            <CardDescription>
              {t("forecasting.generatedPlans")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {procurementPlans.length > 0 ? (
              <div className="space-y-2">
                {procurementPlans.map((plan) => (
                  <div key={plan.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">{plan.materialName}</p>
                      <p className="text-sm text-muted-foreground">
                        {plan.requiredQuantity} {t("forecasting.units")} • {plan.targetDate}
                      </p>
                    </div>
                    <Badge variant="outline">{t(`planning.${plan.status}`)}</Badge>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6">
                <p className="text-sm text-muted-foreground">
                  {t("forecasting.noProcurementPlans")}
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  {t("forecasting.generatePlansDescription")}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
