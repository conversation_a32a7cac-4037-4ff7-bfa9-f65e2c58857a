/**
 * Manufacturing ERP - Simple Forecast Edit Page
 * 
 * Clean, focused edit interface for demand forecasts
 * 
 * <AUTHOR> ERP Developer
 * @version 2.0.0 - Simplified UX Design
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { DemandForecastingService } from "@/lib/services/demand-forecasting"
import { ForecastEditClient } from "./forecast-edit-client"
import { db } from "@/lib/db"
import { products, suppliers } from "@/lib/schema-postgres"
import { eq } from "drizzle-orm"

interface PageProps {
  params: Promise<{ id: string }>
}

export default async function EditForecastPage({ params }: PageProps) {
  // ✅ PROFESSIONAL: Tenant context validation
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const { id } = await params

  // ✅ SIMPLIFIED: Fetch forecast and form data
  const forecastingService = new DemandForecastingService()

  let forecast
  let productList = []
  let supplierList = []

  try {
    [forecast, productList, supplierList] = await Promise.all([
      forecastingService.getDemandForecastById(context.companyId, id),
      db.query.products.findMany({
        where: eq(products.company_id, context.companyId),
        columns: { id: true, name: true, sku: true },
        orderBy: (products, { asc }) => [asc(products.name)],
      }),
      db.query.suppliers.findMany({
        where: eq(suppliers.company_id, context.companyId),
        columns: { id: true, name: true },
        orderBy: (suppliers, { asc }) => [asc(suppliers.name)],
      })
    ])
  } catch (error) {
    console.error("Error fetching forecast:", error)
    redirect('/planning/forecasting')
  }

  // Convert forecast data for form
  const initialData = {
    productId: forecast.productId,
    forecastPeriod: forecast.forecastPeriod,
    forecastedDemand: forecast.forecastedDemand.toString(),
    confidenceLevel: forecast.confidenceLevel,
    forecastMethod: forecast.forecastMethod,
    seasonalityApplied: forecast.seasonalityApplied ? "true" : "false",
    trendFactorApplied: forecast.trendFactorApplied.toString(),
    notes: forecast.notes || "",
    // Extract preferred supplier from JSON
    preferredSupplierId: (() => {
      try {
        const prefs = forecast.supplierPreferences ? JSON.parse(forecast.supplierPreferences) : null
        return prefs?.preferredSupplierId || "auto"
      } catch {
        return "auto"
      }
    })(),
  }

  return (
    <AppShell>
      <ForecastEditClient
        forecast={forecast}
        initialData={initialData}
        productList={productList}
        supplierList={supplierList}
      />
    </AppShell>
  )
}
