"use client"

import { useI18n } from "@/components/i18n-provider"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { DemandForecastForm } from "@/components/planning/demand-forecast-form"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"

interface ForecastEditClientProps {
  forecast: any
  initialData: any
  productList: any[]
  supplierList: any[]
}

export function ForecastEditClient({
  forecast,
  initialData,
  productList,
  supplierList
}: ForecastEditClientProps) {
  const { t } = useI18n()

  return (
    <div className="space-y-6">
      {/* Clean header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" asChild>
          <Link href={`/planning/forecasting/${forecast.id}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t("forecasting.backToForecast")}
          </Link>
        </Button>
        <div>
          <h1 className="text-2xl font-bold">{t("forecasting.editForecast")}</h1>
          <p className="text-muted-foreground">
            {forecast.productName} • {forecast.forecastedDemand.toLocaleString()} {t("forecasting.units")}
          </p>
        </div>
      </div>

      {/* Edit form */}
      <Card>
        <CardHeader>
          <CardTitle>{t("forecasting.forecastDetails")}</CardTitle>
        </CardHeader>
        <CardContent>
          <DemandForecastForm
            initialData={initialData}
            products={productList}
            suppliers={supplierList}
            mode="edit"
          />
        </CardContent>
      </Card>
    </div>
  )
}
