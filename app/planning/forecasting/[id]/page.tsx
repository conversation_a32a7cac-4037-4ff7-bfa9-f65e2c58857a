/**
 * Manufacturing ERP - Simplified Demand Forecast Detail Page
 * 
 * Clean, intuitive interface focused on essential information
 * 
 * <AUTHOR> ERP Developer
 * @version 2.0.0 - Simplified UX Design
 */

import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { DemandForecastingService } from "@/lib/services/demand-forecasting"
import { ForecastProfitabilityService } from "@/lib/services/forecast-profitability"
import { ProfitMarginCard } from "@/components/planning/profit-margin-display"
import { ProcurementPlanningService } from "@/lib/services/procurement-planning"
import { ForecastDetailClient } from "./forecast-detail-client"

interface PageProps {
  params: Promise<{ id: string }>
}

export default async function SimplifiedForecastDetailPage({ params }: PageProps) {
  // ✅ PROFESSIONAL: Tenant context validation
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const { id } = await params

  // ✅ ENHANCED: Fetch essential data including profitability
  const forecastingService = new DemandForecastingService()
  const procurementService = new ProcurementPlanningService()
  const profitabilityService = new ForecastProfitabilityService()

  let forecast
  let procurementPlans = []
  let profitability = null

  try {
    forecast = await forecastingService.getDemandForecastById(context.companyId, id)

    // Calculate profitability for this forecast
    profitability = await profitabilityService.calculateForecastProfitability(context.companyId, id)

    // Only fetch procurement plans if forecast is approved
    if (forecast.approvalStatus === 'approved') {
      procurementPlans = await procurementService.listProcurementPlans(context.companyId, {
        demandForecastId: id
      }).catch(() => [])
    }
  } catch (error) {
    console.error("Error fetching forecast:", error)
    redirect('/planning/forecasting')
  }

  return (
    <AppShell>
      <ForecastDetailClient
        forecast={forecast}
        profitability={profitability}
        procurementPlans={procurementPlans}
      />
    </AppShell>
  )
}
