"use client";

import { useI18n } from "@/components/i18n-provider";
import { EditCustomerForm } from "../../edit-customer-form";

interface Customer {
  id: string;
  name: string;
  contact_name: string | null;
  contact_phone: string | null;
  contact_email: string | null;
  address: string | null;
  incoterm: string | null;
  payment_term: string | null;
  status: string;
  created_at: string;
}

interface EditCustomerPageClientProps {
  customer: Customer;
}

export function EditCustomerPageClient({ customer }: EditCustomerPageClientProps) {
  const { t } = useI18n();

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          {t("customers.edit.page_title")}
        </h1>
        <p className="text-muted-foreground">
          {t("customers.edit.page_description").replace("{customerName}", customer.name)}
        </p>
      </div>

      <div className="max-w-2xl">
        <EditCustomerForm customer={customer} />
      </div>
    </div>
  );
}
