"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Eye, Pencil, Trash2, Plus, Search } from "lucide-react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { AddCustomerForm } from "./add-customer-form"
import { EditCustomerForm } from "./edit-customer-form"
import { useRouter } from "next/navigation"
import { useTabNavigation } from "@/components/tab-navigation"
import { useI18n } from "@/components/i18n-provider"

// This type should match the data returned from the API /api/customers
export type CustomerFromApi = {
  id: string
  name: string
  contact_name: string | null
  contact_phone: string | null
  contact_email: string | null
  address: string | null
  tax_id: string | null
  bank: string | null
  incoterm: string | null
  payment_term: string | null
  status: string
  created_at: string
}

interface CrmClientPageProps {
  initialCustomers: CustomerFromApi[]
}

export function CrmClientPage({ initialCustomers }: CrmClientPageProps) {
  const router = useRouter()
  const { t } = useI18n()
  const { openCustomerViewTab } = useTabNavigation()
  const [searchTerm, setSearchTerm] = useState("")
  const [customerToDelete, setCustomerToDelete] = useState<CustomerFromApi | null>(null)
  const [customerToEdit, setCustomerToEdit] = useState<CustomerFromApi | null>(null)
  const [isAddCustomerOpen, setAddCustomerOpen] = useState(false)

  const handleDeleteCustomer = async () => {
    if (!customerToDelete) return

    try {
      const response = await fetch(`/api/customers/${customerToDelete.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        // Refresh the page to get updated data
        router.refresh()
        setCustomerToDelete(null)
      } else {
        console.error('Failed to delete customer')
      }
    } catch (error) {
      console.error('Error deleting customer:', error)
    }
  }

  const handleEditCustomer = (customer: CustomerFromApi) => {
    setCustomerToEdit(customer)
  }

  // Filter customers based on search term
  const filteredCustomers = initialCustomers.filter(customer => {
    const search = searchTerm.toLowerCase()
    const companyName = customer.name.toLowerCase()
    const contactName = customer.contact_name?.toLowerCase() || ""
    const contactEmail = customer.contact_email?.toLowerCase() || ""

    return companyName.includes(search) ||
      contactName.includes(search) ||
      contactEmail.includes(search)
  })

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t("customers.title")}</h1>
          <p className="text-muted-foreground">
            {t("customers.subtitle")}
          </p>
        </div>
        <Button onClick={() => setAddCustomerOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          {t("customers.add")}
        </Button>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search customers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t("customers.table.company_name")}</TableHead>
              <TableHead>{t("customers.table.contact_person")}</TableHead>
              <TableHead>{t("customers.table.phone")}</TableHead>
              <TableHead>{t("customers.table.email")}</TableHead>
              <TableHead>{t("customers.table.address")}</TableHead>
              <TableHead>{t("customers.table.incoterm")}</TableHead>
              <TableHead>{t("customers.table.payment_terms")}</TableHead>
              <TableHead>{t("customers.table.status")}</TableHead>
              <TableHead className="text-right">{t("customers.table.actions")}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCustomers.map((customer) => (
              <TableRow key={customer.id} className="hover:bg-muted/50">
                <TableCell className="font-medium">{customer.name}</TableCell>
                <TableCell>{customer.contact_name || "-"}</TableCell>
                <TableCell>{customer.contact_phone || "-"}</TableCell>
                <TableCell>{customer.contact_email || "-"}</TableCell>
                <TableCell className="max-w-[200px] truncate" title={customer.address || ""}>
                  {customer.address || "-"}
                </TableCell>
                <TableCell>{customer.incoterm || "-"}</TableCell>
                <TableCell>{customer.payment_term || "-"}</TableCell>
                <TableCell>
                  <Badge variant={customer.status === "active" ? "default" : "secondary"}>
                    {t(`status.${customer.status}` as keyof typeof t) !== `status.${customer.status}` ? t(`status.${customer.status}` as keyof typeof t) : customer.status}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex gap-2 justify-end">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => openCustomerViewTab(customer.id, customer.name)}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      {t("common.view")}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEditCustomer(customer)}
                    >
                      <Pencil className="h-4 w-4 mr-1" />
                      {t("common.edit")}
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => setCustomerToDelete(customer)}
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      {t("common.delete")}
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Add Customer Dialog */}
      <Dialog open={isAddCustomerOpen} onOpenChange={setAddCustomerOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{t("customers.add.title")}</DialogTitle>
            <DialogDescription>
              {t("customers.add.description")}
            </DialogDescription>
          </DialogHeader>
          <AddCustomerForm
            setOpen={setAddCustomerOpen}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Customer Dialog */}
      <Dialog open={!!customerToEdit} onOpenChange={() => setCustomerToEdit(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{t("customers.edit.title")}</DialogTitle>
            <DialogDescription>
              {t("customers.edit.description")}
            </DialogDescription>
          </DialogHeader>
          {customerToEdit && (
            <EditCustomerForm
              customer={customerToEdit}
              onSuccess={() => {
                setCustomerToEdit(null)
                router.refresh()
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!customerToDelete} onOpenChange={() => setCustomerToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("common.confirm_delete")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("customers.delete.confirmation", { name: customerToDelete?.name })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteCustomer}>
              {t("common.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
