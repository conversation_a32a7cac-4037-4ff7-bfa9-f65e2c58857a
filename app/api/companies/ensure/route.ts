import { NextRequest } from 'next/server'
import { getSession } from '@auth0/nextjs-auth0'
import { db, uid } from '@/lib/db'
import { companies } from '@/lib/schema-postgres'
import { eq } from 'drizzle-orm'
import { jsonOk, jsonError } from '@/lib/api-helpers'
import * as Sentry from '@sentry/nextjs'

/**
 * Ensure Company Exists API Endpoint
 * 
 * This endpoint automatically creates a company record for authenticated users
 * who don't have one yet. This is called by the frontend to ensure proper
 * multi-tenant isolation.
 */
export async function POST(req: NextRequest) {
  try {
    const session = await getSession()

    if (!session?.user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      )
    }

    const auth0User = session.user
    console.log(`🔍 Ensuring company exists for user: ${auth0User.email}`)

    // Check if company already exists
    const existingCompany = await db.query.companies.findFirst({
      where: eq(companies.auth0_user_id, auth0User.sub)
    })

    if (existingCompany) {
      console.log(`✅ Company already exists: ${existingCompany.name} (${existingCompany.id})`)
      return jsonOk({
        company: existingCompany,
        created: false,
        message: 'Company already exists'
      })
    }

    // Create new company automatically
    console.log(`🏢 Creating new company for user: ${auth0User.email}`)

    const companyId = uid('company')
    const companyName = generateCompanyName(auth0User)

    const now = new Date()
    const newCompany = {
      id: companyId,
      auth0_user_id: auth0User.sub,
      name: companyName,
      display_name: companyName,
      email: auth0User.email || '',
      onboarding_completed: 'true', // 🛡️ CRITICAL: Set to true to allow API access
      onboarding_step: 'completed',
      status: 'active' as const,
      created_at: now,
      updated_at: now
    }

    await db.insert(companies).values(newCompany)

    console.log(`✅ Created company: ${companyName} (${companyId})`)

    // Return the created company
    const createdCompany = await db.query.companies.findFirst({
      where: eq(companies.id, companyId)
    })

    return jsonOk({
      company: createdCompany,
      created: true,
      message: 'Company created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('❌ Error ensuring company exists:', error)

    // Enhanced Sentry error tracking
    Sentry.captureException(error, {
      tags: {
        endpoint: '/api/companies/ensure',
        operation: 'company_creation',
      },
      extra: {
        userEmail: session?.user?.email,
        userId: session?.user?.sub,
        errorType: error instanceof Error ? error.constructor.name : 'Unknown',
        databaseUrl: process.env.DATABASE_URL ? 'SET' : 'MISSING',
        databaseUrlPostgresql: process.env.DATABASE_URL_POSTGRESQL ? 'SET' : 'MISSING',
        nodeEnv: process.env.NODE_ENV,
        usePostgresql: process.env.USE_POSTGRESQL,
      }
    })

    return jsonError(error)
  }
}

/**
 * Generate a default company name from user info
 */
function generateCompanyName(user: any): string {
  // Try to use user's name
  if (user.name) {
    return `${user.name}'s Company`
  }

  // Try to use given name
  if (user.given_name) {
    return `${user.given_name}'s Company`
  }

  // Try to use nickname
  if (user.nickname) {
    return `${user.nickname}'s Company`
  }

  // Use email prefix as fallback
  if (user.email) {
    const emailPrefix = user.email.split('@')[0]
    const capitalizedPrefix = emailPrefix.charAt(0).toUpperCase() + emailPrefix.slice(1)
    return `${capitalizedPrefix}'s Company`
  }

  // Ultimate fallback
  return 'My Company'
}

/**
 * GET endpoint to check company status
 */
export async function GET(req: NextRequest) {
  try {
    const session = await getSession()

    if (!session?.user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Check if company exists
    const existingCompany = await db.query.companies.findFirst({
      where: eq(companies.auth0_user_id, session.user.sub)
    })

    return jsonOk({
      exists: !!existingCompany,
      company: existingCompany,
      needsOnboarding: existingCompany ? existingCompany.onboarding_completed !== 'true' : true
    })

  } catch (error) {
    console.error('❌ Error checking company status:', error)
    return jsonError(error)
  }
}
