/**
 * Manufacturing ERP - Shipments API
 * Professional shipping management endpoints
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import { customers, salesContracts } from "@/lib/schema-postgres"
import { shipments } from "@/lib/schema-shipping"
import { eq, and, desc, ilike, or, isNull } from "drizzle-orm"
import { ShippingService } from "@/lib/services/shipping-service"
import { ShippingLocationService } from "@/lib/services/shipping-location-service"
import { z } from "zod"

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

const createShipmentSchema = z.object({
  sales_contract_id: z.string().optional(),
  customer_id: z.string().min(1, "Customer is required"),
  shipping_method: z.enum(["sea_freight", "air_freight", "express", "truck"], {
    errorMap: () => ({ message: "Invalid shipping method" })
  }),
  carrier: z.string().optional(),
  service_type: z.enum(["standard", "express", "economy"]).optional(),
  pickup_address: z.any().optional(),
  delivery_address: z.any().optional(),
  // ✅ PHASE 2 ENHANCEMENT: Location integration (optional for backward compatibility)
  pickup_location_id: z.string().optional(),
  staging_location_id: z.string().optional(),
  pickup_instructions: z.string().optional(),
  ship_date: z.string().optional(),
  estimated_delivery: z.string().optional(),
  shipping_cost: z.string().optional(),
  insurance_cost: z.string().optional(),
  notes: z.string().optional(),
  special_instructions: z.string().optional(),
  items: z.array(z.object({
    product_id: z.string().min(1, "Product is required"),
    stock_lot_id: z.string().optional(),
    quantity: z.string().min(1, "Quantity is required"),
    unit_price: z.string().optional(),
    batch_number: z.string().optional(),
    lot_number: z.string().optional(),
  })).min(1, "At least one item is required")
})

const createFromContractSchema = z.object({
  sales_contract_id: z.string().min(1, "Sales contract ID is required"),
  shipping_method: z.enum(["sea_freight", "air_freight", "express", "truck"]).optional(),
  carrier: z.string().optional(),
  service_type: z.enum(["standard", "express", "economy"]).optional(),
  ship_date: z.string().optional(),
  estimated_delivery: z.string().optional(),
  shipping_cost: z.string().optional(),
  insurance_cost: z.string().optional(),
  notes: z.string().optional(),
  special_instructions: z.string().optional(),
})

// ============================================================================
// GET - List Shipments
// ============================================================================

export const GET = withTenantAuth(async function GET(request: NextRequest, context) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get("search") || ""
    const status = searchParams.get("status") || ""
    const customer_id = searchParams.get("customer_id") || ""
    const unlinked = searchParams.get("unlinked") === "true"
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "50")
    const offset = (page - 1) * limit

    // Build where conditions
    let whereConditions = [eq(shipments.company_id, context.companyId)]

    if (search) {
      whereConditions.push(
        or(
          ilike(shipments.shipment_number, `%${search}%`),
          ilike(shipments.tracking_number, `%${search}%`),
          ilike(shipments.notes, `%${search}%`)
        )!
      )
    }

    if (status) {
      whereConditions.push(eq(shipments.status, status))
    }

    if (customer_id) {
      whereConditions.push(eq(shipments.customer_id, customer_id))
    }

    // ✅ EXPORT INTEGRATION: Filter for unlinked shipments
    if (unlinked) {
      whereConditions.push(isNull(shipments.export_declaration_id))
    }

    // Get shipments with relationships
    const shipmentsList = await db.query.shipments.findMany({
      where: and(...whereConditions),
      with: {
        customer: {
          columns: {
            id: true,
            name: true,
            contact_name: true,
            contact_email: true,
            contact_phone: true
          }
        },
        salesContract: {
          columns: {
            id: true,
            number: true,
            status: true
          }
        },
        items: {
          with: {
            product: {
              columns: {
                id: true,
                name: true,
                sku: true,
                unit: true
              }
            }
          }
        }
      },
      orderBy: [desc(shipments.created_at)],
      limit,
      offset
    })

    // Get total count for pagination
    const totalCount = await db
      .select({ count: shipments.id })
      .from(shipments)
      .where(and(...whereConditions))

    return jsonOk({
      shipments: shipmentsList,
      pagination: {
        page,
        limit,
        total: totalCount.length,
        pages: Math.ceil(totalCount.length / limit)
      }
    })

  } catch (error) {
    console.error("Error fetching shipments:", error)
    return jsonError("Failed to fetch shipments", 500)
  }
})

// ============================================================================
// POST - Create Shipment
// ============================================================================

export const POST = withTenantAuth(async function POST(request: NextRequest, context) {
  try {
    const body = await request.json()

    // Check if creating from contract
    if (body.from_contract) {
      const validated = createFromContractSchema.parse(body)

      const shippingService = new ShippingService()
      const shipment = await shippingService.createShipmentFromContract(
        validated.sales_contract_id,
        context.companyId,
        context.userId
      )

      // Get the created shipment with relationships
      const createdShipment = await db.query.shipments.findFirst({
        where: and(
          eq(shipments.id, shipment.id),
          eq(shipments.company_id, context.companyId)
        ),
        with: {
          customer: true,
          salesContract: true,
          items: {
            with: {
              product: true
            }
          }
        }
      })

      return jsonOk(createdShipment, { status: 201 })
    }

    // Regular shipment creation
    const validated = createShipmentSchema.parse(body)

    // Verify customer exists and belongs to company
    const customer = await db.query.customers.findFirst({
      where: and(
        eq(customers.id, validated.customer_id),
        eq(customers.company_id, context.companyId)
      )
    })

    if (!customer) {
      return jsonError("Customer not found", 404)
    }

    // If sales contract specified, verify it exists and belongs to company
    if (validated.sales_contract_id) {
      const contract = await db.query.salesContracts.findFirst({
        where: and(
          eq(salesContracts.id, validated.sales_contract_id),
          eq(salesContracts.company_id, context.companyId)
        )
      })

      if (!contract) {
        return jsonError("Sales contract not found", 404)
      }

      // ERP Best Practice: Allow approved, active, and in_production contracts to be shipped
      const shippableStatuses = ["approved", "active", "in_production"]
      if (!shippableStatuses.includes(contract.status)) {
        return jsonError(`Only contracts with status ${shippableStatuses.join(", ")} can be shipped`, 400)
      }
    }

    // ✅ PHASE 2 ENHANCEMENT: Location validation and inventory checking (optional)
    const shippingLocationService = new ShippingLocationService()

    // Validate pickup location if specified
    if (validated.pickup_location_id && validated.pickup_location_id.trim() !== "") {
      const availableLocations = await shippingLocationService.getAvailableShippingLocations(
        validated.shipping_method,
        context.companyId
      )

      const isLocationAvailable = availableLocations.some(loc => loc.locationId === validated.pickup_location_id)
      if (!isLocationAvailable) {
        return jsonError("Selected pickup location is not available for this shipping method", 400)
      }
    }

    // Validate staging capacity if specified
    if (validated.staging_location_id && validated.staging_location_id.trim() !== "") {
      const totalVolume = validated.items.reduce((sum, item) => sum + parseFloat(item.quantity), 0)

      const capacityValidation = await shippingLocationService.validateShippingCapacity(
        validated.staging_location_id,
        totalVolume,
        context.companyId
      )

      if (!capacityValidation.canAccommodate) {
        return jsonError(`Insufficient capacity at staging location: ${capacityValidation.message}`, 400, {
          available_capacity: capacityValidation.availableCapacity,
          required_capacity: totalVolume,
          alternative_locations: capacityValidation.alternativeLocations
        })
      }
    }

    // Inventory availability checking for manual shipments
    for (const item of validated.items) {
      const shippingService = new ShippingService()
      const availableInventory = await shippingService.getAvailableInventory(item.product_id, context.companyId)

      const totalAvailable = availableInventory.reduce((sum, lot) => sum + parseFloat(lot.qty), 0)
      const requiredQuantity = parseFloat(item.quantity)

      if (totalAvailable < requiredQuantity) {
        return jsonError(`Insufficient inventory for product ${item.product_id}`, 400, {
          product_id: item.product_id,
          available_quantity: totalAvailable,
          required_quantity: requiredQuantity,
          shortage: requiredQuantity - totalAvailable
        })
      }
    }

    // Create shipment
    const shippingService = new ShippingService()
    const shipment = await shippingService.createShipment(validated, context.companyId, context.userId)

    // Get the created shipment with relationships
    const createdShipment = await db.query.shipments.findFirst({
      where: and(
        eq(shipments.id, shipment.id),
        eq(shipments.company_id, context.companyId)
      ),
      with: {
        customer: true,
        salesContract: true,
        items: {
          with: {
            product: true
          }
        }
      }
    })

    return jsonOk(createdShipment, { status: 201 })

  } catch (error) {
    console.error("Error creating shipment:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, error.errors)
    }

    if (error instanceof Error) {
      return jsonError(error.message, 400)
    }

    return jsonError("Failed to create shipment", 500)
  }
})
