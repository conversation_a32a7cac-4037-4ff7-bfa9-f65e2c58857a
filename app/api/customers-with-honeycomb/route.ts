/**
 * Manufacturing ERP - Customers API with Honeycomb Tracing
 * 
 * Example of how to add Honeycomb observability to your existing API routes
 * This demonstrates the integration pattern for all your ERP endpoints
 */

import { db, uid } from "@/lib/db"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { customers } from "@/lib/schema-postgres"
import { desc, eq, and } from "drizzle-orm"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { customerSchema } from "@/lib/validations"
import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"

// 🍯 HONEYCOMB: Import tracing utilities
import { withHoneycombTracing, ERPTracing } from "@/lib/honeycomb-middleware"
import { traceDatabaseOperation, addSpanAttributes } from "@/lib/honeycomb"

// 🛡️ SECURE + 🍯 TRACED: Multi-tenant GET endpoint with Honeycomb observability
export const GET = withHoneycombTracing(
  withTenantAuth(async function GET(request: NextRequest, context) {
    try {
      // 🍯 Add ERP context to trace
      addSpanAttributes({
        'erp.module': 'customers',
        'erp.operation': 'list',
        'erp.company_id': context.companyId,
        'erp.user_id': context.userId,
      })

      // 🍯 Trace database operation
      const rows = await traceDatabaseOperation('select', 'customers', async () => {
        return await db.query.customers.findMany({
          where: eq(customers.company_id, context.companyId),
          orderBy: [desc(customers.created_at)],
        })
      })

      // 🍯 Add result metrics to trace
      addSpanAttributes({
        'erp.customers.count': rows.length,
        'erp.customers.has_data': rows.length > 0,
      })

      return jsonOk(rows)
    } catch (e) {
      // 🍯 Error will be automatically tracked by withHoneycombTracing
      return jsonError(e)
    }
  }),
  {
    operationName: 'customers-list'
  }
)

// 🛡️ SECURE + 🍯 TRACED: Multi-tenant POST endpoint with Honeycomb observability
export const POST = withHoneycombTracing(
  withTenantAuth(async function POST(req: NextRequest, context) {
    try {
      // 🍯 Add ERP context to trace
      addSpanAttributes({
        'erp.module': 'customers',
        'erp.operation': 'create',
        'erp.company_id': context.companyId,
        'erp.user_id': context.userId,
      })

      // Validate request body
      const validation = await validateRequestBody(req, customerSchema)

      if (!validation.success) {
        // 🍯 Track validation errors
        addSpanAttributes({
          'erp.validation.success': false,
          'erp.validation.error_count': validation.error.issues.length,
        })
        return createValidationErrorResponse(validation.error.issues)
      }

      const data = validation.data

      // 🍯 Add customer data context (without sensitive info)
      addSpanAttributes({
        'erp.validation.success': true,
        'erp.customer.has_email': !!data.email,
        'erp.customer.has_phone': !!data.phone,
        'erp.customer.has_address': !!(data.address || data.city),
      })

      // 🍯 Trace database operation
      const newCustomer = await traceDatabaseOperation('insert', 'customers', async () => {
        const customerId = uid()
        
        await db.insert(customers).values({
          id: customerId,
          company_id: context.companyId,
          name: data.name,
          email: data.email,
          phone: data.phone || null,
          address: data.address || null,
          city: data.city || null,
          country: data.country || null,
          postal_code: data.postal_code || null,
          tax_id: data.tax_id || null,
          payment_terms: data.payment_terms || null,
          credit_limit: data.credit_limit || null,
          notes: data.notes || null,
        })

        return customerId
      })

      // 🍯 Track successful customer creation
      ERPTracing.trackContractOperation('customer_created', newCustomer, 'active')
      
      addSpanAttributes({
        'erp.customer.created': true,
        'erp.customer.id': newCustomer,
      })

      return jsonOk({ 
        id: newCustomer, 
        message: "Customer created successfully",
        tracing: {
          traced: true,
          module: 'customers',
          operation: 'create'
        }
      }, { status: 201 })

    } catch (e) {
      // 🍯 Error will be automatically tracked by withHoneycombTracing
      addSpanAttributes({
        'erp.customer.created': false,
        'erp.error.type': 'customer_creation_failed',
      })
      return jsonError(e)
    }
  }),
  {
    operationName: 'customers-create'
  }
)
