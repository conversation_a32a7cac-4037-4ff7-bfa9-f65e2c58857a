import { db, uid } from "@/lib/db"
import { json<PERSON><PERSON>r, jsonOk } from "@/lib/api-helpers"
import { locations } from "@/lib/schema-postgres"
import { desc, eq } from "drizzle-orm"
import { withTenantAuth } from "@/lib/tenant-utils"
import { z } from "zod"

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

const createLocationSchema = z.object({
  name: z.string().min(1, "Location name is required"),
  type: z.enum([
    'warehouse', 'raw_materials', 'finished_goods', 'work_in_progress',
    'quality_control', 'shipping', 'receiving', 'quarantine', 'returns'
  ]),
  description: z.string().optional(),
  capacity: z.number().int().min(0).optional(),
  location_code: z.string().optional(),
  is_active: z.boolean().optional(),
  is_temperature_controlled: z.boolean().optional(),
  security_level: z.enum(['low', 'medium', 'high']).optional(),
  parent_location_id: z.string().optional(),
  address: z.string().optional(),
  floor_level: z.number().int().optional(),
  zone: z.string().optional(),
  allows_mixed_products: z.boolean().optional(),
  requires_quality_check: z.boolean().optional(),
  automated_handling: z.boolean().optional(),
})

// ============================================================================
// API ENDPOINTS
// ============================================================================

// 🛡️ SECURE: Multi-tenant GET endpoint with proper isolation
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    console.log("🔍 LOCATIONS API: Starting query for company:", context.companyId)

    const rows = await db.query.locations.findMany({
      where: eq(locations.company_id, context.companyId),
      orderBy: [desc(locations.created_at)],
      with: {
        parentLocation: true,
        childLocations: true,
      },
    })

    console.log("✅ LOCATIONS API: Query successful, found", rows.length, "locations")
    return jsonOk({ locations: rows })
  } catch (error) {
    console.error("❌ LOCATIONS API: Query failed:", error)
    return jsonError("Failed to fetch locations", 500)
  }
})

// 🛡️ SECURE: Multi-tenant POST endpoint for creating locations
export const POST = withTenantAuth(async function POST(request, context) {
  try {
    console.log("📝 LOCATIONS API: Creating location for company:", context.companyId)

    const body = await request.json()
    const validatedData = createLocationSchema.parse(body)

    // Generate unique ID
    const locationId = uid()

    // Create location with company isolation
    const newLocation = await db.insert(locations).values({
      id: locationId,
      company_id: context.companyId, // 🛡️ CRITICAL: Multi-tenant isolation
      name: validatedData.name,
      type: validatedData.type,
      description: validatedData.description || null,
      capacity: validatedData.capacity || 0,
      location_code: validatedData.location_code || null,
      is_active: validatedData.is_active ?? true,
      is_temperature_controlled: validatedData.is_temperature_controlled ?? false,
      security_level: validatedData.security_level || 'medium',
      parent_location_id: validatedData.parent_location_id || null,
      address: validatedData.address || null,
      floor_level: validatedData.floor_level || null,
      zone: validatedData.zone || null,
      allows_mixed_products: validatedData.allows_mixed_products ?? true,
      requires_quality_check: validatedData.requires_quality_check ?? false,
      automated_handling: validatedData.automated_handling ?? false,
    }).returning()

    console.log("✅ LOCATIONS API: Location created successfully:", locationId)
    return jsonOk(newLocation[0], { status: 201 })
  } catch (error) {
    console.error("❌ LOCATIONS API: Creation failed:", error)

    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, error.errors)
    }

    // Handle unique constraint violations
    if (error instanceof Error && error.message.includes('unique')) {
      return jsonError("Location code already exists", 409)
    }

    return jsonError("Failed to create location", 500)
  }
})
