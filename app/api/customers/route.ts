import { db, uid } from "@/lib/db"
import { json<PERSON>rror, jsonOk } from "@/lib/api-helpers"
import { customers } from "@/lib/schema-postgres"
import { desc, eq, and } from "drizzle-orm"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { customerSchema } from "@/lib/validations"
import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"

// 🍯 HONEYCOMB: Import tracing utilities
import { withHoneycombTracing } from "@/lib/honeycomb-middleware"
import { traceDatabaseOperation, addSpanAttributes } from "@/lib/honeycomb"

// 🛡️ SECURE + 🍯 TRACED: Multi-tenant GET endpoint with Honeycomb observability
export const GET = withHoneycombTracing(
  withTenantAuth(async function GET(request: NextRequest, context) {
    try {
      // 🍯 Add ERP context to trace
      addSpanAttributes({
        'erp.module': 'customers',
        'erp.operation': 'list',
        'erp.company_id': context.companyId,
        'erp.user_id': context.userId,
      })

      // 🍯 Trace database operation
      const rows = await traceDatabaseOperation('select', 'customers', async () => {
        return await db.query.customers.findMany({
          where: eq(customers.company_id, context.companyId),
          orderBy: [desc(customers.created_at)],
        })
      })

      // 🍯 Add result metrics to trace
      addSpanAttributes({
        'erp.customers.count': rows.length,
        'erp.customers.has_data': rows.length > 0,
      })

      return jsonOk(rows)
    } catch (e) {
      return jsonError(e)
    }
  }),
  { operationName: 'customers-list' }
)

// 🛡️ SECURE + 🍯 TRACED: Multi-tenant POST endpoint with Honeycomb observability
export const POST = withHoneycombTracing(
  withTenantAuth(async function POST(req: NextRequest, context) {
    try {
      // 🍯 Add ERP context to trace
      addSpanAttributes({
        'erp.module': 'customers',
        'erp.operation': 'create',
        'erp.company_id': context.companyId,
        'erp.user_id': context.userId,
      })

      // Validate request body
      const validation = await validateRequestBody(req, customerSchema)

      if (!validation.success) {
        // 🍯 Track validation errors
        addSpanAttributes({
          'erp.validation.success': false,
          'erp.validation.error_count': validation.error.issues.length,
        })
        return createValidationErrorResponse(validation.error.issues)
      }

      const body = validation.data
      const id = uid("cust")

      // 🍯 Add customer data context (without sensitive info)
      addSpanAttributes({
        'erp.validation.success': true,
        'erp.customer.has_contact_email': !!body.contact_email,
        'erp.customer.has_contact_phone': !!body.contact_phone,
        'erp.customer.has_address': !!body.address,
        'erp.customer.status': body.status || 'active',
      })

      // Map validated data to database schema with company_id for tenant isolation
      const newCustomer = {
        id,
        company_id: context.companyId, // 🛡️ CRITICAL: Associate with current company
        name: body.name,
        contact_name: body.contact_name,
        contact_phone: body.contact_phone,
        contact_email: body.contact_email,
        address: body.address,
        tax_id: body.tax_id,
        bank: body.bank,
        incoterm: body.incoterm,
        payment_term: body.payment_term,
        status: body.status || "active",
      }

      // 🍯 Trace database operation
      await traceDatabaseOperation('insert', 'customers', async () => {
        return await db.insert(customers).values(newCustomer)
      })

      // 🛡️ SECURE: Only return customer if it belongs to current company
      const row = await traceDatabaseOperation('select', 'customers', async () => {
        return await db.query.customers.findFirst({
          where: and(
            eq(customers.id, id),
            eq(customers.company_id, context.companyId)
          ),
        })
      })

      // 🍯 Track successful customer creation
      addSpanAttributes({
        'erp.customer.created': true,
        'erp.customer.id': id,
      })

      return jsonOk(row, { status: 201 })
    } catch (e) {
      // 🍯 Track creation failure
      addSpanAttributes({
        'erp.customer.created': false,
        'erp.error.type': 'customer_creation_failed',
      })
      return jsonError(e)
    }
  }),
  { operationName: 'customers-create' }
)
