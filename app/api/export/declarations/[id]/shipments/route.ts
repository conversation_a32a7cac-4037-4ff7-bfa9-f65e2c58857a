/**
 * Manufacturing ERP - Export Declaration Shipments Integration API
 * Professional API for linking shipments to export declarations
 */

import { NextRequest } from "next/server"
import { db } from "@/lib/db"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { declarations } from "@/lib/schema-postgres"
import { shipments } from "@/lib/schema-shipping"
import { eq, and } from "drizzle-orm"
import { withTenantAuth } from "@/lib/tenant-utils"
import { z } from "zod"

const linkShipmentSchema = z.object({
  shipment_id: z.string().min(1, "Shipment ID is required"),
})

const unlinkShipmentSchema = z.object({
  shipment_id: z.string().min(1, "Shipment ID is required"),
})

// ✅ PROFESSIONAL ERP: Get shipments linked to export declaration
export const GET = withTenantAuth(async function GET(
  _: Request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: declarationId } = await params

    // ✅ SECURITY: Verify declaration exists and belongs to company
    const declaration = await db.query.declarations.findFirst({
      where: and(
        eq(declarations.id, declarationId),
        eq(declarations.company_id, context.companyId)
      )
    })

    if (!declaration) {
      return jsonError("Declaration not found", 404)
    }

    // ✅ SECURE: Get shipments linked to this declaration
    const linkedShipments = await db.query.shipments.findMany({
      where: and(
        eq(shipments.export_declaration_id, declarationId),
        eq(shipments.company_id, context.companyId)
      ),
      with: {
        customer: true,
        salesContract: true,
        items: {
          with: {
            product: true,
          },
        },
      },
    })

    return jsonOk(linkedShipments)
  } catch (error) {
    console.error("Error fetching declaration shipments:", error)
    return jsonError("Failed to fetch shipments", 500)
  }
})

// ✅ PROFESSIONAL ERP: Link shipment to export declaration
export const POST = withTenantAuth(async function POST(
  req: NextRequest,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: declarationId } = await params
    const body = await req.json()
    const { shipment_id } = linkShipmentSchema.parse(body)

    // ✅ SECURITY: Verify declaration exists and belongs to company
    const declaration = await db.query.declarations.findFirst({
      where: and(
        eq(declarations.id, declarationId),
        eq(declarations.company_id, context.companyId)
      )
    })

    if (!declaration) {
      return jsonError("Declaration not found", 404)
    }

    // ✅ SECURITY: Verify shipment exists and belongs to company
    const shipment = await db.query.shipments.findFirst({
      where: and(
        eq(shipments.id, shipment_id),
        eq(shipments.company_id, context.companyId)
      )
    })

    if (!shipment) {
      return jsonError("Shipment not found", 404)
    }

    // ✅ BUSINESS LOGIC: Check if shipment is already linked to another declaration
    if (shipment.export_declaration_id && shipment.export_declaration_id !== declarationId) {
      return jsonError("Shipment is already linked to another export declaration", 400)
    }

    // ✅ UPDATE: Link shipment to declaration
    await db.update(shipments)
      .set({
        export_declaration_id: declarationId,
        updated_at: new Date(),
      })
      .where(and(
        eq(shipments.id, shipment_id),
        eq(shipments.company_id, context.companyId)
      ))

    // ✅ AUDIT TRAIL: Log the linking
    console.log(`🔗 Export Declaration Shipment Linked:`, {
      declarationId,
      shipmentId: shipment_id,
      companyId: context.companyId,
      userId: context.userId,
      timestamp: new Date().toISOString(),
    })

    return jsonOk({ message: "Shipment linked successfully" })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return jsonError("Invalid request data", 400, error.errors)
    }
    console.error("Error linking shipment to declaration:", error)
    return jsonError("Failed to link shipment", 500)
  }
})

// ✅ PROFESSIONAL ERP: Unlink shipment from export declaration
export const DELETE = withTenantAuth(async function DELETE(
  req: NextRequest,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: declarationId } = await params
    const body = await req.json()
    const { shipment_id } = unlinkShipmentSchema.parse(body)

    // ✅ SECURITY: Verify declaration exists and belongs to company
    const declaration = await db.query.declarations.findFirst({
      where: and(
        eq(declarations.id, declarationId),
        eq(declarations.company_id, context.companyId)
      )
    })

    if (!declaration) {
      return jsonError("Declaration not found", 404)
    }

    // ✅ SECURITY: Verify shipment exists and belongs to company
    const shipment = await db.query.shipments.findFirst({
      where: and(
        eq(shipments.id, shipment_id),
        eq(shipments.company_id, context.companyId)
      )
    })

    if (!shipment) {
      return jsonError("Shipment not found", 404)
    }

    // ✅ UPDATE: Unlink shipment from declaration
    await db.update(shipments)
      .set({
        export_declaration_id: null,
        updated_at: new Date(),
      })
      .where(and(
        eq(shipments.id, shipment_id),
        eq(shipments.company_id, context.companyId)
      ))

    // ✅ AUDIT TRAIL: Log the unlinking
    console.log(`🔗 Export Declaration Shipment Unlinked:`, {
      declarationId,
      shipmentId: shipment_id,
      companyId: context.companyId,
      userId: context.userId,
      timestamp: new Date().toISOString(),
    })

    return jsonOk({ message: "Shipment unlinked successfully" })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return jsonError("Invalid request data", 400, error.errors)
    }
    console.error("Error unlinking shipment from declaration:", error)
    return jsonError("Failed to unlink shipment", 500)
  }
})
