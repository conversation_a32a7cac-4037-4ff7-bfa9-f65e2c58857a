/**
 * Manufacturing ERP - Export Declaration Document Download API
 * Professional API for downloading individual documents
 */

import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonError } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import { declarations, documents } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"

// ✅ PROFESSIONAL ERP: Download individual document
export const GET = withTenantAuth(async function GET(
  _: NextRequest,
  context,
  { params }: { params: Promise<{ id: string; filename: string }> }
) {
  try {
    const { id: declarationId, filename } = await params

    // ✅ SECURITY: Verify declaration exists and belongs to company
    const declaration = await db.query.declarations.findFirst({
      where: and(
        eq(declarations.id, declarationId),
        eq(declarations.company_id, context.companyId)
      )
    })

    if (!declaration) {
      return jsonError("Declaration not found", 404)
    }

    // ✅ SECURITY: Verify document exists and belongs to this declaration
    const document = await db.query.documents.findFirst({
      where: and(
        eq(documents.declaration_id, declarationId),
        eq(documents.filename, filename),
        eq(documents.company_id, context.companyId)
      )
    })

    if (!document) {
      return jsonError("Document not found", 404)
    }

    // ✅ PROFESSIONAL: Handle Vercel Blob URLs - redirect to blob URL directly
    // Since export documents are stored in Vercel Blob, we can redirect to the blob URL
    // This is more efficient than proxying the file through our API

    console.log('🔍 Serving export document:', {
      filename: document.filename,
      url: document.url,
      filetype: document.filetype,
    })

    try {
      // ✅ VERCEL BLOB: Fetch from blob storage and proxy through our API for security
      const response = await fetch(document.url)

      if (!response.ok) {
        console.error('❌ Failed to fetch from Vercel Blob:', response.status, response.statusText)
        throw new Error(`Failed to fetch document from storage: ${response.status}`)
      }

      const blob = await response.blob()

      // ✅ PROFESSIONAL: Return file with proper headers
      return new Response(blob, {
        headers: {
          'Content-Type': document.filetype || 'application/octet-stream',
          'Content-Disposition': `inline; filename="${document.filename}"`,
          'Cache-Control': 'private, max-age=3600',
          'Access-Control-Allow-Origin': '*',
        },
      })
    } catch (storageError) {
      console.error("❌ Storage fetch error:", storageError)
      return jsonError("Failed to retrieve document", 500)
    }
  } catch (error) {
    console.error("Error downloading document:", error)
    return jsonError("Failed to download document", 500)
  }
})
