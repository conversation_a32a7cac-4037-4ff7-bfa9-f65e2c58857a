/**
 * Manufacturing ERP - Export Declaration File Serving Endpoint
 * Secure file serving for export declaration documents (local development)
 */

import { NextRequest } from "next/server"
import { jsonError } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import { documents, declarations } from "@/lib/schema-postgres"
import { and, eq } from "drizzle-orm"
import { withTenantAuth } from "@/lib/tenant-utils"
import { readFile } from "fs/promises"
import { join } from "path"
import { lookup } from "mime-types"

// ✅ MANUFACTURING ERP: Secure file serving endpoint for export declaration documents
export const GET = withTenantAuth(async function GET(
  request: NextRequest,
  context,
  { params }: { params: Promise<{ id: string; filename: string }> }
) {
  try {
    const { id: declarationId, filename } = await params

    console.log('🔍 File serving request:', {
      declarationId,
      filename,
      companyId: context.companyId,
      userId: context.userId,
    })

    // ✅ SECURITY: Validate filename to prevent directory traversal
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      console.error('❌ Invalid filename detected:', filename)
      return jsonError('Invalid filename', 400)
    }

    // ✅ SECURITY: Check if declaration exists and belongs to company
    const declaration = await db.query.declarations.findFirst({
      where: and(
        eq(declarations.id, declarationId),
        eq(declarations.company_id, context.companyId)
      )
    })

    if (!declaration) {
      console.error('❌ Declaration not found or access denied:', {
        declarationId,
        companyId: context.companyId
      })
      return jsonError('Declaration not found', 404)
    }

    // ✅ SECURITY: Verify document exists in database and belongs to company
    const document = await db.query.documents.findFirst({
      where: and(
        eq(documents.filename, filename),
        eq(documents.declaration_id, declarationId),
        eq(documents.company_id, context.companyId)
      )
    })

    if (!document) {
      console.error('❌ Document not found in database:', {
        filename,
        declarationId,
        companyId: context.companyId
      })
      return jsonError('Document not found', 404)
    }

    let fileBuffer: Buffer
    let fileSize: number

    try {
      // ✅ LOCAL DEVELOPMENT: Read from local file system
      const filePath = join(process.cwd(), 'uploads', 'export', context.companyId, declarationId, 'documents', filename)
      fileBuffer = await readFile(filePath)
      fileSize = fileBuffer.length

      console.log('✅ File read successfully:', {
        filename,
        fileSize,
        filePath: filePath.replace(process.cwd(), '.')
      })
    } catch (error) {
      console.error('❌ Failed to read file:', {
        filename,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      return jsonError('File not found on disk', 404)
    }

    // ✅ SECURITY: Determine safe content type
    const mimeType = lookup(filename) || 'application/octet-stream'
    const safeContentType = mimeType.startsWith('text/') ||
      mimeType.startsWith('image/') ||
      mimeType.startsWith('application/pdf') ||
      mimeType.startsWith('application/json')
      ? mimeType
      : 'application/octet-stream'

    // ✅ AUDIT TRAIL: Log file access
    console.log('📁 Export Declaration Document Accessed:', {
      declarationId,
      filename,
      companyId: context.companyId,
      userId: context.userId,
      fileSize,
      contentType: safeContentType,
      timestamp: new Date().toISOString(),
    })

    // ✅ PROFESSIONAL: Return file with proper headers
    // Allow iframe embedding for safe content types (text, images, PDFs)
    const allowIframe = safeContentType.startsWith('text/') ||
      safeContentType.startsWith('image/') ||
      safeContentType.startsWith('application/pdf')

    return new Response(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': safeContentType,
        'Content-Length': fileSize.toString(),
        'Content-Disposition': `inline; filename="${filename}"`,
        'Cache-Control': 'private, max-age=3600', // Cache for 1 hour
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': allowIframe ? 'SAMEORIGIN' : 'DENY',
      },
    })
  } catch (error) {
    console.error('❌ File serving error:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    })
    return jsonError('Internal server error', 500)
  }
})
