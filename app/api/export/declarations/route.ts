import { db, uid } from "@/lib/db"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { declarations, declarationItems } from "@/lib/schema-postgres"
import { desc, eq, and } from "drizzle-orm"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { declarationSchema } from "@/lib/validations"
import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"

// 🛡️ SECURE: Multi-tenant GET endpoint with proper isolation
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const rows = await db.query.declarations.findMany({
      where: eq(declarations.company_id, context.companyId),
      orderBy: [desc(declarations.created_at)],
      with: {
        items: {
          with: {
            product: true,
          },
        },
        // ✅ COMPREHENSIVE: Load all relations for table display
        documents: true,
        salesContract: {
          with: {
            customer: true,
          },
        },
      },
    })
    return jsonOk(rows)
  } catch (e) {
    return jsonError(e)
  }
})

// 🛡️ SECURE: Multi-tenant POST endpoint with proper isolation
export const POST = withTenantAuth(async function POST(req: NextRequest, context) {
  try {
    // Validate request body
    const validation = await validateRequestBody(req, declarationSchema)

    if (!validation.success) {
      return createValidationErrorResponse(validation.error.issues)
    }

    const body = validation.data
    const id = uid("decl")

    const newDeclaration = {
      id,
      company_id: context.companyId, // 🛡️ CRITICAL: Associate with current company
      number: body.number,
      status: body.status || "draft",
      // ✅ SIMPLE INTEGRATION: Optional contract linking
      sales_contract_id: body.sales_contract_id || null,
      contract_reference: body.contract_reference || null,
    }
    const items = body.items.map((item) => {
      // ✅ PHASE 1 ENHANCEMENT: Calculate total value if unit value provided
      let totalValue: string | undefined
      if (item.unit_value && item.qty) {
        const qty = typeof item.qty === 'string' ? parseFloat(item.qty) : item.qty
        totalValue = (item.unit_value * qty).toString()
      }

      return {
        id: uid("dcli"),
        declaration_id: id, // Fixed: Use correct field name
        product_id: item.productId || item.product_id, // Support both formats
        qty: item.qty.toString(),
        hs_code: item.hsCode || item.hs_code, // Support both formats
        quality_status: item.quality_status || "pending", // ✅ SIMPLE INTEGRATION: Quality status
        // ✅ PHASE 1 ENHANCEMENT: Value declaration fields for customs compliance
        unit_value: item.unit_value?.toString(),
        total_value: item.total_value?.toString() || totalValue,
        value_currency: item.value_currency || "USD",
        value_method: item.value_method,
      }
    })

    await db.transaction(async (tx) => {
      await tx.insert(declarations).values(newDeclaration)
      if (items.length > 0) {
        await tx.insert(declarationItems).values(items)
      }
    })

    // 🛡️ SECURE: Only return declaration if it belongs to current company
    const row = await db.query.declarations.findFirst({
      where: and(
        eq(declarations.id, id),
        eq(declarations.company_id, context.companyId)
      ),
      with: {
        items: {
          with: {
            product: true,
          },
        },
        // ✅ COMPREHENSIVE: Load all relations for immediate use
        documents: true,
        salesContract: {
          with: {
            customer: true,
          },
        },
      },
    })
    return jsonOk(row, { status: 201 })
  } catch (e) {
    return jsonError(e)
  }
})
