/**
 * Manufacturing ERP - Debug Location Relationships
 * Comprehensive analysis of what should be tracked at each location type
 */

import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import {
  locations,
  stockLots,
  rawMaterialLots,
  qualityInspections
} from "@/lib/schema-postgres"
import { shipments } from "@/lib/schema-shipping"
import { eq, and } from "drizzle-orm"

export const GET = withTenantAuth(async function GET(request, context) {
  try {
    console.log("🔍 DEBUG: Analyzing location relationships for company:", context.companyId)

    // Get all locations
    const allLocations = await db.query.locations.findMany({
      where: eq(locations.company_id, context.companyId),
    })

    // Get quality inspections (should be tracked at Quality Control Lab)
    const qualityInspections = await db.query.qualityInspections.findMany({
      where: eq(qualityInspections.company_id, context.companyId),
      with: {
        workOrder: {
          with: {
            product: true
          }
        }
      }
    })

    // Get shipments (should be tracked at Shipping Dock)
    // Note: Shipments are in separate schema, simplified for now
    const allShipments: any[] = []

    // Analyze what should be at each location
    const locationAnalysis = await Promise.all(allLocations.map(async (location) => {
      const analysis = {
        locationId: location.id,
        locationName: location.name,
        locationType: location.type,
        currentUtilization: 0,
        shouldTrack: [] as string[],
        actualData: {} as any,
        recommendations: [] as string[]
      }

      switch (location.type) {
        case 'raw_materials':
          // Should track raw material lots
          const rawMaterialLots = await db.query.rawMaterialLots.findMany({
            where: and(
              eq(rawMaterialLots.company_id, context.companyId),
              eq(rawMaterialLots.location, location.id)
            ),
            with: { rawMaterial: true }
          })

          analysis.shouldTrack = ['raw_material_lots']
          analysis.actualData.rawMaterialLots = rawMaterialLots.length
          analysis.actualData.totalQuantity = rawMaterialLots.reduce((sum, lot) =>
            sum + parseFloat(lot.qty || '0'), 0
          )
          analysis.currentUtilization = (analysis.actualData.totalQuantity / location.capacity) * 100
          break

        case 'finished_goods':
          // Should track finished goods stock lots
          const finishedGoodsLots = await db.query.stockLots.findMany({
            where: and(
              eq(stockLots.company_id, context.companyId),
              eq(stockLots.location, location.id)
            ),
            with: { product: true }
          })

          analysis.shouldTrack = ['stock_lots']
          analysis.actualData.stockLots = finishedGoodsLots.length
          analysis.actualData.totalQuantity = finishedGoodsLots.reduce((sum, lot) =>
            sum + parseFloat(lot.qty || '0'), 0
          )
          analysis.currentUtilization = (analysis.actualData.totalQuantity / location.capacity) * 100
          break

        case 'quality_control':
          // Should track quality inspections in progress
          const activeInspections = qualityInspections.filter(inspection =>
            inspection.status === 'in-progress' || inspection.status === 'pending'
          )

          analysis.shouldTrack = ['quality_inspections_in_progress', 'items_under_inspection']
          analysis.actualData.activeInspections = activeInspections.length
          analysis.actualData.inspectionDetails = activeInspections.map(inspection => ({
            id: inspection.id,
            type: inspection.inspection_type,
            status: inspection.status,
            productName: inspection.workOrder?.product?.name || 'Unknown'
          }))

          // Quality Control utilization based on active inspections
          analysis.currentUtilization = (activeInspections.length / location.capacity) * 100

          if (activeInspections.length === 0) {
            analysis.recommendations.push("No active quality inspections found. Create quality inspections to see utilization.")
          }
          break

        case 'shipping':
          // Should track shipments being prepared/staged
          const activeShipments = allShipments.filter(shipment =>
            (shipment.pickup_location_id === location.id ||
              shipment.staging_location_id === location.id) &&
            (shipment.status === 'preparing' || shipment.status === 'ready')
          )

          analysis.shouldTrack = ['shipments_preparing', 'shipments_ready', 'staged_items']
          analysis.actualData.activeShipments = activeShipments.length
          analysis.actualData.shipmentDetails = activeShipments.map(shipment => ({
            id: shipment.id,
            shipment_number: shipment.shipment_number,
            status: shipment.status,
            itemCount: shipment.items?.length || 0
          }))

          // Calculate total items being staged
          const totalStagedItems = activeShipments.reduce((sum, shipment) =>
            sum + (shipment.items?.length || 0), 0
          )
          analysis.actualData.totalStagedItems = totalStagedItems
          analysis.currentUtilization = (totalStagedItems / location.capacity) * 100

          if (activeShipments.length === 0) {
            analysis.recommendations.push("No active shipments found. Create shipments with this location to see utilization.")
          }
          break

        default:
          analysis.shouldTrack = ['unknown']
          analysis.recommendations.push(`Location type '${location.type}' needs specific tracking implementation.`)
      }

      return analysis
    }))

    const summary = {
      totalLocations: allLocations.length,
      totalQualityInspections: qualityInspections.length,
      totalShipments: allShipments.length,
      locationTypes: [...new Set(allLocations.map(loc => loc.type))],
      utilizationIssues: locationAnalysis.filter(loc => loc.currentUtilization === 0 && loc.recommendations.length > 0)
    }

    const result = {
      summary,
      locationAnalysis,
      implementationNeeded: {
        qualityControlTracking: "Quality inspections need location tracking to show utilization at Quality Control Lab",
        shippingTracking: "Shipments need proper location assignment to show utilization at Shipping Dock",
        currentImplementation: "Only Raw Materials and Finished Goods locations currently show proper utilization"
      }
    }

    console.log("✅ DEBUG: Location relationship analysis complete")
    return jsonOk(result)
  } catch (error) {
    console.error("❌ DEBUG: Analysis failed:", error)
    return jsonError("Location relationship analysis failed", 500)
  }
})
