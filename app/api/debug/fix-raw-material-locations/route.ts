/**
 * Manufacturing ERP - Fix Raw Material Lot Locations
 * Production-ready fix to update raw material lot location references
 */

import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import { rawMaterialLots, locations } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"

export const POST = withTenantAuth(async function POST(request, context) {
  try {
    console.log("🔧 FIXING: Raw material lot locations for company:", context.companyId)

    // Get all locations for mapping
    const allLocations = await db.query.locations.findMany({
      where: eq(locations.company_id, context.companyId),
    })

    // Get all raw material lots with legacy location values
    const lots = await db.query.rawMaterialLots.findMany({
      where: eq(rawMaterialLots.company_id, context.companyId),
    })

    // Create mapping from legacy location codes to actual location IDs
    const locationMapping = {
      "rm_building_a": allLocations.find(loc => 
        loc.type === 'raw_materials' || 
        loc.name.toLowerCase().includes('raw materials')
      )?.id,
      "fg_main_warehouse": allLocations.find(loc => 
        loc.type === 'finished_goods' || 
        loc.name.toLowerCase().includes('finished goods')
      )?.id,
      "quality_control": allLocations.find(loc => 
        loc.type === 'quality_control' || 
        loc.name.toLowerCase().includes('quality')
      )?.id,
      "shipping_dock": allLocations.find(loc => 
        loc.type === 'shipping' || 
        loc.name.toLowerCase().includes('shipping')
      )?.id,
    }

    console.log("📍 Location mapping:", locationMapping)

    const updates = []
    const errors = []

    // Update each lot with the correct location ID
    for (const lot of lots) {
      const currentLocation = lot.location
      const newLocationId = locationMapping[currentLocation as keyof typeof locationMapping]

      if (newLocationId && newLocationId !== currentLocation) {
        try {
          await db
            .update(rawMaterialLots)
            .set({ 
              location: newLocationId,
              updated_at: new Date()
            })
            .where(eq(rawMaterialLots.id, lot.id))

          updates.push({
            lotId: lot.id,
            oldLocation: currentLocation,
            newLocation: newLocationId,
            qty: lot.qty,
          })

          console.log(`✅ Updated lot ${lot.id}: ${currentLocation} → ${newLocationId}`)
        } catch (error) {
          console.error(`❌ Failed to update lot ${lot.id}:`, error)
          errors.push({
            lotId: lot.id,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      } else if (!newLocationId) {
        console.log(`⚠️ No mapping found for location: ${currentLocation}`)
        errors.push({
          lotId: lot.id,
          error: `No mapping found for location: ${currentLocation}`
        })
      } else {
        console.log(`ℹ️ Lot ${lot.id} already has correct location: ${currentLocation}`)
      }
    }

    const result = {
      summary: {
        totalLots: lots.length,
        updatesApplied: updates.length,
        errors: errors.length,
      },
      locationMapping,
      updates,
      errors,
      message: updates.length > 0 
        ? `Successfully updated ${updates.length} raw material lots with correct location IDs`
        : "No updates needed - all lots already have correct location IDs"
    }

    console.log("🎉 FIXING COMPLETE:", result.summary)
    return jsonOk(result)
  } catch (error) {
    console.error("❌ FIXING FAILED:", error)
    return jsonError("Failed to fix raw material lot locations", 500)
  }
})
