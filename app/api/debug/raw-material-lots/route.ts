/**
 * Manufacturing ERP - Debug Raw Material Lots API
 * Debug endpoint to check raw material lots and their location values
 */

import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import { rawMaterialLots, locations } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"

export const GET = withTenantAuth(async function GET(request, context) {
  try {
    console.log("🔍 DEBUG: Checking raw material lots for company:", context.companyId)

    // Get all raw material lots
    const lots = await db.query.rawMaterialLots.findMany({
      where: eq(rawMaterialLots.company_id, context.companyId),
      with: {
        rawMaterial: true,
      },
    })

    // Get all locations
    const allLocations = await db.query.locations.findMany({
      where: eq(locations.company_id, context.companyId),
    })

    // Analyze location values
    const locationValues = lots.map(lot => lot.location)
    const uniqueLocationValues = [...new Set(locationValues)]

    const debugInfo = {
      summary: {
        totalLots: lots.length,
        totalLocations: allLocations.length,
        uniqueLocationValues: uniqueLocationValues.length,
      },
      rawMaterialLots: lots.map(lot => ({
        id: lot.id,
        materialName: lot.rawMaterial?.name,
        qty: lot.qty,
        location: lot.location,
        status: lot.status,
        created_at: lot.created_at,
      })),
      locations: allLocations.map(loc => ({
        id: loc.id,
        name: loc.name,
        type: loc.type,
        location_code: loc.location_code,
      })),
      locationAnalysis: {
        uniqueLocationValues,
        locationMatches: uniqueLocationValues.map(locValue => {
          const matchingLocation = allLocations.find(loc =>
            loc.id === locValue ||
            loc.name === locValue ||
            loc.location_code === locValue
          )
          return {
            lotLocationValue: locValue,
            matchingLocationRecord: matchingLocation ? {
              id: matchingLocation.id,
              name: matchingLocation.name,
              type: matchingLocation.type,
              location_code: matchingLocation.location_code,
            } : null,
          }
        }),
      },
      utilizationTest: {
        message: "Testing utilization calculation for each location",
        results: await Promise.all(allLocations.map(async (location) => {
          // Test raw material lots for this location
          const testRawMaterialLots = await db.query.rawMaterialLots.findMany({
            where: and(
              eq(rawMaterialLots.company_id, context.companyId),
              eq(rawMaterialLots.location, location.id),
              eq(rawMaterialLots.status, 'available')
            ),
          })

          const totalRawMaterialQty = testRawMaterialLots.reduce((sum, lot) => {
            return sum + parseFloat(lot.qty || '0')
          }, 0)

          return {
            locationId: location.id,
            locationName: location.name,
            rawMaterialLots: testRawMaterialLots.length,
            totalRawMaterialQty,
            lotDetails: testRawMaterialLots.map(lot => ({
              id: lot.id,
              qty: lot.qty,
              location: lot.location,
              status: lot.status,
            }))
          }
        }))
      },
    }

    console.log("✅ DEBUG: Analysis complete")
    return jsonOk(debugInfo)
  } catch (error) {
    console.error("❌ DEBUG: Analysis failed:", error)
    return jsonError("Debug analysis failed", 500)
  }
})
