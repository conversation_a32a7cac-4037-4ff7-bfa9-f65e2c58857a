/**
 * Manufacturing ERP - Honeycomb Test API Route
 * 
 * Example API route demonstrating Honeycomb tracing integration
 * This shows how to instrument your existing API routes
 */

import { NextRequest, NextResponse } from 'next/server'
import { withHoneycombTracing, ERPTracing } from '@/lib/honeycomb-middleware'
import { traceBusinessProcess, traceDatabaseOperation, addSpanAttributes } from '@/lib/honeycomb'

// Example of a traced API route
export const GET = withHoneycombTracing(
  async function GET(req: NextRequest) {
    // Add custom attributes
    addSpanAttributes({
      'test.endpoint': 'honeycomb-test',
      'test.purpose': 'demonstration',
    })

    // Simulate database operation
    const dbResult = await traceDatabaseOperation('select', 'test_table', async () => {
      // Simulate database query
      await new Promise(resolve => setTimeout(resolve, 50))
      return { count: 42, status: 'success' }
    })

    // Simulate business process
    const businessResult = await traceBusinessProcess(
      'test_manufacturing_process',
      'contract',
      'test-contract-123',
      async () => {
        // Simulate business logic
        await new Promise(resolve => setTimeout(resolve, 100))
        
        // Track ERP operation
        ERPTracing.trackContractOperation('test', 'test-contract-123', 'approved')
        
        return { processId: 'test-process-456', result: 'completed' }
      }
    )

    // Return test results
    return NextResponse.json({
      message: 'Honeycomb tracing test successful',
      timestamp: new Date().toISOString(),
      database: dbResult,
      business: businessResult,
      tracing: {
        enabled: true,
        environment: process.env.NODE_ENV,
        honeycombEnabled: !!process.env.HONEYCOMB_API_KEY,
      }
    })
  },
  {
    operationName: 'honeycomb-test-endpoint'
  }
)

// Example POST endpoint with error handling
export const POST = withHoneycombTracing(
  async function POST(req: NextRequest) {
    try {
      const body = await req.json()
      
      addSpanAttributes({
        'test.request_body_size': JSON.stringify(body).length,
        'test.has_test_field': !!body.test,
      })

      // Simulate potential error
      if (body.simulateError) {
        throw new Error('Simulated error for testing Honeycomb error tracking')
      }

      // Simulate successful operation
      await new Promise(resolve => setTimeout(resolve, 200))

      return NextResponse.json({
        message: 'POST request processed successfully',
        received: body,
        processed: true,
      })
    } catch (error) {
      // Error will be automatically tracked by the middleware
      return NextResponse.json(
        { 
          error: 'Request processing failed',
          message: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      )
    }
  },
  {
    operationName: 'honeycomb-test-post'
  }
)
