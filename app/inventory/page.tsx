"use client"

import Link from "next/link"
import { AppShell } from "@/components/app-shell"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { useEffect, useState, Suspense, useMemo } from "react"
import { safeJson } from "@/lib/safe-fetch"
import { useI18n } from "@/components/i18n-provider"
import {
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Package,
  Package2,
  TrendingUp,
  Activity,
  Zap,
  ArrowDown,
  ArrowUp,
  ArrowLeftRight,
  Settings,
  BarChart3,
  Eye,
  X,
  Layers
} from "lucide-react"
import React from "react"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { getLocationForUI, LEGACY_LOCATION_MAPPING } from "@/lib/location-config"

// ============================================================================
// LOCATION UTILITIES - Database-driven location management
// ============================================================================

interface DatabaseLocation {
  id: string
  name: string
  type: string
  description?: string
  capacity: number
  current_utilization: number
  is_active: boolean
  location_code?: string
}

// Fetch locations from the API
async function fetchLocationsFromAPI(): Promise<DatabaseLocation[]> {
  try {
    const response = await fetch("/api/locations")
    if (!response.ok) {
      console.error("Failed to fetch locations:", response.status, response.statusText)
      return []
    }
    const data = await response.json()
    console.log("🔍 Locations API response:", data)
    // ✅ FIX: API returns { locations: [...] } format
    return Array.isArray(data) ? data : (data?.locations || data?.data || [])
  } catch (error) {
    console.error("Error fetching locations:", error)
    return []
  }
}

// Convert database locations to dropdown format
function getLocationsForDropdown(locations: DatabaseLocation[] | undefined): Array<{ value: string, label: string, description: string }> {
  if (!locations || !Array.isArray(locations)) {
    return []
  }
  return locations
    .filter(location => location.is_active)
    .map(location => ({
      value: location.id,
      label: `${getLocationIcon(location.type)} ${location.name}`,
      description: location.description || `${location.type} location`
    }))
}

// Get location icon based on type
function getLocationIcon(type: string): string {
  const iconMap: Record<string, string> = {
    'warehouse': '🏢',
    'raw_materials': '📦',
    'finished_goods': '✅',
    'work_in_progress': '⚙️',
    'quality_control': '🔍',
    'shipping': '🚛',
    'receiving': '📥',
    'quarantine': '⚠️',
    'returns': '↩️'
  }
  return iconMap[type] || '📍'
}
import { InventoryAnalyticsDashboard } from "@/components/inventory/inventory-analytics-dashboard"
import { InventoryDiscrepancyAnalyzer } from "@/components/inventory/inventory-discrepancy-analyzer"
import { ShippingInventoryAudit } from "@/components/inventory/shipping-inventory-audit"
import { EnhancedInventoryKPIs } from "@/components/inventory/enhanced-inventory-kpis"

export default function InventoryPage() {
  const { t } = useI18n()

  return (
    <AppShell>
      <div className="space-y-6">
        {/* ✅ PROFESSIONAL HEADER */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('inventory.management.title')}</h1>
            <p className="text-muted-foreground">
              {t('inventory.management.subtitle')}
            </p>
          </div>
        </div>

        <Tabs defaultValue="inventory" className="space-y-6">
          <TabsList>
            <TabsTrigger value="inventory">
              <div className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                {t('inventory.tabs.finished_goods')}
              </div>
            </TabsTrigger>
            <TabsTrigger value="analytics">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                {t('inventory.tabs.analytics')}
              </div>
            </TabsTrigger>
            <TabsTrigger value="discrepancy">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4" />
                {t('inventory.tabs.discrepancy')}
              </div>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="inventory" className="space-y-6">
            {/* ✅ ENHANCED KPIs - SEPARATED FINISHED GOODS & RAW MATERIALS */}
            <EnhancedInventoryKPIs />

            {/* ✅ RAW MATERIALS NAVIGATION */}
            <Card className="border-green-200 bg-green-50/30">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Package2 className="h-5 w-5 text-green-600" />
                    <div>
                      <h3 className="font-medium text-green-800">{t('inventory.raw_materials_nav.title')}</h3>
                      <p className="text-sm text-green-600">{t('inventory.raw_materials_nav.subtitle')}</p>
                    </div>
                  </div>
                  <Button variant="outline" className="border-green-300 text-green-700 hover:bg-green-100" asChild>
                    <a href="/raw-materials">
                      {t('inventory.raw_materials_nav.button')}
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* ✅ QUICK ACTIONS - RESTORED TRANSACTION FUNCTIONALITY */}
            <QuickActions />

            {/* ✅ MAIN INVENTORY TABLE - FULL WIDTH FOR BETTER UX */}
            <StockCard />

            {/* ✅ RECENT ACTIVITY - SIMPLIFIED */}
            <RecentActivity />
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            {/* ✅ ENHANCED: Cross-Category Analytics */}
            <Card className="border-blue-200 bg-blue-50/30">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <BarChart3 className="h-6 w-6 text-blue-600" />
                    <div>
                      <h3 className="text-lg font-semibold text-blue-800">{t('inventory.analytics.title')}</h3>
                      <p className="text-sm text-blue-600">
                        {t('inventory.analytics.subtitle')}
                      </p>
                    </div>
                  </div>
                  <Link
                    href="/inventory/analytics"
                    className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <TrendingUp className="h-4 w-4" />
                    {t('inventory.analytics.view_analytics')}
                  </Link>
                </div>
              </CardContent>
            </Card>

            {/* ✅ LEGACY: Original Analytics Dashboard */}
            <Suspense fallback={<div>{t('inventory.analytics.loading')}</div>}>
              <InventoryAnalyticsDashboard companyId="current-company" />
            </Suspense>
          </TabsContent>

          <TabsContent value="discrepancy" className="space-y-6">
            <InventoryDiscrepancyAnalyzer />
            <ShippingInventoryAudit />
          </TabsContent>
        </Tabs>
      </div>
    </AppShell>
  )
}

// ✅ REMOVED: Old InventoryKPIs component replaced with EnhancedInventoryKPIs

// Quick Actions Component
function QuickActions() {
  const { t } = useI18n()
  const [showInboundDialog, setShowInboundDialog] = useState(false)
  const [showOutboundDialog, setShowOutboundDialog] = useState(false)
  const [showTransferDialog, setShowTransferDialog] = useState(false)
  const [showAdjustmentDialog, setShowAdjustmentDialog] = useState(false)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5" />
          {t('inventory.quick_actions.title')}
        </CardTitle>
        <CardDescription>{t('inventory.quick_actions.subtitle')}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Button
            onClick={() => setShowInboundDialog(true)}
            className="h-20 flex-col gap-2"
            variant="outline"
          >
            <ArrowDown className="h-6 w-6 text-green-600" />
            <span>{t('inventory.quick_actions.receive')}</span>
          </Button>

          <Button
            onClick={() => setShowOutboundDialog(true)}
            className="h-20 flex-col gap-2"
            variant="outline"
          >
            <ArrowUp className="h-6 w-6 text-blue-600" />
            <span>{t('inventory.quick_actions.ship')}</span>
          </Button>

          <Button
            onClick={() => setShowTransferDialog(true)}
            className="h-20 flex-col gap-2"
            variant="outline"
          >
            <ArrowLeftRight className="h-6 w-6 text-purple-600" />
            <span>{t('inventory.quick_actions.transfer')}</span>
          </Button>

          <Button
            onClick={() => setShowAdjustmentDialog(true)}
            className="h-20 flex-col gap-2"
            variant="outline"
          >
            <Settings className="h-6 w-6 text-orange-600" />
            <span>{t('inventory.quick_actions.adjust')}</span>
          </Button>
        </div>

        {/* Quick Action Dialogs */}
        <QuickInboundDialog
          open={showInboundDialog}
          onOpenChange={setShowInboundDialog}
        />
        <QuickOutboundDialog
          open={showOutboundDialog}
          onOpenChange={setShowOutboundDialog}
        />
        <QuickTransferDialog
          open={showTransferDialog}
          onOpenChange={setShowTransferDialog}
        />
        <QuickAdjustmentDialog
          open={showAdjustmentDialog}
          onOpenChange={setShowAdjustmentDialog}
        />
      </CardContent>
    </Card>
  )
}

// Enhanced Stock Analytics Component with Location Intelligence
function StockAnalytics() {
  const { t } = useI18n()
  const [analytics, setAnalytics] = useState({
    locationDistribution: [],
    qualityDistribution: [],
    topProducts: [],
    locationUtilization: [],
    flowMetrics: [],
    locationAlerts: []
  })
  const [loading, setLoading] = useState(true)
  const [locations, setLocations] = useState<DatabaseLocation[]>([])

  // Load locations from API
  useEffect(() => {
    async function loadLocations() {
      const fetchedLocations = await fetchLocationsFromAPI()
      setLocations(fetchedLocations)
    }
    loadLocations()
  }, [])

  async function loadAnalytics() {
    try {
      console.log("🔄 Loading inventory analytics...")

      const [stockResponse, transactionsResponse] = await Promise.all([
        fetch("/api/inventory/lots").then(async res => {
          console.log("📊 Stock lots API response:", res.status, res.statusText)
          if (!res.ok) {
            console.error("❌ Stock lots API failed:", res.status, res.statusText)
            throw new Error(`Stock lots API failed: ${res.status}`)
          }
          return res.json()
        }),
        fetch("/api/inventory/transactions?limit=50").then(async res => {
          console.log("📊 Transactions API response:", res.status, res.statusText)
          if (!res.ok) {
            console.error("❌ Transactions API failed:", res.status, res.statusText)
            throw new Error(`Transactions API failed: ${res.status}`)
          }
          return res.json()
        })
      ])

      const stockData = Array.isArray(stockResponse) ? stockResponse : (stockResponse?.data || [])
      const transactionsData = Array.isArray(transactionsResponse) ? transactionsResponse : (transactionsResponse?.data || [])

      console.log("📦 Analytics data loaded:", {
        stockLots: stockData.length,
        transactions: transactionsData.length
      })

      // Enhanced Location Distribution with Capacity Analysis
      const locationStats = stockData.reduce((acc, lot) => {
        const location = lot.location || 'unknown'
        acc[location] = (acc[location] || 0) + parseFloat(lot.qty)
        return acc
      }, {})

      const locationDistribution = Object.entries(locationStats).map(([location, qty]) => ({
        location: location.replace(/_/g, ' '),
        qty: qty as number,
        percentage: Math.round((qty as number / Object.values(locationStats).reduce((a, b) => (a as number) + (b as number), 0)) * 100)
      }))

      // Professional Location Utilization Analysis using database locations
      const locationUtilization = locations.map(location => {
        // Check current stock for this location
        let currentStock = locationStats[location.id] || 0

        // Fallback: Check by location name variations
        if (currentStock === 0) {
          currentStock = locationStats[location.name] || 0
        }

        const capacity = location.capacity || 1000 // Default capacity if not set
        const utilization = Math.round((currentStock / capacity) * 100)
        const status = utilization > 90 ? 'critical' : utilization > 75 ? 'warning' : utilization > 50 ? 'good' : 'low'

        return {
          location: location.id,
          name: location.name,
          current: currentStock,
          capacity: capacity,
          utilization,
          status,
          type: location.type,
          icon: getLocationIcon(location.type),
          color: getLocationColor(location.type),
          description: location.description || `${location.type} location`
        }
      })

      // Helper function to get location color
      function getLocationColor(type: string): string {
        const colorMap: Record<string, string> = {
          'warehouse': 'blue-500',
          'raw_materials': 'amber-500',
          'finished_goods': 'green-500',
          'work_in_progress': 'blue-500',
          'quality_control': 'yellow-500',
          'shipping': 'purple-500',
          'receiving': 'indigo-500',
          'quarantine': 'red-500',
          'returns': 'orange-500'
        }
        return colorMap[type] || 'gray-500'
      }

      // Manufacturing Flow Metrics (Last 24 hours)
      const now = new Date()
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      const recentTransactions = transactionsData.filter(txn =>
        new Date(txn.created_at) > yesterday
      )

      const flowMetrics = [
        {
          flow: "Raw → WIP",
          description: "Production Consumption",
          count: recentTransactions.filter(txn =>
            txn.from_location === 'raw_materials' && txn.to_location === 'work_in_progress'
          ).length,
          volume: recentTransactions
            .filter(txn => txn.from_location === 'raw_materials' && txn.to_location === 'work_in_progress')
            .reduce((sum, txn) => sum + parseFloat(txn.qty || 0), 0)
        },
        {
          flow: "WIP → Finished",
          description: "Production Completion",
          count: recentTransactions.filter(txn =>
            txn.from_location === 'work_in_progress' && txn.to_location === 'finished_goods'
          ).length,
          volume: recentTransactions
            .filter(txn => txn.from_location === 'work_in_progress' && txn.to_location === 'finished_goods')
            .reduce((sum, txn) => sum + parseFloat(txn.qty || 0), 0)
        },
        {
          flow: "Finished → Warehouse",
          description: "Storage Allocation",
          count: recentTransactions.filter(txn =>
            txn.from_location === 'finished_goods' &&
            (txn.to_location === 'warehouse_a' || txn.to_location === 'warehouse_b')
          ).length,
          volume: recentTransactions
            .filter(txn => txn.from_location === 'finished_goods' &&
              (txn.to_location === 'warehouse_a' || txn.to_location === 'warehouse_b'))
            .reduce((sum, txn) => sum + parseFloat(txn.qty || 0), 0)
        },
        {
          flow: "Warehouse → Shipped",
          description: "Customer Shipments",
          count: recentTransactions.filter(txn =>
            (txn.from_location === 'warehouse_a' || txn.from_location === 'warehouse_b') &&
            txn.transaction_type === 'outbound'
          ).length,
          volume: recentTransactions
            .filter(txn => (txn.from_location === 'warehouse_a' || txn.from_location === 'warehouse_b') &&
              txn.transaction_type === 'outbound')
            .reduce((sum, txn) => sum + parseFloat(txn.qty || 0), 0)
        }
      ]

      // Location Alerts (Industrial ERP Standards)
      const locationAlerts = locationUtilization
        .filter(loc => loc.status === 'critical' || loc.status === 'warning')
        .map(loc => ({
          type: loc.status === 'critical' ? 'error' : 'warning',
          location: loc.name,
          message: loc.status === 'critical'
            ? `${loc.name} at ${loc.utilization}% capacity (Critical)`
            : `${loc.name} at ${loc.utilization}% capacity (Warning)`,
          action: loc.status === 'critical'
            ? 'Immediate action required'
            : 'Consider redistribution'
        }))

      // Quality distribution
      const qualityStats = stockData.reduce((acc, lot) => {
        const status = lot.quality_status || 'pending'
        acc[status] = (acc[status] || 0) + 1
        return acc
      }, {})

      const qualityDistribution = Object.entries(qualityStats).map(([status, count]) => ({
        status,
        count: count as number,
        percentage: Math.round((count as number / stockData.length) * 100)
      }))

      // Top products by quantity
      const productStats = stockData.reduce((acc, lot) => {
        const productName = lot.product?.name || 'Unknown Product'
        acc[productName] = (acc[productName] || 0) + parseFloat(lot.qty)
        return acc
      }, {})

      const topProducts = Object.entries(productStats)
        .sort(([, a], [, b]) => (b as number) - (a as number))
        .slice(0, 5)
        .map(([name, qty]) => ({ name, qty: qty as number }))

      setAnalytics({
        locationDistribution,
        qualityDistribution,
        topProducts,
        locationUtilization,
        flowMetrics,
        locationAlerts
      })
    } catch (error) {
      console.error('Error loading analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadAnalytics()
  }, [])

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t('inventory.analytics.stock_analytics')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Location Alerts */}
      {analytics.locationAlerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              {t('inventory.analytics.location_alerts')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {analytics.locationAlerts.map((alert, index) => (
                <div key={index} className={`p-3 rounded-lg border ${alert.type === 'error' ? 'bg-red-50 border-red-200' : 'bg-yellow-50 border-yellow-200'
                  }`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {alert.type === 'error' ?
                        <XCircle className="h-4 w-4 text-red-500" /> :
                        <AlertTriangle className="h-4 w-4 text-yellow-500" />
                      }
                      <span className="text-sm font-medium">{alert.message}</span>
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">{alert.action}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Location Utilization */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            {t('inventory.analytics.location_utilization')}
          </CardTitle>
          <CardDescription>{t('inventory.analytics.industrial_capacity')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analytics.locationUtilization.map((location) => (
              <div key={location.location} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-lg">{location.icon}</span>
                    <div>
                      <p className="text-sm font-medium">{location.name}</p>
                      <p className="text-xs text-muted-foreground capitalize">{location.type}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-mono">{location.current}/{location.capacity}</p>
                    <p className={`text-xs ${location.status === 'critical' ? 'text-red-600' :
                      location.status === 'warning' ? 'text-yellow-600' :
                        location.status === 'good' ? 'text-green-600' : 'text-gray-600'
                      }`}>
                      {location.utilization}%
                    </p>
                  </div>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${location.status === 'critical' ? 'bg-red-500' :
                      location.status === 'warning' ? 'bg-yellow-500' :
                        location.status === 'good' ? 'bg-green-500' : 'bg-gray-400'
                      }`}
                    style={{ width: `${Math.min(location.utilization, 100)}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Manufacturing Flow Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ArrowLeftRight className="h-5 w-5" />
            {t('inventory.analytics.flow_metrics_24h')}
          </CardTitle>
          <CardDescription>{t('inventory.analytics.workflow_analysis')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.flowMetrics.map((flow, index) => (
              <div key={index} className="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50">
                <div>
                  <p className="text-sm font-medium">{flow.flow}</p>
                  <p className="text-xs text-muted-foreground">{flow.description}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-mono">{flow.volume} units</p>
                  <p className="text-xs text-muted-foreground">{flow.count} transactions</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Traditional Analytics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            {t('inventory.analytics.stock_distribution')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Location Distribution */}
          <div>
            <h4 className="text-sm font-medium mb-3">{t('inventory.analytics.stock_by_location')}</h4>
            <div className="space-y-2">
              {analytics.locationDistribution.map((item) => (
                <div key={item.location} className="flex items-center justify-between">
                  <span className="text-sm capitalize">{item.location}</span>
                  <div className="flex items-center gap-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${item.percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-mono w-12 text-right">{item.qty}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Quality Distribution */}
          <div>
            <h4 className="text-sm font-medium mb-3">{t('inventory.analytics.quality_status')}</h4>
            <div className="space-y-2">
              {analytics.qualityDistribution.map((item) => (
                <div key={item.status} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant={
                      item.status === 'approved' ? 'default' :
                        item.status === 'pending' ? 'secondary' :
                          item.status === 'quarantined' ? 'destructive' : 'outline'
                    }>
                      {item.status}
                    </Badge>
                  </div>
                  <span className="text-sm font-mono">{item.count} lots</span>
                </div>
              ))}
            </div>
          </div>

          {/* Top Products */}
          <div>
            <h4 className="text-sm font-medium mb-3">{t('inventory.analytics.top_products_by_quantity')}</h4>
            <div className="space-y-2">
              {analytics.topProducts.map((item, index) => (
                <div key={item.name} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-xs bg-gray-100 rounded-full w-5 h-5 flex items-center justify-center">
                      {index + 1}
                    </span>
                    <span className="text-sm">{item.name}</span>
                  </div>
                  <span className="text-sm font-mono">{item.qty}</span>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Recent Activity Component
function RecentActivity() {
  const { t } = useI18n()
  const [recentTransactions, setRecentTransactions] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  // Helper function to format location names
  const formatLocationName = (locationId: string | null | undefined): string => {
    if (!locationId) return 'Unknown location'
    const loc = getLocationForUI(locationId)
    return loc ? `${loc.icon} ${loc.displayName}` : locationId
  }

  async function loadRecentActivity() {
    try {
      const response = await fetch("/api/inventory/transactions?limit=8").then(res => res.json())
      const data = Array.isArray(response) ? response : (response?.data || [])
      setRecentTransactions(data)
    } catch (error) {
      console.error('Error loading recent activity:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadRecentActivity()
    // Refresh every 60 seconds
    const interval = setInterval(loadRecentActivity, 60000)
    return () => clearInterval(interval)
  }, [])

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-3">
                <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-1">
                  <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-2 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          {t('inventory.recent_activity.title')}
        </CardTitle>
        <CardDescription>{t('inventory.recent_activity.subtitle')}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {recentTransactions.length === 0 ? (
            <p className="text-sm text-muted-foreground text-center py-4">
              {t('inventory.recent_activity.no_activity')}
            </p>
          ) : (
            recentTransactions.map((txn) => (
              <div key={txn.id} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50">
                <div className={`h-8 w-8 rounded-full flex items-center justify-center ${txn.transaction_type === 'inbound' || txn.type === 'inbound' ? 'bg-green-100' :
                  txn.transaction_type === 'outbound' || txn.type === 'outbound' ? 'bg-blue-100' :
                    txn.transaction_type === 'transfer' ? 'bg-purple-100' :
                      txn.transaction_type === 'adjustment' ? 'bg-orange-100' : 'bg-gray-100'
                  }`}>
                  {txn.transaction_type === 'inbound' || txn.type === 'inbound' ?
                    <ArrowDown className="h-4 w-4 text-green-600" /> :
                    txn.transaction_type === 'outbound' || txn.type === 'outbound' ?
                      <ArrowUp className="h-4 w-4 text-blue-600" /> :
                      txn.transaction_type === 'transfer' ?
                        <ArrowLeftRight className="h-4 w-4 text-purple-600" /> :
                        txn.transaction_type === 'adjustment' ?
                          <Settings className="h-4 w-4 text-orange-600" /> :
                          <Package className="h-4 w-4 text-gray-600" />
                  }
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium truncate">
                      {txn.product?.name || 'Unknown Product'}
                    </p>
                    <span className="text-xs text-muted-foreground">
                      {new Date(txn.created_at).toLocaleTimeString()}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <p className="text-xs text-muted-foreground">
                      {txn.transaction_type === 'transfer' && txn.from_location && txn.to_location ?
                        `${formatLocationName(txn.from_location)} → ${formatLocationName(txn.to_location)}` :
                        formatLocationName(txn.location || txn.from_location || txn.to_location)
                      }
                    </p>
                    <span className="text-xs font-mono">
                      {txn.qty} {txn.product?.unit || 'units'}
                    </span>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {recentTransactions.length > 0 && (
          <div className="mt-4 pt-3 border-t">
            <Button variant="ghost" size="sm" className="w-full">
              <Eye className="h-4 w-4 mr-2" />
              View All Transactions
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Quick Action Dialog Components
function QuickInboundDialog({ open, onOpenChange }: { open: boolean; onOpenChange: (open: boolean) => void }) {
  const { t } = useI18n()
  const [products, setProducts] = useState<any[]>([])
  const [locations, setLocations] = useState<DatabaseLocation[]>([])
  const [loading, setLoading] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [form, setForm] = useState({
    product_id: "",
    qty: 100,
    location: "", // Will be set to first available location
    reason_code: "receipt",
    reference: "",
    notes: ""
  })

  // Load locations when dialog opens
  useEffect(() => {
    if (open) {
      loadLocations()
    }
  }, [open])

  async function loadLocations() {
    const fetchedLocations = await fetchLocationsFromAPI()
    setLocations(fetchedLocations)
    // Set default location to first finished goods or warehouse location
    const defaultLocation = fetchedLocations.find(loc =>
      loc.type === 'finished_goods' || loc.type === 'warehouse'
    )
    if (defaultLocation && !form.location) {
      setForm(prev => ({ ...prev, location: defaultLocation.id }))
    }
  }

  // Get locations for dropdown
  const locationOptions = getLocationsForDropdown(locations)

  // Smart location suggestion based on product type and industrial ERP logic
  function getSmartLocationSuggestion(productName: string) {
    const name = productName.toLowerCase()
    if (name.includes('raw') || name.includes('material')) {
      return 'rm_building_a' // Raw Materials - Building A
    }
    if (name.includes('finished') || name.includes('complete')) {
      return 'fg_main_warehouse' // Main Finished Goods Warehouse
    }
    // Default to main finished goods warehouse for most products
    return 'fg_main_warehouse'
  }

  const reasonCodes = [
    { value: "receipt", label: "Purchase Receipt" },
    { value: "return", label: "Customer Return" },
    { value: "adjustment", label: "Inventory Adjustment" }
  ]

  async function loadProducts() {
    if (products.length > 0) return // Already loaded

    try {
      setLoading(true)
      const response = await fetch("/api/products").then(res => res.json())
      const productsData = Array.isArray(response) ? response : (response?.data || [])
      setProducts(productsData)
    } catch (error) {
      console.error('Error loading products:', error)
    } finally {
      setLoading(false)
    }
  }

  async function handleSubmit() {
    if (!form.product_id || !form.qty || !form.location) return

    setSubmitting(true)
    try {
      const response = await fetch("/api/inventory/transactions/inbound", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          transaction_type: "inbound",
          product_id: form.product_id,
          qty: form.qty,
          to_location: form.location,
          reference: form.reference || undefined,
          reason_code: form.reason_code,
          notes: form.notes || undefined
        })
      })

      if (response.ok) {
        // Reset form and close dialog
        setForm({
          product_id: "",
          qty: 100,
          location: "fg_main_warehouse",
          reason_code: "receipt",
          reference: "",
          notes: ""
        })
        onOpenChange(false)

        // Refresh the page data
        window.location.reload()
      } else {
        const errorData = await response.json()
        alert(`Inbound failed: ${errorData.message || errorData.error}`)
      }
    } catch (error) {
      console.error("Inbound error:", error)
      alert("Network error occurred")
    } finally {
      setSubmitting(false)
    }
  }

  // Load products when dialog opens
  useEffect(() => {
    if (open) {
      loadProducts()
    }
  }, [open])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ArrowDown className="h-5 w-5 text-green-600" />
            {t('inventory.dialogs.receive.title')}
          </DialogTitle>
          <DialogDescription>
            {t('inventory.dialogs.receive.description')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="grid gap-2">
            <Label>{t('inventory.dialogs.product')}</Label>
            <Select value={form.product_id} onValueChange={(v) => setForm({ ...form, product_id: v })}>
              <SelectTrigger>
                <SelectValue placeholder={loading ? t('inventory.dialogs.loading') : t('inventory.dialogs.select_product')} />
              </SelectTrigger>
              <SelectContent>
                {products.map((p) => (
                  <SelectItem key={p.id} value={p.id}>
                    {p.sku} - {p.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label>{t('inventory.dialogs.quantity')}</Label>
              <Input
                type="number"
                value={form.qty}
                onChange={(e) => setForm({ ...form, qty: Number(e.target.value) })}
                min="1"
              />
            </div>
            <div className="grid gap-2">
              <Label>{t('inventory.dialogs.location')}</Label>
              <Select value={form.location} onValueChange={(v) => setForm({ ...form, location: v })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {locationOptions.map((loc) => (
                    <SelectItem key={loc.value} value={loc.value}>
                      <div className="flex flex-col">
                        <span>{loc.label}</span>
                        <span className="text-xs text-muted-foreground">{loc.description}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                💡 {locations.find(l => l.value === form.location)?.description || 'Professional location management'}
              </p>
              {/* ✅ CAPACITY VALIDATION */}
              <CapacityValidation
                locationId={form.location}
                quantity={form.qty}
              />
            </div>
          </div>

          <div className="grid gap-2">
            <Label>{t('inventory.dialogs.reason')}</Label>
            <Select value={form.reason_code} onValueChange={(v) => setForm({ ...form, reason_code: v })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {reasonCodes.map((reason) => (
                  <SelectItem key={reason.value} value={reason.value}>
                    {reason.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-2">
            <Label>{t('inventory.dialogs.reference')}</Label>
            <Input
              value={form.reference}
              onChange={(e) => setForm({ ...form, reference: e.target.value })}
              placeholder={t('inventory.dialogs.po_number')}
            />
          </div>

          <div className="grid gap-2">
            <Label>{t('inventory.dialogs.notes')}</Label>
            <Input
              value={form.notes}
              onChange={(e) => setForm({ ...form, notes: e.target.value })}
              placeholder={t('inventory.dialogs.additional_notes')}
            />
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              {t('inventory.dialogs.cancel')}
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={submitting || !form.product_id || !form.qty}
              className="bg-green-600 hover:bg-green-700"
            >
              {submitting ? t('inventory.dialogs.processing') : t('inventory.dialogs.receive.button')}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

function QuickOutboundDialog({ open, onOpenChange }: { open: boolean; onOpenChange: (open: boolean) => void }) {
  const { t } = useI18n()
  const [stockLots, setStockLots] = useState<any[]>([])
  const [products, setProducts] = useState<any[]>([])
  const [locations, setLocations] = useState<DatabaseLocation[]>([])
  const [loading, setLoading] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [form, setForm] = useState({
    product_id: "",
    qty: 1,
    location: "any",
    reason_code: "shipment",
    reference: "",
    notes: ""
  })
  const [availableStock, setAvailableStock] = useState(0)

  // Load locations and products when dialog opens
  useEffect(() => {
    if (open) {
      loadLocations()
      loadProducts()
      loadStockLots()
    }
  }, [open])

  async function loadLocations() {
    const fetchedLocations = await fetchLocationsFromAPI()
    setLocations(fetchedLocations)
  }

  async function loadProducts() {
    try {
      const response = await fetch("/api/products").then(res => res.json())
      const productsData = Array.isArray(response) ? response : (response?.data || [])
      setProducts(productsData)
    } catch (error) {
      console.error('Error loading products:', error)
    }
  }

  // Get locations for dropdown
  const locationOptions = getLocationsForDropdown(locations)

  // Get products with available stock
  const productsWithStock = stockLots.length === 0
    ? products // Show all products while loading stock lots
    : products.filter(product => {
      return stockLots.some(lot => lot.product_id === product.id && parseFloat(lot.qty) > 0)
    })

  // Get locations that have stock for the selected product
  const locationsForProduct = form.product_id
    ? stockLots
      .filter(lot => lot.product_id === form.product_id && parseFloat(lot.qty) > 0)
      .map(lot => lot.location)
      .filter((location, index, self) => self.indexOf(location) === index) // Remove duplicates
    : []

  const reasonCodes = [
    { value: "shipment", label: "Customer Shipment" },
    { value: "sample", label: "Sample Request" },
    { value: "transfer", label: "Internal Transfer" },
    { value: "adjustment", label: "Inventory Adjustment" }
  ]

  async function loadStockLots() {
    if (stockLots.length > 0) return // Already loaded

    try {
      setLoading(true)
      const response = await fetch("/api/inventory/lots").then(res => res.json())
      const lotsData = Array.isArray(response) ? response : (response?.data || [])
      setStockLots(lotsData.filter(lot => parseFloat(lot.qty) > 0)) // Only lots with stock
    } catch (error) {
      console.error('Error loading stock lots:', error)
    } finally {
      setLoading(false)
    }
  }

  // Calculate available stock when product/location changes
  useEffect(() => {
    if (form.product_id && form.location && form.location !== "any") {
      const available = stockLots
        .filter(lot => lot.product_id === form.product_id && lot.location === form.location)
        .reduce((sum, lot) => sum + parseFloat(lot.qty), 0)
      setAvailableStock(available)
    } else if (form.product_id) {
      const available = stockLots
        .filter(lot => lot.product_id === form.product_id)
        .reduce((sum, lot) => sum + parseFloat(lot.qty), 0)
      setAvailableStock(available)
    } else {
      setAvailableStock(0)
    }
  }, [form.product_id, form.location, stockLots])

  async function handleSubmit() {
    if (!form.product_id || !form.qty || form.qty > availableStock) return

    setSubmitting(true)
    try {
      const response = await fetch("/api/inventory/transactions/outbound", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          transaction_type: "outbound",
          product_id: form.product_id,
          qty: form.qty,
          from_location: (form.location && form.location !== "any") ? form.location : undefined,
          reference: form.reference || undefined,
          reason_code: form.reason_code,
          notes: form.notes || undefined
        })
      })

      if (response.ok) {
        // Reset form and close dialog
        setForm({
          product_id: "",
          qty: 1,
          location: "any",
          reason_code: "shipment",
          reference: "",
          notes: ""
        })
        onOpenChange(false)

        // Refresh the page data
        window.location.reload()
      } else {
        const errorData = await response.json()
        alert(`Outbound failed: ${errorData.message || errorData.error}`)
      }
    } catch (error) {
      console.error("Outbound error:", error)
      alert("Network error occurred")
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ArrowUp className="h-5 w-5 text-blue-600" />
            {t('inventory.dialogs.ship.title')}
          </DialogTitle>
          <DialogDescription>
            {t('inventory.dialogs.ship.description')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="grid gap-2">
            <Label>{t('inventory.dialogs.product')}</Label>
            <Select value={form.product_id} onValueChange={(v) => setForm({ ...form, product_id: v, location: "any" })}>
              <SelectTrigger>
                <SelectValue placeholder={loading ? t('inventory.dialogs.loading') : t('inventory.dialogs.select_product_with_stock')} />
              </SelectTrigger>
              <SelectContent>
                {productsWithStock.map((p) => (
                  <SelectItem key={p.id} value={p.id}>
                    {p.sku} - {p.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {form.product_id && (
            <div className="grid gap-2">
              <Label>{t('inventory.dialogs.location_optional')}</Label>
              <Select value={form.location} onValueChange={(v) => setForm({ ...form, location: v })}>
                <SelectTrigger>
                  <SelectValue placeholder={t('inventory.dialogs.any_location')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="any">{t('inventory.dialogs.any_location')}</SelectItem>
                  {locationsForProduct.map((loc) => {
                    // Find the location object to get proper label
                    const locationObj = locationOptions.find(l => l.value === loc)
                    return (
                      <SelectItem key={loc} value={loc}>
                        {locationObj ? locationObj.label : loc.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </SelectItem>
                    )
                  })}
                </SelectContent>
              </Select>
            </div>
          )}

          <div className="grid gap-2">
            <Label>{t('inventory.dialogs.quantity')}</Label>
            <Input
              type="number"
              value={form.qty}
              onChange={(e) => setForm({ ...form, qty: Number(e.target.value) })}
              min="1"
              max={availableStock}
            />
            {availableStock > 0 && (
              <p className="text-xs text-muted-foreground">
                {t('inventory.dialogs.available')}: {availableStock} units
              </p>
            )}
            {form.qty > availableStock && (
              <p className="text-xs text-red-600">
                Insufficient stock available
              </p>
            )}
          </div>

          <div className="grid gap-2">
            <Label>{t('inventory.dialogs.reason')}</Label>
            <Select value={form.reason_code} onValueChange={(v) => setForm({ ...form, reason_code: v })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {reasonCodes.map((reason) => (
                  <SelectItem key={reason.value} value={reason.value}>
                    {reason.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-2">
            <Label>{t('inventory.dialogs.reference')}</Label>
            <Input
              value={form.reference}
              onChange={(e) => setForm({ ...form, reference: e.target.value })}
              placeholder={t('inventory.dialogs.order_number')}
            />
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              {t('inventory.dialogs.cancel')}
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={submitting || !form.product_id || !form.qty || form.qty > availableStock}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {submitting ? t('inventory.dialogs.processing') : t('inventory.dialogs.ship.button')}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

function QuickTransferDialog({ open, onOpenChange }: { open: boolean; onOpenChange: (open: boolean) => void }) {
  const { t } = useI18n()
  const [stockLots, setStockLots] = useState<any[]>([])
  const [products, setProducts] = useState<any[]>([])
  const [locations, setLocations] = useState<DatabaseLocation[]>([])
  const [loading, setLoading] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [form, setForm] = useState({
    product_id: "",
    qty: 1,
    from_location: "",
    to_location: "",
    notes: ""
  })
  const [availableStock, setAvailableStock] = useState(0)

  // Load locations and products when dialog opens
  useEffect(() => {
    if (open) {
      loadLocations()
      loadProducts()
      loadStockLots()
    }
  }, [open])

  async function loadLocations() {
    const fetchedLocations = await fetchLocationsFromAPI()
    setLocations(fetchedLocations)
  }

  async function loadProducts() {
    try {
      const response = await fetch("/api/products").then(res => res.json())
      const productsData = Array.isArray(response) ? response : (response?.data || [])
      setProducts(productsData)
    } catch (error) {
      console.error('Error loading products:', error)
    }
  }

  // Get locations for dropdown
  const locationOptions = getLocationsForDropdown(locations)

  // Get products with stock for transfer
  const productsWithStock = stockLots.length === 0
    ? products // Show all products while loading stock lots
    : products.filter(product => {
      // Check if this product has stock in any location
      return stockLots.some(lot => lot.product_id === product.id && parseFloat(lot.qty) > 0)
    })

  // Get locations that have stock for the selected product
  const locationsForProduct = form.product_id
    ? stockLots
      .filter(lot => lot.product_id === form.product_id && parseFloat(lot.qty) > 0)
      .map(lot => lot.location)
      .filter((location, index, self) => self.indexOf(location) === index) // Remove duplicates
    : []

  async function loadStockLots() {
    if (stockLots.length > 0) return

    try {
      setLoading(true)
      const response = await fetch("/api/inventory/lots").then(res => res.json())
      const lotsData = Array.isArray(response) ? response : (response?.data || [])
      setStockLots(lotsData.filter(lot => parseFloat(lot.qty) > 0))
    } catch (error) {
      console.error('Error loading stock lots:', error)
    } finally {
      setLoading(false)
    }
  }

  // Calculate available stock for transfer
  useEffect(() => {
    if (form.product_id && form.from_location) {
      const available = stockLots
        .filter(lot => lot.product_id === form.product_id && lot.location === form.from_location)
        .reduce((sum, lot) => sum + parseFloat(lot.qty), 0)
      setAvailableStock(available)
    } else {
      setAvailableStock(0)
    }
  }, [form.product_id, form.from_location, stockLots])

  async function handleSubmit() {
    if (!form.product_id || !form.qty || !form.from_location || !form.to_location || form.from_location === form.to_location) return

    setSubmitting(true)
    try {
      const response = await fetch("/api/inventory/transactions/transfer", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          transaction_type: "transfer",
          product_id: form.product_id,
          qty: form.qty,
          from_location: form.from_location,
          to_location: form.to_location,
          notes: form.notes || undefined
        })
      })

      if (response.ok) {
        setForm({
          product_id: "",
          qty: 1,
          from_location: "",
          to_location: "",
          notes: ""
        })
        onOpenChange(false)
        window.location.reload()
      } else {
        const errorData = await response.json()
        alert(`Transfer failed: ${errorData.message || errorData.error}`)
      }
    } catch (error) {
      console.error("Transfer error:", error)
      alert("Network error occurred")
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ArrowLeftRight className="h-5 w-5 text-purple-600" />
            {t('inventory.dialogs.transfer.title')}
          </DialogTitle>
          <DialogDescription>
            {t('inventory.dialogs.transfer.description')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="grid gap-2">
            <Label>{t('inventory.dialogs.product')}</Label>
            <Select value={form.product_id} onValueChange={(v) => setForm({ ...form, product_id: v, from_location: "", to_location: "" })}>
              <SelectTrigger>
                <SelectValue placeholder={loading ? t('inventory.dialogs.loading') : t('inventory.dialogs.select_product')} />
              </SelectTrigger>
              <SelectContent>
                {productsWithStock.map((p) => (
                  <SelectItem key={p.id} value={p.id}>
                    {p.sku} - {p.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label>{t('inventory.dialogs.from_location')}</Label>
              <Select value={form.from_location} onValueChange={(v) => setForm({ ...form, from_location: v })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select source" />
                </SelectTrigger>
                <SelectContent>
                  {/* ✅ FIXED: Show all locations with consistent formatting */}
                  {(locationsForProduct.length > 0 ? locationsForProduct : locationOptions.map(l => l.value)).map((loc) => {
                    // Find the location object to get proper label
                    const locationObj = locationOptions.find(l => l.value === loc)
                    return (
                      <SelectItem key={loc} value={loc}>
                        {locationObj ? locationObj.label : loc.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </SelectItem>
                    )
                  })}
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label>{t('inventory.dialogs.to_location')}</Label>
              <Select value={form.to_location} onValueChange={(v) => setForm({ ...form, to_location: v })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select destination" />
                </SelectTrigger>
                <SelectContent>
                  {locationOptions
                    .filter(loc => loc.value !== form.from_location)
                    .map((loc) => (
                      <SelectItem key={loc.value} value={loc.value}>
                        {loc.label}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid gap-2">
            <Label>{t('inventory.dialogs.quantity')}</Label>
            <Input
              type="number"
              value={form.qty}
              onChange={(e) => setForm({ ...form, qty: Number(e.target.value) })}
              min="1"
              max={availableStock}
            />
            {availableStock > 0 && (
              <p className="text-xs text-muted-foreground">
                {t('inventory.dialogs.available')} at source: {availableStock} units
              </p>
            )}
          </div>

          <div className="grid gap-2">
            <Label>{t('inventory.dialogs.notes')}</Label>
            <Input
              value={form.notes}
              onChange={(e) => setForm({ ...form, notes: e.target.value })}
              placeholder={t('inventory.dialogs.transfer_reason')}
            />
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              {t('inventory.dialogs.cancel')}
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={submitting || !form.product_id || !form.qty || !form.from_location || !form.to_location || form.from_location === form.to_location}
              className="bg-purple-600 hover:bg-purple-700"
            >
              {submitting ? t('inventory.dialogs.processing') : t('inventory.dialogs.transfer.button')}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

function QuickAdjustmentDialog({ open, onOpenChange }: { open: boolean; onOpenChange: (open: boolean) => void }) {
  const { t } = useI18n()
  const [products, setProducts] = useState<any[]>([])
  const [locations, setLocations] = useState<DatabaseLocation[]>([])
  const [loading, setLoading] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [form, setForm] = useState({
    product_id: "",
    qty: 0,
    location: "", // Will be set to first available location
    reason_code: "cycle_count",
    notes: ""
  })

  // Load locations and products when dialog opens
  useEffect(() => {
    if (open) {
      loadLocations()
      loadProducts()
    }
  }, [open])

  async function loadLocations() {
    const fetchedLocations = await fetchLocationsFromAPI()
    setLocations(fetchedLocations)
    // Set default location to first finished goods or warehouse location
    const defaultLocation = fetchedLocations.find(loc =>
      loc.type === 'finished_goods' || loc.type === 'warehouse'
    )
    if (defaultLocation && !form.location) {
      setForm(prev => ({ ...prev, location: defaultLocation.id }))
    }
  }

  // Get locations for dropdown
  const locationOptions = getLocationsForDropdown(locations)

  const reasonCodes = [
    { value: "cycle_count", label: "Cycle Count Adjustment" },
    { value: "damage", label: "Damaged Goods" },
    { value: "obsolete", label: "Obsolete Inventory" },
    { value: "found", label: "Found Inventory" },
    { value: "other", label: "Other Adjustment" }
  ]

  async function loadProducts() {
    if (products.length > 0) return

    try {
      setLoading(true)
      const response = await fetch("/api/products").then(res => res.json())
      const productsData = Array.isArray(response) ? response : (response?.data || [])
      setProducts(productsData)
    } catch (error) {
      console.error('Error loading products:', error)
    } finally {
      setLoading(false)
    }
  }

  async function handleSubmit() {
    if (!form.product_id || form.qty === 0 || !form.reason_code || !form.notes.trim()) return

    setSubmitting(true)
    try {
      const response = await fetch("/api/inventory/transactions/adjustment", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          transaction_type: "adjustment",
          product_id: form.product_id,
          qty: form.qty,
          location: form.location,
          reason_code: form.reason_code,
          notes: form.notes
        })
      })

      if (response.ok) {
        setForm({
          product_id: "",
          qty: 0,
          location: "finished_goods",
          reason_code: "cycle_count",
          notes: ""
        })
        onOpenChange(false)
        window.location.reload()
      } else {
        const errorData = await response.json()
        alert(`Adjustment failed: ${errorData.message || errorData.error}`)
      }
    } catch (error) {
      console.error("Adjustment error:", error)
      alert("Network error occurred")
    } finally {
      setSubmitting(false)
    }
  }

  useEffect(() => {
    if (open) {
      loadProducts()
    }
  }, [open])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-orange-600" />
            {t('inventory.dialogs.adjust.title')}
          </DialogTitle>
          <DialogDescription>
            {t('inventory.dialogs.adjust.description')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="grid gap-2">
            <Label>{t('inventory.dialogs.product')}</Label>
            <Select value={form.product_id} onValueChange={(v) => setForm({ ...form, product_id: v })}>
              <SelectTrigger>
                <SelectValue placeholder={loading ? t('inventory.dialogs.loading') : t('inventory.dialogs.select_product')} />
              </SelectTrigger>
              <SelectContent>
                {products.map((p) => (
                  <SelectItem key={p.id} value={p.id}>
                    {p.sku} - {p.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label>{t('inventory.dialogs.quantity')}</Label>
              <Input
                type="number"
                value={form.qty}
                onChange={(e) => setForm({ ...form, qty: Number(e.target.value) })}
                placeholder="±0"
              />
              <p className="text-xs text-muted-foreground">
                Positive = Add, Negative = Remove
              </p>
            </div>
            <div className="grid gap-2">
              <Label>{t('inventory.dialogs.location')}</Label>
              <Select value={form.location} onValueChange={(v) => setForm({ ...form, location: v })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {locationOptions.map((loc) => (
                    <SelectItem key={loc.value} value={loc.value}>
                      {loc.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid gap-2">
            <Label>{t('inventory.dialogs.reason')}</Label>
            <Select value={form.reason_code} onValueChange={(v) => setForm({ ...form, reason_code: v })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {reasonCodes.map((reason) => (
                  <SelectItem key={reason.value} value={reason.value}>
                    {reason.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-2">
            <Label>{t('inventory.dialogs.notes_required')}</Label>
            <Input
              value={form.notes}
              onChange={(e) => setForm({ ...form, notes: e.target.value })}
              placeholder={t('inventory.dialogs.adjustment_reason')}
              required
            />
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              {t('inventory.dialogs.cancel')}
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={submitting || !form.product_id || form.qty === 0 || !form.notes.trim()}
              className="bg-orange-600 hover:bg-orange-700"
            >
              {submitting ? t('inventory.dialogs.processing') : t('inventory.dialogs.adjust.button')}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

function InboundCard() {
  const { t } = useI18n()
  const [products, setProducts] = useState<any[]>([])
  const [recentInbound, setRecentInbound] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [form, setForm] = useState({
    product_id: "",
    qty: 100,
    location: "finished_goods",
    reference: "",
    reason_code: "receipt",
    note: ""
  })

  // ✅ FIXED: Use actual locations from your location directory
  const locations = getLocationsForDropdown()

  const reasonCodes = [
    { value: "receipt", label: "Purchase Receipt" },
    { value: "return", label: "Customer Return" },
    { value: "adjustment", label: "Inventory Adjustment" }
  ]

  async function refresh() {
    try {
      console.log("🔄 Refreshing inbound data...")

      const [productsResponse, inboundResponse] = await Promise.all([
        fetch("/api/products").then(res => res.json()),
        fetch("/api/inventory/transactions?transaction_type=inbound&limit=5").then(res => res.json())
      ])

      console.log("📦 Products response:", productsResponse)
      console.log("📥 Inbound response:", inboundResponse)

      // Handle different response formats
      const productsData = Array.isArray(productsResponse) ? productsResponse : (productsResponse?.data || [])
      const inboundData = Array.isArray(inboundResponse) ? inboundResponse : (inboundResponse?.data || [])

      setProducts(productsData)
      setRecentInbound(inboundData)

      console.log("✅ Loaded products:", productsData.length)
      console.log("✅ Loaded inbound transactions:", inboundData.length)
    } catch (err) {
      console.error('❌ Error refreshing inbound data:', err)
      setError('Failed to load inbound data')
    }
  }

  useEffect(() => {
    refresh()
  }, [])

  async function add() {
    if (!form.product_id || !form.location || !form.qty) {
      setError("Please fill all required fields")
      return
    }

    setLoading(true)
    setError(null)

    try {
      const requestData = {
        transaction_type: "inbound",
        product_id: form.product_id,
        qty: form.qty,
        to_location: form.location,
        reference: form.reference || undefined,
        reason_code: form.reason_code,
        notes: form.note || undefined
      }

      console.log("📤 Sending inbound transaction request:", requestData)

      const response = await fetch("/api/inventory/transactions/inbound", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestData)
      })

      console.log("📥 Inbound transaction response status:", response.status)

      if (response.ok) {
        const result = await response.json()
        console.log("✅ Inbound transaction success:", result)

        setForm({
          product_id: "",
          qty: 100,
          location: "finished_goods",
          reference: "",
          reason_code: "receipt",
          note: ""
        })
        await (window as any).refreshInventory?.()
        await refresh() // Refresh recent inbound list
      } else {
        const errorData = await response.json()
        console.error("❌ Inbound transaction error response:", errorData)
        setError(errorData.message || "Inbound transaction failed")
      }
    } catch (error) {
      console.error("❌ Inbound transaction network error:", error)
      setError("Network error occurred")
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>{t("inventory.inbound.title")}</span>
          <Badge variant="outline">{recentInbound.length} Recent</Badge>
        </CardTitle>
        <CardDescription>{t("inventory.inbound.desc")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Input Form */}
        <div className="grid gap-3 md:grid-cols-6">
          <div className="grid gap-1">
            <Label>{t("field.product")}</Label>
            <Select value={form.product_id} onValueChange={(v) => setForm({ ...form, product_id: v })}>
              <SelectTrigger>
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                {products.map((p) => (
                  <SelectItem key={p.id} value={p.id}>
                    {p.sku} - {p.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-1">
            <Label>{t("field.qty")}</Label>
            <Input type="number" value={form.qty} onChange={(e) => setForm({ ...form, qty: Number(e.target.value) })} />
          </div>
          <div className="grid gap-1">
            <Label>Location</Label>
            <Select value={form.location} onValueChange={(v) => setForm({ ...form, location: v })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {locationOptions.map((loc) => (
                  <SelectItem key={loc.value} value={loc.value}>
                    {loc.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-1">
            <Label>Reason</Label>
            <Select value={form.reason_code} onValueChange={(v) => setForm({ ...form, reason_code: v })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {reasonCodes.map((reason) => (
                  <SelectItem key={reason.value} value={reason.value}>
                    {reason.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-1">
            <Label>Reference</Label>
            <Input
              value={form.reference}
              onChange={(e) => setForm({ ...form, reference: e.target.value })}
              placeholder="PO Number"
            />
          </div>
          <div className="grid gap-1">
            <Label>Action</Label>
            <Button onClick={add} disabled={loading} className="w-full">
              {loading ? "Processing..." : t("action.addInbound")}
            </Button>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-sm text-red-800">{error}</p>
          </div>
        )}

        {/* Recent Inbound Transactions Table */}
        {recentInbound.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-muted-foreground">Recent Inbound Transactions</h4>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Reference</TableHead>
                  <TableHead>Time</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recentInbound.map((txn) => (
                  <TableRow key={txn.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{txn.product?.name || "N/A"}</div>
                        <div className="text-sm text-muted-foreground">{txn.product?.sku || txn.product_id}</div>
                        {txn.product?.description && (
                          <div className="text-xs text-muted-foreground">{txn.product.description}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-mono">
                        {txn.qty} {txn.product?.unit || "units"}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {txn.reason_code && `Reason: ${txn.reason_code}`}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {(() => {
                          const rawLoc = txn.to_location || txn.location
                          const loc = getLocationForUI(rawLoc)
                          return loc ? `${loc.icon} ${loc.displayName}` : rawLoc
                        })()}
                      </Badge>
                      {txn.workflow_trigger && txn.workflow_trigger !== "manual" && (
                        <div className="text-xs text-muted-foreground mt-1">
                          Auto: {txn.workflow_trigger}
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="text-sm">
                      <div>{txn.reference || "-"}</div>
                      {txn.notes && (
                        <div className="text-xs text-muted-foreground">{txn.notes}</div>
                      )}
                    </TableCell>
                    <TableCell className="text-xs text-muted-foreground">
                      {new Date(txn.created_at).toLocaleString()}
                      {txn.created_by && (
                        <div>By: {txn.created_by.slice(-8)}</div>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

function OutboundCard() {
  const { t } = useI18n()
  const [products, setProducts] = useState<any[]>([])
  const [availableStock, setAvailableStock] = useState<any[]>([])
  const [recentOutbound, setRecentOutbound] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [form, setForm] = useState({
    product_id: "",
    qty: 10,
    location: "finished_goods",
    reference: "",
    reason_code: "shipment"
  })

  // ✅ FIXED: Use actual locations from your location directory
  const locations = getLocationsForDropdown()

  const reasonCodes = [
    { value: "shipment", label: "Customer Shipment" },
    { value: "sample", label: "Sample Request" },
    { value: "transfer", label: "Internal Transfer" }
  ]

  async function refresh() {
    try {
      console.log("🔄 Refreshing outbound data...")

      const [productsResponse, stockResponse, outboundResponse] = await Promise.all([
        fetch("/api/products").then(res => res.json()),
        fetch("/api/inventory/lots?quality_status=approved").then(res => res.json()),
        fetch("/api/inventory/transactions?transaction_type=outbound&limit=5").then(res => res.json())
      ])

      console.log("📦 Products response:", productsResponse)
      console.log("📊 Stock response:", stockResponse)
      console.log("📤 Outbound response:", outboundResponse)

      // Handle different response formats
      const productsData = Array.isArray(productsResponse) ? productsResponse : (productsResponse?.data || [])
      const stockData = Array.isArray(stockResponse) ? stockResponse : (stockResponse?.data || [])
      const outboundData = Array.isArray(outboundResponse) ? outboundResponse : (outboundResponse?.data || [])

      setProducts(productsData)
      setAvailableStock(stockData)
      setRecentOutbound(outboundData)

      console.log("✅ Loaded products:", productsData.length)
      console.log("✅ Loaded stock:", stockData.length)
      console.log("✅ Loaded outbound transactions:", outboundData.length)
    } catch (err) {
      console.error('❌ Error refreshing outbound data:', err)
      setError('Failed to load outbound data')
    }
  }

  useEffect(() => {
    refresh()
  }, [])

  // Get available quantity for selected product and location
  const getAvailableQty = () => {
    if (!form.product_id || !form.location) return 0
    return availableStock
      .filter(lot => lot.product_id === form.product_id && lot.location === form.location && parseFloat(lot.qty) > 0)
      .reduce((sum, lot) => sum + parseFloat(lot.qty), 0)
  }

  async function ship() {
    if (!form.product_id || !form.qty || !form.location) {
      setError("Please fill all required fields")
      return
    }

    const availableQty = getAvailableQty()
    if (form.qty > availableQty) {
      setError(`Insufficient stock. Available: ${availableQty}, Requested: ${form.qty}`)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch("/api/inventory/transactions/outbound", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          transaction_type: "outbound",
          product_id: form.product_id,
          qty: form.qty,
          from_location: form.location,
          reference: form.reference,
          reason_code: form.reason_code,
          notes: form.reference
        })
      })

      if (response.ok) {
        const result = await response.json()
        setForm({
          product_id: "",
          qty: 10,
          location: "finished_goods",
          reference: "",
          reason_code: "shipment"
        })
        await (window as any).refreshInventory?.()
        await refresh() // Refresh available stock and recent outbound
      } else {
        const errorData = await response.json()
        setError(errorData.message || "Outbound transaction failed")
      }
    } catch (error) {
      console.error("Outbound transaction error:", error)
      setError("Network error occurred")
    } finally {
      setLoading(false)
    }
  }

  const availableQty = getAvailableQty()
  const selectedProduct = products.find(p => p.id === form.product_id)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>{t("inventory.outbound.title")}</span>
          <div className="flex items-center gap-2">
            <Badge variant="outline">{recentOutbound.length} Recent</Badge>
            {form.product_id && form.location && (
              <Badge variant={availableQty > 0 ? "default" : "destructive"}>
                {availableQty} Available
              </Badge>
            )}
          </div>
        </CardTitle>
        <CardDescription>{t("inventory.outbound.desc")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Input Form */}
        <div className="grid gap-3 md:grid-cols-6">
          <div className="grid gap-1">
            <Label>{t("field.product")}</Label>
            <Select value={form.product_id} onValueChange={(v) => setForm({ ...form, product_id: v })}>
              <SelectTrigger>
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                {products.map((p) => (
                  <SelectItem key={p.id} value={p.id}>
                    {p.sku} - {p.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-1">
            <Label>{t("field.qty")}</Label>
            <Input
              type="number"
              value={form.qty}
              onChange={(e) => setForm({ ...form, qty: Number(e.target.value) })}
              max={availableQty}
            />
            {form.product_id && form.location && (
              <p className="text-xs text-muted-foreground">
                {t('inventory.dialogs.max')}: {availableQty} {selectedProduct?.unit}
              </p>
            )}
          </div>
          <div className="grid gap-1">
            <Label>From Location</Label>
            <Select value={form.location} onValueChange={(v) => setForm({ ...form, location: v })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {locations.map((loc) => (
                  <SelectItem key={loc.value} value={loc.value}>
                    {loc.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-1">
            <Label>Reason</Label>
            <Select value={form.reason_code} onValueChange={(v) => setForm({ ...form, reason_code: v })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {reasonCodes.map((reason) => (
                  <SelectItem key={reason.value} value={reason.value}>
                    {reason.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-1">
            <Label>Reference</Label>
            <Input
              value={form.reference}
              onChange={(e) => setForm({ ...form, reference: e.target.value })}
              placeholder="SO Number"
            />
          </div>
          <div className="grid gap-1">
            <Label>Action</Label>
            <Button
              onClick={ship}
              disabled={loading || availableQty < form.qty}
              className="w-full"
            >
              {loading ? "Processing..." : t("action.addOutbound")}
            </Button>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-sm text-red-800">{error}</p>
          </div>
        )}

        {/* Available Stock Summary */}
        {form.product_id && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <h4 className="text-sm font-medium text-blue-800 mb-2">Available Stock - {selectedProduct?.name}</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
              {locationOptions.map(loc => {
                const qty = availableStock
                  .filter(lot => lot.product_id === form.product_id && lot.location === loc.value && parseFloat(lot.qty) > 0)
                  .reduce((sum, lot) => sum + parseFloat(lot.qty), 0)
                return (
                  <div key={loc.value} className="flex justify-between">
                    <span>{loc.label}:</span>
                    <span className="font-mono">{qty} {selectedProduct?.unit}</span>
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {/* Recent Outbound Transactions Table */}
        {recentOutbound.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-muted-foreground">Recent Outbound Transactions</h4>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>From Location</TableHead>
                  <TableHead>Reference</TableHead>
                  <TableHead>Time</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recentOutbound.map((txn) => (
                  <TableRow key={txn.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{txn.product?.name || "N/A"}</div>
                        <div className="text-sm text-muted-foreground">{txn.product?.sku || txn.product_id}</div>
                        {txn.product?.description && (
                          <div className="text-xs text-muted-foreground">{txn.product.description}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-mono">
                        {txn.qty} {txn.product?.unit || "units"}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {txn.reason_code && `Reason: ${txn.reason_code}`}
                      </div>
                      {txn.affected_lots && (
                        <div className="text-xs text-blue-600">
                          FIFO: {txn.affected_lots.length} lots
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">
                        {(() => {
                          const rawLoc = txn.from_location || txn.location
                          const loc = getLocationForUI(rawLoc)
                          return loc ? `${loc.icon} ${loc.displayName}` : rawLoc
                        })()}
                      </Badge>
                      {txn.workflow_trigger && txn.workflow_trigger !== "manual" && (
                        <div className="text-xs text-muted-foreground mt-1">
                          Auto: {txn.workflow_trigger}
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="text-sm">
                      <div>{txn.reference || "-"}</div>
                      {txn.notes && (
                        <div className="text-xs text-muted-foreground">{txn.notes}</div>
                      )}
                    </TableCell>
                    <TableCell className="text-xs text-muted-foreground">
                      {new Date(txn.created_at).toLocaleString()}
                      {txn.created_by && (
                        <div>By: {txn.created_by.slice(-8)}</div>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

function TransferCard() {
  const { t } = useI18n()
  const [products, setProducts] = useState<any[]>([])
  const [availableStock, setAvailableStock] = useState<any[]>([])
  const [recentTransfers, setRecentTransfers] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [form, setForm] = useState({
    product_id: "",
    qty: 10,
    from_location: "finished_goods",
    to_location: "shipping",
    notes: ""
  })

  // ✅ FIXED: Use actual locations from your location directory
  const locations = getLocationsForDropdown()

  async function refresh() {
    try {
      console.log("🔄 Refreshing transfer data...")

      const [productsResponse, stockResponse, transfersResponse] = await Promise.all([
        fetch("/api/products").then(res => res.json()),
        fetch("/api/inventory/lots?quality_status=approved").then(res => res.json()),
        fetch("/api/inventory/transactions?transaction_type=transfer&limit=5").then(res => res.json())
      ])

      console.log("📦 Products response:", productsResponse)
      console.log("📊 Stock response:", stockResponse)
      console.log("🔄 Transfer response:", transfersResponse)

      // Handle different response formats
      const productsData = Array.isArray(productsResponse) ? productsResponse : (productsResponse?.data || [])
      const stockData = Array.isArray(stockResponse) ? stockResponse : (stockResponse?.data || [])
      const transfersData = Array.isArray(transfersResponse) ? transfersResponse : (transfersResponse?.data || [])

      setProducts(productsData)
      setAvailableStock(stockData)
      setRecentTransfers(transfersData)

      console.log("✅ Loaded products:", productsData.length)
      console.log("✅ Loaded stock:", stockData.length)
      console.log("✅ Loaded transfers:", transfersData.length)
    } catch (err) {
      console.error('❌ Error refreshing transfer data:', err)
      setError('Failed to load transfer data')
    }
  }

  useEffect(() => {
    refresh()
  }, [])

  // Get available quantity for selected product and from_location
  const getAvailableQty = () => {
    if (!form.product_id || !form.from_location) return 0
    return availableStock
      .filter(lot => lot.product_id === form.product_id && lot.location === form.from_location && parseFloat(lot.qty) > 0)
      .reduce((sum, lot) => sum + parseFloat(lot.qty), 0)
  }

  async function transfer() {
    if (!form.product_id || !form.qty || !form.from_location || !form.to_location) {
      setError("Please fill all required fields")
      return
    }

    if (form.from_location === form.to_location) {
      setError("Source and destination locations must be different")
      return
    }

    const availableQty = getAvailableQty()
    if (form.qty > availableQty) {
      setError(`Insufficient stock in ${form.from_location}. Available: ${availableQty}, Requested: ${form.qty}`)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch("/api/inventory/transactions/transfer", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          transaction_type: "transfer",
          product_id: form.product_id,
          qty: form.qty,
          from_location: form.from_location,
          to_location: form.to_location,
          reason_code: "transfer",
          notes: form.notes
        })
      })

      if (response.ok) {
        const result = await response.json()
        setForm({
          product_id: "",
          qty: 10,
          from_location: "finished_goods",
          to_location: "shipping",
          notes: ""
        })
        await (window as any).refreshInventory?.()
        await refresh()
      } else {
        const errorData = await response.json()
        setError(errorData.message || "Transfer transaction failed")
      }
    } catch (error) {
      console.error("Transfer transaction error:", error)
      setError("Network error occurred")
    } finally {
      setLoading(false)
    }
  }

  const availableQty = getAvailableQty()
  const selectedProduct = products.find(p => p.id === form.product_id)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Transfer Inventory</span>
          <div className="flex items-center gap-2">
            <Badge variant="outline">{recentTransfers.length} Recent</Badge>
            {form.product_id && form.from_location && (
              <Badge variant={availableQty > 0 ? "default" : "destructive"}>
                {availableQty} Available
              </Badge>
            )}
          </div>
        </CardTitle>
        <CardDescription>Move inventory between locations with validation</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Input Form */}
        <div className="grid gap-3 md:grid-cols-6">
          <div className="grid gap-1">
            <Label>{t("field.product")}</Label>
            <Select value={form.product_id} onValueChange={(v) => setForm({ ...form, product_id: v })}>
              <SelectTrigger>
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                {products.map((p) => (
                  <SelectItem key={p.id} value={p.id}>
                    {p.sku} - {p.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-1">
            <Label>{t("field.qty")}</Label>
            <Input
              type="number"
              value={form.qty}
              onChange={(e) => setForm({ ...form, qty: Number(e.target.value) })}
              max={availableQty}
            />
            {form.product_id && form.from_location && (
              <p className="text-xs text-muted-foreground">
                {t('inventory.dialogs.max')}: {availableQty} {selectedProduct?.unit}
              </p>
            )}
          </div>
          <div className="grid gap-1">
            <Label>{t('inventory.dialogs.from_location')}</Label>
            <Select value={form.from_location} onValueChange={(v) => setForm({ ...form, from_location: v })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {locationOptions.map((loc) => (
                  <SelectItem key={loc.value} value={loc.value}>
                    {loc.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-1">
            <Label>{t('inventory.dialogs.to_location')}</Label>
            <Select value={form.to_location} onValueChange={(v) => setForm({ ...form, to_location: v })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {locations.filter(loc => loc.value !== form.from_location).map((loc) => (
                  <SelectItem key={loc.value} value={loc.value}>
                    {loc.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-1">
            <Label>{t('inventory.dialogs.notes')}</Label>
            <Input
              value={form.notes}
              onChange={(e) => setForm({ ...form, notes: e.target.value })}
              placeholder={t('inventory.dialogs.transfer_reason')}
            />
          </div>
          <div className="grid gap-1">
            <Label>Action</Label>
            <Button
              onClick={transfer}
              disabled={loading || availableQty < form.qty || form.from_location === form.to_location}
              className="w-full"
            >
              {loading ? t('inventory.dialogs.processing') : t('inventory.dialogs.transfer.button')}
            </Button>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-sm text-red-800">{error}</p>
          </div>
        )}

        {/* Recent Transfer Transactions Table */}
        {recentTransfers.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-muted-foreground">Recent Transfer Transactions</h4>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Transfer Route</TableHead>
                  <TableHead>Notes</TableHead>
                  <TableHead>Time</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recentTransfers.map((txn) => (
                  <TableRow key={txn.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{txn.product?.name || "N/A"}</div>
                        <div className="text-sm text-muted-foreground">{txn.product?.sku || txn.product_id}</div>
                        {txn.product?.description && (
                          <div className="text-xs text-muted-foreground">{txn.product.description}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-mono">
                        {txn.qty} {txn.product?.unit || "units"}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {txn.reason_code && `Reason: ${txn.reason_code}`}
                      </div>
                      {txn.affected_lots && (
                        <div className="text-xs text-blue-600">
                          FIFO: {txn.affected_lots.length} lots affected
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1 text-sm">
                        <Badge variant="secondary">{txn.from_location}</Badge>
                        <span>→</span>
                        <Badge variant="outline">{txn.to_location}</Badge>
                      </div>
                      {txn.workflow_trigger && txn.workflow_trigger !== "manual" && (
                        <div className="text-xs text-muted-foreground mt-1">
                          Auto: {txn.workflow_trigger}
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="text-sm">
                      <div>{txn.notes || "-"}</div>
                      {txn.reference && (
                        <div className="text-xs text-muted-foreground">Ref: {txn.reference}</div>
                      )}
                    </TableCell>
                    <TableCell className="text-xs text-muted-foreground">
                      {new Date(txn.created_at).toLocaleString()}
                      {txn.created_by && (
                        <div>By: {txn.created_by.slice(-8)}</div>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

function AdjustmentCard() {
  const { t } = useI18n()
  const [products, setProducts] = useState<any[]>([])
  const [form, setForm] = useState({
    product_id: "",
    qty: 0,
    location: "finished_goods",
    reason_code: "cycle_count",
    notes: ""
  })

  // ✅ FIXED: Use actual locations from your location directory
  const locations = getLocationsForDropdown().map(loc => loc.value)

  const reasonCodes = [
    { value: "cycle_count", label: "Cycle Count" },
    { value: "damage", label: "Damage" },
    { value: "obsolete", label: "Obsolete" },
    { value: "adjustment", label: "Adjustment" }
  ]

  useEffect(() => {
    async function loadProducts() {
      try {
        console.log("🔄 Loading products for AdjustmentCard...")

        const productsResponse = await fetch("/api/products").then(res => res.json())

        console.log("📦 AdjustmentCard products response:", productsResponse)

        // Handle different response formats
        const productsData = Array.isArray(productsResponse) ? productsResponse : (productsResponse?.data || [])

        setProducts(productsData)

        console.log("✅ AdjustmentCard loaded products:", productsData.length)
      } catch (err) {
        console.error('❌ AdjustmentCard error loading products:', err)
        setProducts([])
      }
    }

    loadProducts()
  }, [])

  async function adjust() {
    if (!form.product_id || form.qty === 0 || !form.location || !form.notes.trim()) {
      alert("Please fill all fields and provide adjustment notes")
      return
    }

    try {
      const response = await fetch("/api/inventory/transactions/adjustment", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          transaction_type: "adjustment",
          product_id: form.product_id,
          qty: form.qty,
          location: form.location,
          reason_code: form.reason_code,
          notes: form.notes
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log("✅ Adjustment transaction:", result.message)
        setForm({
          product_id: "",
          qty: 0,
          location: "finished_goods",
          reason_code: "cycle_count",
          notes: ""
        })
        await (window as any).refreshInventory?.()
      } else {
        const error = await response.json()
        alert(`Adjustment failed: ${error.message}`)
      }
    } catch (error) {
      console.error("Adjustment error:", error)
      alert("Adjustment failed: Network error")
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <span>Adjustment</span>
          <Badge variant="outline">New</Badge>
        </CardTitle>
        <CardDescription>Adjust inventory quantities (±)</CardDescription>
      </CardHeader>
      <CardContent className="grid gap-3 md:grid-cols-6">
        <div className="grid gap-1">
          <Label>{t("field.product")}</Label>
          <Select value={form.product_id} onValueChange={(v) => setForm({ ...form, product_id: v })}>
            <SelectTrigger>
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
              {products.map((p) => (
                <SelectItem key={p.id} value={p.id}>
                  {p.sku} - {p.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="grid gap-1">
          <Label>Adjustment Qty (±)</Label>
          <Input
            type="number"
            value={form.qty}
            onChange={(e) => setForm({ ...form, qty: Number(e.target.value) })}
            placeholder="+ or - amount"
          />
        </div>
        <div className="grid gap-1">
          <Label>{t("field.location")}</Label>
          <Select value={form.location} onValueChange={(v) => setForm({ ...form, location: v })}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {locationOptions.map((loc) => (
                <SelectItem key={loc.value} value={loc.value}>
                  {loc.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="grid gap-1">
          <Label>Reason</Label>
          <Select value={form.reason_code} onValueChange={(v) => setForm({ ...form, reason_code: v })}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {reasonCodes.map((reason) => (
                <SelectItem key={reason.value} value={reason.value}>
                  {reason.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="grid gap-1 md:col-span-2">
          <Label>{t('inventory.dialogs.notes_required')}</Label>
          <Input
            value={form.notes}
            onChange={(e) => setForm({ ...form, notes: e.target.value })}
            placeholder={t('inventory.dialogs.adjustment_reason')}
            required
          />
        </div>
        <div className="md:col-span-6">
          <Button onClick={adjust}>Process Adjustment</Button>
        </div>
      </CardContent>
    </Card>
  )
}

function StockCard() {
  const { t } = useI18n()
  const [lots, setLots] = useState<any[]>([])
  const [filteredLots, setFilteredLots] = useState<any[]>([])
  const [products, setProducts] = useState<any[]>([])
  const [realLocations, setRealLocations] = useState<string[]>([])
  const [databaseLocations, setDatabaseLocations] = useState<DatabaseLocation[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [qualityFilter, setQualityFilter] = useState<string>("all")
  const [searchTerm, setSearchTerm] = useState<string>("")
  const [locationFilter, setLocationFilter] = useState<string>("all")

  async function refresh() {
    try {
      setLoading(true)
      setError(null)

      // Build API URL with quality status filter
      const lotsUrl = qualityFilter === "all"
        ? "/api/inventory/lots"
        : `/api/inventory/lots?quality_status=${qualityFilter}`

      const [lotsResponse, productsResponse, realLocationsResponse, locationsResponse] = await Promise.all([
        safeJson(lotsUrl, { data: [] }),
        safeJson("/api/products", { data: [] }),
        fetch("/api/inventory/real-locations")
          .then(res => res.ok ? res.json() : { locations: [] })
          .catch(() => ({ locations: [] })),
        safeJson("/api/locations", { data: [] })
      ])

      // Extract data from API response structure
      const l = lotsResponse?.data || []
      const p = productsResponse?.data || []
      const realLocs = realLocationsResponse?.locations || []
      // ✅ FIX: API returns { locations: [...] } not { data: [...] }
      const dbLocs = locationsResponse?.locations || []

      // ✅ DEBUG: Log location data loading
      console.log('🔍 Location Filter Debug:', {
        locationsResponse,
        dbLocs,
        dbLocsLength: dbLocs.length,
        dbLocsArray: Array.isArray(dbLocs)
      })

      // Ensure we always have arrays
      const lotsData = Array.isArray(l) ? l : []
      setLots(lotsData)
      setProducts(Array.isArray(p) ? p : [])
      setRealLocations(Array.isArray(realLocs) ? realLocs : [])
      setDatabaseLocations(Array.isArray(dbLocs) ? dbLocs : [])

      // Apply filters
      applyFilters(lotsData)
    } catch (err) {
      console.error('Error refreshing inventory:', err)
      setError('Failed to load inventory data')
      setLots([])
      setFilteredLots([])
      setProducts([])
      setRealLocations([])
      setDatabaseLocations([])
    } finally {
      setLoading(false)
    }
  }

  // ✅ PROFESSIONAL FILTERING LOGIC
  const applyFilters = (lotsData: any[]) => {
    let filtered = [...lotsData]

    // Quality filter
    if (qualityFilter !== "all") {
      filtered = filtered.filter(lot => lot.quality_status === qualityFilter)
    }

    // Location filter
    if (locationFilter !== "all") {
      filtered = filtered.filter(lot => lot.location === locationFilter)
    }

    // Search filter
    if (searchTerm.trim()) {
      const search = searchTerm.toLowerCase()
      filtered = filtered.filter(lot =>
        lot.product?.name?.toLowerCase().includes(search) ||
        lot.product?.sku?.toLowerCase().includes(search) ||
        lot.lot_number?.toLowerCase().includes(search) ||
        lot.workOrder?.number?.toLowerCase().includes(search)
      )
    }

    setFilteredLots(filtered)
  }

  useEffect(() => {
    ; (window as any).refreshInventory = refresh
    refresh()
    return () => {
      ; (window as any).refreshInventory = undefined
    }
  }, [])

  // ✅ APPLY FILTERS WHEN FILTER VALUES CHANGE
  useEffect(() => {
    applyFilters(lots)
  }, [qualityFilter, locationFilter, searchTerm, lots])

  // ✅ APPROVE PENDING LOT
  const handleApproveLot = async (lotId: string) => {
    try {
      const response = await fetch(`/api/inventory/lots/${lotId}/quality`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          quality_status: "approved",
          quality_notes: "Manually approved via inventory management"
        }),
      })

      if (response.ok) {
        // Refresh the inventory data
        refresh()
        // Show success message (you can add toast here if needed)
        console.log("Lot approved successfully")
      } else {
        console.error("Failed to approve lot")
      }
    } catch (error) {
      console.error("Error approving lot:", error)
    }
  }

  // Quality status badge component - Professional 4-status system
  const QualityStatusBadge = ({ status }: { status: string }) => {
    const statusConfig = {
      pending: { label: t("quality.status.pending"), icon: Clock, variant: "outline" as const },
      approved: { label: t("quality.status.approved"), icon: CheckCircle, variant: "default" as const },
      quarantined: { label: t("quality.status.quarantined"), icon: AlertTriangle, variant: "secondary" as const },
      rejected: { label: t("quality.status.rejected"), icon: XCircle, variant: "destructive" as const },
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.approved
    const Icon = config.icon

    return (
      <Badge variant={config.variant}>
        <Icon className="mr-1 h-3 w-3" />
        {config.label}
      </Badge>
    )
  }

  // ✅ ENHANCED: Professional inventory table with product totals
  const EnhancedInventoryTable = ({
    filteredLots,
    products,
    searchTerm,
    qualityFilter,
    locationFilter,
    getLocationForUI,
    handleApproveLot,
    QualityStatusBadge
  }: any) => {
    // Group lots by product and calculate totals
    const groupedInventory = useMemo(() => {
      const groups = new Map()

      filteredLots.forEach((lot: any) => {
        const productId = lot.product_id
        const productKey = `${productId}-${lot.product?.name || 'Unknown'}-${lot.product?.sku || productId}`

        if (!groups.has(productKey)) {
          groups.set(productKey, {
            product: lot.product,
            productId,
            lots: [],
            totalQuantity: 0,
            locations: new Set(),
            qualityStatuses: new Set()
          })
        }

        const group = groups.get(productKey)
        group.lots.push(lot)
        group.totalQuantity += parseFloat(lot.qty || 0)
        group.locations.add(lot.location)
        group.qualityStatuses.add(lot.quality_status || 'pending')
      })

      return Array.from(groups.values()).sort((a, b) =>
        (a.product?.name || 'Unknown').localeCompare(b.product?.name || 'Unknown')
      )
    }, [filteredLots])

    if (filteredLots.length === 0) {
      return (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Product</TableHead>
                <TableHead>Total Quantity</TableHead>
                <TableHead>Lots</TableHead>
                <TableHead>Locations</TableHead>
                <TableHead>Quality Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  <div className="text-muted-foreground">
                    <Package className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    {searchTerm || qualityFilter !== "all" || locationFilter !== "all" ? (
                      <p>No inventory found matching your filters</p>
                    ) : (
                      <p>No inventory items found</p>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      )
    }

    return (
      <div className="space-y-4">
        {/* ✅ SUMMARY CARDS */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">{t('inventory.stock_inventory.total_products')}</p>
                  <p className="text-2xl font-bold text-blue-600">{groupedInventory.length}</p>
                </div>
                <Package className="h-8 w-8 text-blue-600 opacity-80" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">{t('inventory.stock_inventory.total_lots')}</p>
                  <p className="text-2xl font-bold text-green-600">{filteredLots.length}</p>
                </div>
                <Layers className="h-8 w-8 text-green-600 opacity-80" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">{t('inventory.stock_inventory.total_units')}</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {groupedInventory.reduce((sum, group) => sum + group.totalQuantity, 0).toLocaleString()}
                  </p>
                </div>
                <BarChart3 className="h-8 w-8 text-purple-600 opacity-80" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">{t('inventory.stock_inventory.pending_quality')}</p>
                  <p className="text-2xl font-bold text-orange-600">
                    {filteredLots.filter((lot: any) => lot.quality_status === 'pending').length}
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-orange-600 opacity-80" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* ✅ ENHANCED PRODUCT-GROUPED TABLE */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[300px]">{t('inventory.stock_inventory.table.product')}</TableHead>
                <TableHead className="text-right w-[120px]">{t('inventory.stock_inventory.table.total_quantity')}</TableHead>
                <TableHead className="w-[100px]">{t('inventory.stock_inventory.table.lots')}</TableHead>
                <TableHead className="w-[200px]">{t('inventory.stock_inventory.table.locations')}</TableHead>
                <TableHead className="w-[150px]">{t('inventory.stock_inventory.table.quality_status')}</TableHead>
                <TableHead className="w-[100px]">{t('inventory.stock_inventory.table.actions')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {groupedInventory.map((group, groupIndex) => (
                <React.Fragment key={`${group.productId}-${groupIndex}`}>
                  {/* ✅ PRODUCT SUMMARY ROW */}
                  <TableRow className="bg-muted/30 hover:bg-muted/50">
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-bold text-lg">{group.product?.name || "Unknown Product"}</div>
                        <div className="text-sm text-muted-foreground font-mono">
                          {group.product?.sku || group.productId}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="font-bold text-lg text-blue-600">
                        {group.totalQuantity.toLocaleString()} {group.product?.unit || ""}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary" className="font-medium">
                        {group.lots.length} lots
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {Array.from(group.locations).slice(0, 2).map((location: string) => {
                          const loc = getEnhancedLocationForUI(location)
                          return (
                            <Badge key={location} variant="outline" className="text-xs">
                              {loc ? `${loc.icon} ${loc.displayName}` : location}
                            </Badge>
                          )
                        })}
                        {group.locations.size > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{group.locations.size - 2} more
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {Array.from(group.qualityStatuses).map((status: string) => (
                          <QualityStatusBadge key={status} status={status} />
                        ))}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          const element = document.getElementById(`lots-${group.productId}-${groupIndex}`)
                          if (element) {
                            element.style.display = element.style.display === 'none' ? '' : 'none'
                          }
                        }}
                        className="text-blue-600 hover:text-blue-700"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        {t('inventory.stock_inventory.table.details')}
                      </Button>
                    </TableCell>
                  </TableRow>

                  {/* ✅ LOT DETAILS ROWS (Initially Hidden) */}
                  <TableRow id={`lots-${group.productId}-${groupIndex}`} style={{ display: 'none' }}>
                    <TableCell colSpan={6} className="p-0">
                      <div className="bg-muted/10 border-t">
                        <Table>
                          <TableHeader>
                            <TableRow className="bg-muted/20">
                              <TableHead className="pl-8">Lot Number</TableHead>
                              <TableHead className="text-right">Quantity</TableHead>
                              <TableHead>Location</TableHead>
                              <TableHead>Quality</TableHead>
                              <TableHead>Work Order</TableHead>
                              <TableHead>Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {group.lots.map((lot: any) => (
                              <TableRow key={lot.id} className="bg-background/50">
                                <TableCell className="pl-8">
                                  <div className="font-mono text-sm font-medium">
                                    {lot.lot_number || `LOT-${lot.id.slice(-8)}`}
                                  </div>
                                  {lot.expiry_date && (
                                    <div className="text-xs text-muted-foreground">Exp: {lot.expiry_date}</div>
                                  )}
                                </TableCell>
                                <TableCell className="text-right">
                                  <div className="font-medium">
                                    {parseFloat(lot.qty || 0).toLocaleString()} {group.product?.unit || ""}
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <Badge variant="outline" className="font-mono text-xs">
                                    {(() => {
                                      const loc = getLocationForUI(lot.location)
                                      return loc ? `${loc.icon} ${loc.displayName}` : (lot.location || 'Unknown')
                                    })()}
                                  </Badge>
                                </TableCell>
                                <TableCell>
                                  <QualityStatusBadge status={lot.quality_status || "pending"} />
                                </TableCell>
                                <TableCell>
                                  {lot.workOrder ? (
                                    <div className="text-sm">
                                      <div className="font-medium text-blue-600 hover:underline cursor-pointer">
                                        {lot.workOrder.number}
                                      </div>
                                      <div className="text-xs">
                                        {lot.workOrder.status === 'completed' ? (
                                          <Badge variant="default" className="text-xs">Completed</Badge>
                                        ) : lot.workOrder.status === 'in-progress' ? (
                                          <Badge variant="secondary" className="text-xs">In Progress</Badge>
                                        ) : (
                                          <Badge variant="outline" className="text-xs">Pending</Badge>
                                        )}
                                      </div>
                                    </div>
                                  ) : lot.work_order_id ? (
                                    <div className="text-sm font-mono text-muted-foreground">
                                      {lot.work_order_id.slice(-8)}
                                    </div>
                                  ) : (
                                    <span className="text-muted-foreground">-</span>
                                  )}
                                </TableCell>
                                <TableCell>
                                  {lot.quality_status === "pending" && (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleApproveLot(lot.id)}
                                      className="text-green-600 hover:text-green-700 hover:bg-green-50"
                                    >
                                      <CheckCircle className="h-4 w-4 mr-1" />
                                      Approve
                                    </Button>
                                  )}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </TableCell>
                  </TableRow>
                </React.Fragment>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    )
  }

  // ✅ PRODUCTION-READY: Hybrid location utility with intelligent matching
  const getEnhancedLocationForUI = (locationValue: string | null | undefined) => {
    if (!locationValue) return null

    // ✅ DEBUG: Log function calls (will show in browser console)
    console.log('🔍 getEnhancedLocationForUI called:', {
      locationValue,
      databaseLocationsCount: databaseLocations.length,
      databaseLocations: databaseLocations.map(loc => ({ id: loc.id, name: loc.name, type: loc.type }))
    })

    const typeIcons = {
      'warehouse': '🏢',
      'raw_materials': '📦',
      'finished_goods': '✅',
      'work_in_progress': '⚙️',
      'quality_control': '🔍',
      'shipping': '🚚',
      'receiving': '📥',
      'quarantine': '⚠️',
      'returns': '↩️'
    }

    // ✅ ENHANCED: Smart fallback with intelligent location mapping
    if (!databaseLocations || databaseLocations.length === 0) {
      console.log('⚠️ Database locations not loaded yet, using enhanced fallback')

      // ✅ SMART MAPPING: Map common location codes to proper names
      const locationMappings = {
        'finished_goods': { name: 'Main Finished Goods Warehouse', icon: '✅', type: 'finished_goods' },
        'rm_building_a': { name: 'Raw Materials Storage', icon: '📦', type: 'raw_materials' },
        'raw_materials': { name: 'Raw Materials Storage', icon: '📦', type: 'raw_materials' },
        'quality_control': { name: 'Quality Control Lab', icon: '🔍', type: 'quality_control' },
        'shipping': { name: 'Shipping Dock', icon: '🚚', type: 'shipping' },
        'warehouse': { name: 'Main Warehouse', icon: '🏢', type: 'warehouse' },
        'work_in_progress': { name: 'Work in Progress Area', icon: '⚙️', type: 'work_in_progress' },
      }

      const mapping = locationMappings[locationValue as keyof typeof locationMappings]
      if (mapping) {
        return {
          id: locationValue,
          displayName: mapping.name,
          icon: mapping.icon,
          color: 'bg-blue-500',
          type: mapping.type
        }
      }

      // Generic fallback for unknown locations
      const smartIcon = typeIcons[locationValue as keyof typeof typeIcons] || '📍'
      const smartName = locationValue.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
      return {
        id: locationValue,
        displayName: smartName,
        icon: smartIcon,
        color: 'bg-gray-500',
        type: locationValue
      }
    }

    // STEP 1: Try exact ID match first (for future-proofing)
    const exactMatch = databaseLocations.find(loc => loc.id === locationValue)
    if (exactMatch) {
      return {
        id: exactMatch.id,
        displayName: exactMatch.name,
        icon: typeIcons[exactMatch.type as keyof typeof typeIcons] || '📍',
        color: 'bg-blue-500',
        type: exactMatch.type
      }
    }

    // STEP 2: Try type-based match (for current data compatibility)
    const typeMatch = databaseLocations.find(loc => loc.type === locationValue)
    if (typeMatch) {
      return {
        id: typeMatch.id,
        displayName: typeMatch.name,
        icon: typeIcons[typeMatch.type as keyof typeof typeIcons] || '📍',
        color: 'bg-blue-500',
        type: typeMatch.type
      }
    }

    // STEP 3: Try intelligent matching for legacy codes (like "rm_building_a")
    const intelligentMatch = databaseLocations.find(loc => {
      const locationLower = locationValue.toLowerCase()
      const nameLower = loc.name.toLowerCase()
      const codeLower = loc.location_code?.toLowerCase() || ''

      // Direct code match
      if (codeLower === locationLower) return true

      // Smart pattern matching for legacy codes
      if (locationLower.includes('rm_') || locationLower.includes('raw')) {
        return loc.type === 'raw_materials'
      }
      if (locationLower.includes('fg_') || locationLower.includes('finished')) {
        return loc.type === 'finished_goods'
      }
      if (locationLower.includes('wip_') || locationLower.includes('work')) {
        return loc.type === 'work_in_progress'
      }
      if (locationLower.includes('qc_') || locationLower.includes('quality')) {
        return loc.type === 'quality_control'
      }
      if (locationLower.includes('ship') || locationLower.includes('dock')) {
        return loc.type === 'shipping'
      }

      // Partial name matching
      return nameLower.includes(locationLower) || locationLower.includes(nameLower.split(' ')[0])
    })

    if (intelligentMatch) {
      return {
        id: intelligentMatch.id,
        displayName: intelligentMatch.name,
        icon: typeIcons[intelligentMatch.type as keyof typeof typeIcons] || '📍',
        color: 'bg-blue-500',
        type: intelligentMatch.type
      }
    }

    // STEP 4: Enhanced fallback with smart formatting
    const smartIcon = typeIcons[locationValue as keyof typeof typeIcons] || '📍'

    // ✅ PRODUCTION FIX: Don't show raw UUIDs to users
    let smartName: string
    if (locationValue.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      // This is a UUID - show generic location name instead of raw UUID
      smartName = t('inventory.unknown_location')
      console.warn('⚠️ Location UUID not found in database:', locationValue)
    } else {
      // This is a location code - format it nicely
      smartName = locationValue.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    }

    return {
      id: locationValue,
      displayName: smartName,
      icon: smartIcon,
      color: 'bg-gray-500',
      type: locationValue
    }
  }

  // ✅ FIX: Get locations for filter dropdown from database locations
  const locationFilterOptions = getLocationsForDropdown(databaseLocations)

  // ✅ DEBUG: Log location filter options
  console.log('🔍 Location Filter Options Debug:', {
    databaseLocations,
    databaseLocationsLength: databaseLocations.length,
    locationFilterOptions,
    locationFilterOptionsLength: locationFilterOptions.length
  })

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>{t('inventory.stock_inventory.title')}</span>
          <Badge variant="outline">{filteredLots.length} items</Badge>
        </CardTitle>
        <CardDescription>{t('inventory.stock_inventory.subtitle')}</CardDescription>
      </CardHeader>
      <CardContent>
        {/* ✅ PROFESSIONAL SEARCH AND FILTERS */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          {/* Search */}
          <div className="flex-1">
            <Input
              placeholder={t('inventory.stock_inventory.search_placeholder')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>

          {/* Quality Filter */}
          <Select value={qualityFilter} onValueChange={setQualityFilter}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Quality" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('inventory.stock_inventory.all_quality')}</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="quarantined">Quarantined</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
            </SelectContent>
          </Select>

          {/* Location Filter */}
          <Select value={locationFilter} onValueChange={setLocationFilter}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Location" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('inventory.stock_inventory.all_locations')}</SelectItem>
              {loading ? (
                <SelectItem value="loading" disabled>
                  Loading locations...
                </SelectItem>
              ) : (
                locationFilterOptions.map(location => (
                  <SelectItem key={location.value} value={location.value}>
                    {location.label}
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
        </div>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="ml-2">{t("loading.inventory")}</span>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <p className="text-red-600 mb-2">{error}</p>
            <button
              type="button"
              onClick={refresh}
              className="text-blue-600 hover:underline"
            >
              {t("loading.try_again")}
            </button>
          </div>
        ) : (
          <EnhancedInventoryTable
            filteredLots={filteredLots}
            products={products}
            searchTerm={searchTerm}
            qualityFilter={qualityFilter}
            locationFilter={locationFilter}
            getLocationForUI={getEnhancedLocationForUI}
            handleApproveLot={handleApproveLot}
            QualityStatusBadge={QualityStatusBadge}
          />
        )}
      </CardContent>
    </Card>
  )
}

function TransactionsCard() {
  const { t } = useI18n()
  const [txns, setTxns] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [typeFilter, setTypeFilter] = useState<string>("all")

  async function refresh() {
    try {
      setLoading(true)
      setError(null)

      console.log("🔄 Refreshing transactions with filter:", typeFilter)

      // Use the new enhanced transaction API with product relationships
      const apiUrl = typeFilter === "all"
        ? "/api/inventory/transactions?limit=50"
        : `/api/inventory/transactions?transaction_type=${typeFilter}&limit=50`

      const txnsResponse = await fetch(apiUrl).then(res => res.json())

      console.log("📊 Transactions response:", txnsResponse)

      // Handle different response formats
      const tRows = Array.isArray(txnsResponse) ? txnsResponse : (txnsResponse?.data || [])

      setTxns(Array.isArray(tRows) ? tRows : [])

      console.log("✅ Loaded transactions:", tRows.length)
    } catch (err) {
      console.error('❌ Error refreshing transactions:', err)
      setError('Failed to load transaction data')
      setTxns([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    refresh()
  }, [typeFilter]) // Refresh when filter changes

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>{t("inventory.txns.title")}</span>
          <Badge variant="outline">{txns.length} Transactions</Badge>
        </CardTitle>
        <CardDescription>{t("inventory.txns.desc")}</CardDescription>
      </CardHeader>
      <CardContent>
        {/* Transaction Type Filter */}
        <div className="mb-4">
          <Label>Transaction Type Filter</Label>
          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Transactions</SelectItem>
              <SelectItem value="inbound">Inbound Only</SelectItem>
              <SelectItem value="outbound">Outbound Only</SelectItem>
              <SelectItem value="transfer">Transfer Only</SelectItem>
              <SelectItem value="adjustment">Adjustment Only</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="ml-2">Loading transactions...</span>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <p className="text-red-600 mb-2">{error}</p>
            <button type="button" onClick={refresh} className="text-blue-600 hover:underline">
              Retry
            </button>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Type</TableHead>
                <TableHead>Product Details</TableHead>
                <TableHead>Quantity</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Workflow</TableHead>
                <TableHead>Reference</TableHead>
                <TableHead>Time</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.isArray(txns) && txns.map((tRow) => (
                <TableRow key={tRow.id}>
                  <TableCell>
                    <Badge variant={
                      tRow.transaction_type === "inbound" || tRow.type === "inbound" ? "default" :
                        tRow.transaction_type === "outbound" || tRow.type === "outbound" ? "secondary" :
                          tRow.transaction_type === "transfer" ? "outline" :
                            tRow.transaction_type === "adjustment" ? "destructive" : "secondary"
                    }>
                      {tRow.transaction_type || tRow.type}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-medium">{tRow.product?.name || "N/A"}</div>
                      <div className="text-sm text-muted-foreground">{tRow.product?.sku || tRow.product_id}</div>
                      {tRow.product?.description && (
                        <div className="text-xs text-muted-foreground">{tRow.product.description}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-mono">
                      {tRow.qty} {tRow.product?.unit || "units"}
                    </div>
                    {tRow.reason_code && (
                      <div className="text-xs text-muted-foreground">
                        {tRow.reason_code}
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    {tRow.transaction_type === "transfer" && tRow.from_location && tRow.to_location ? (
                      <div className="flex items-center gap-1 text-sm">
                        <Badge variant="secondary">
                          {(() => {
                            const loc = getLocationForUI(tRow.from_location)
                            return loc ? `${loc.icon} ${loc.displayName}` : tRow.from_location
                          })()}
                        </Badge>
                        <span>→</span>
                        <Badge variant="outline">
                          {(() => {
                            const loc = getLocationForUI(tRow.to_location)
                            return loc ? `${loc.icon} ${loc.displayName}` : tRow.to_location
                          })()}
                        </Badge>
                      </div>
                    ) : (
                      <Badge variant="outline">
                        {(() => {
                          const rawLoc = tRow.location || tRow.from_location || tRow.to_location || "N/A"
                          const loc = getLocationForUI(rawLoc)
                          return loc ? `${loc.icon} ${loc.displayName}` : rawLoc
                        })()}
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    {tRow.workflow_trigger ? (
                      <Badge variant="outline">
                        {tRow.workflow_trigger.replace(/_/g, " ")}
                      </Badge>
                    ) : (
                      <span className="text-muted-foreground">Manual</span>
                    )}
                  </TableCell>
                  <TableCell className="text-sm">
                    <div>{tRow.reference || tRow.ref || "-"}</div>
                    {tRow.notes && (
                      <div className="text-xs text-muted-foreground">{tRow.notes}</div>
                    )}
                  </TableCell>
                  <TableCell className="text-xs text-muted-foreground">
                    {new Date(tRow.created_at).toLocaleString()}
                    {tRow.created_by && (
                      <div>By: {tRow.created_by.slice(-8)}</div>
                    )}
                  </TableCell>
                </TableRow>
              ))}
              {(!Array.isArray(txns) || txns.length === 0) && (
                <TableRow>
                  <TableCell colSpan={7} className="text-muted-foreground text-center py-8">
                    {typeFilter === "all" ? "No transactions found" : `No ${typeFilter} transactions found`}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  )
}

// ✅ PROFESSIONAL: Capacity Validation Component
function CapacityValidation({ locationId, quantity }: { locationId: string, quantity: number }) {
  const [validation, setValidation] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (!locationId || !quantity || quantity <= 0) {
      setValidation(null)
      return
    }

    const validateCapacity = async () => {
      setLoading(true)
      try {
        const response = await fetch('/api/locations/utilization', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ locationId, quantity })
        })

        if (response.ok) {
          const data = await response.json()
          setValidation(data.validation)
        }
      } catch (error) {
        console.error('Capacity validation error:', error)
      } finally {
        setLoading(false)
      }
    }

    const debounceTimer = setTimeout(validateCapacity, 500)
    return () => clearTimeout(debounceTimer)
  }, [locationId, quantity])

  if (!locationId || !quantity || quantity <= 0) return null
  if (loading) return <div className="text-xs text-muted-foreground">Checking capacity...</div>

  if (!validation) return null

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal': return 'text-green-600 bg-green-50 border-green-200'
      case 'warning': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'critical': return 'text-orange-600 bg-orange-50 border-orange-200'
      case 'full': return 'text-red-600 bg-red-50 border-red-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'normal': return '✅'
      case 'warning': return '⚠️'
      case 'critical': return '🔶'
      case 'full': return '🚫'
      default: return 'ℹ️'
    }
  }

  return (
    <div className={`mt-2 p-3 rounded-lg border text-sm ${getStatusColor(validation.status)}`}>
      <div className="flex items-start gap-2">
        <span className="text-base">{getStatusIcon(validation.status)}</span>
        <div className="flex-1">
          <p className="font-medium">{validation.message}</p>
          {validation.alternativeLocations && validation.alternativeLocations.length > 0 && (
            <div className="mt-2">
              <p className="text-xs font-medium">Alternative locations:</p>
              <ul className="text-xs mt-1 space-y-1">
                {validation.alternativeLocations.map((loc: string, idx: number) => (
                  <li key={idx}>• {loc}</li>
                ))}
              </ul>
            </div>
          )}
          <div className="mt-2 text-xs opacity-75">
            Available: {validation.availableCapacity?.toLocaleString()} units •
            After: {validation.utilizationAfter}% utilized
          </div>
        </div>
      </div>
    </div>
  )
}
