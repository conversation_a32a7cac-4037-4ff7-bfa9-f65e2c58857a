{"tutorial.badge": "Interactive ERP Tutorial", "tutorial.hero.title": "Master Your Manufacturing ERP System", "tutorial.hero.subtitle": "Take a comprehensive guided tour through every aspect of your textile manufacturing and export operations. Learn how to streamline your workflow from customer management to financial reporting.", "tutorial.startTour": "Start Interactive Tour", "tutorial.skipToLogin": "Skip to Login", "tutorial.backToHome": "Back to Home", "tutorial.progress.title": "Tutorial Progress", "tutorial.progress.of": "of", "tutorial.stepByStep": "Step-by-Step Instructions", "tutorial.expectedOutcome": "Expected Outcome", "tutorial.nextAction": "Next Action", "tutorial.tryModule": "Try This Module", "tutorial.previous": "Previous", "tutorial.next": "Next", "tutorial.workflow.title": "Complete Workflow", "tutorial.tips.title": "Quick Tips", "tutorial.tips.tip1": "Each step builds on the previous one - follow the sequence for best results.", "tutorial.tips.tip2": "You can click on any step to jump ahead, but we recommend following the order.", "tutorial.tips.tip3": "Try each module in a new tab to see the real system in action.", "tutorial.getStarted.title": "Ready to Begin?", "tutorial.getStarted.description": "Start your free trial and put what you've learned into practice.", "tutorial.getStarted.button": "Start Free Trial", "tutorial.steps.setup.title": "Company Setup", "tutorial.steps.setup.description": "Initialize your ERP system with company profile and basic configuration", "tutorial.steps.setup.content.1": "Create your company profile with business details, contact information, and operational preferences", "tutorial.steps.setup.content.2": "Configure system settings including currency, language, and timezone for your operations", "tutorial.steps.setup.content.3": "Set up user permissions and access levels for your team members", "tutorial.steps.setup.outcome": "Your ERP system is configured and ready for business operations with proper company isolation and security", "tutorial.steps.setup.next": "Proceed to customer management to start building your business relationships", "tutorial.steps.customers.title": "Customer Management", "tutorial.steps.customers.description": "Build and manage your customer database with comprehensive contact and business information", "tutorial.steps.customers.content.1": "Add new customers with complete business details including company name, contact person, and communication preferences", "tutorial.steps.customers.content.2": "Configure payment terms, credit limits, and special pricing arrangements for each customer", "tutorial.steps.customers.content.3": "Track customer history, preferences, and relationship status to improve service quality", "tutorial.steps.customers.outcome": "A comprehensive customer database that supports relationship management and business growth", "tutorial.steps.customers.next": "Set up your product catalog to define what you manufacture and sell", "tutorial.steps.products.title": "Product Catalog", "tutorial.steps.products.description": "Create detailed product specifications with SKUs, pricing, and quality standards", "tutorial.steps.products.content.1": "Define products with detailed specifications including SKU, dimensions, materials, and quality requirements", "tutorial.steps.products.content.2": "Set up pricing structures, cost calculations, and profit margins for accurate financial planning", "tutorial.steps.products.content.3": "Configure inventory parameters including minimum stock levels, reorder points, and storage requirements", "tutorial.steps.products.outcome": "A complete product catalog that supports manufacturing, pricing, and inventory management", "tutorial.steps.products.next": "Create product samples for customer evaluation and approval processes", "tutorial.steps.samples.title": "Sample Management", "tutorial.steps.samples.description": "Manage product samples for customer evaluation and approval workflows", "tutorial.steps.samples.content.1": "Create sample requests linked to customers and products with detailed specifications and purposes", "tutorial.steps.samples.content.2": "Track sample status through development, testing, and customer approval stages", "tutorial.steps.samples.content.3": "Convert approved samples into sales contracts with accurate specifications and pricing", "tutorial.steps.samples.outcome": "Streamlined sample-to-production workflow that ensures customer satisfaction and accurate orders", "tutorial.steps.samples.next": "Create sales contracts based on approved samples and customer requirements", "tutorial.steps.contracts.title": "Sales Contracts", "tutorial.steps.contracts.description": "Create and manage sales contracts with professional templates and approval workflows", "tutorial.steps.contracts.content.1": "Generate contracts using professional templates with customer details, product specifications, and terms", "tutorial.steps.contracts.content.2": "Configure payment terms, delivery schedules, and quality requirements for each contract", "tutorial.steps.contracts.content.3": "Manage contract approval workflow and track status from draft to completion", "tutorial.steps.contracts.outcome": "Professional sales contracts that protect your business and ensure clear customer expectations", "tutorial.steps.contracts.next": "Generate work orders to begin production based on approved contracts", "tutorial.steps.production.title": "Production Management", "tutorial.steps.production.description": "Plan and track manufacturing operations with work orders and production scheduling", "tutorial.steps.production.content.1": "Generate work orders automatically from approved sales contracts with detailed production requirements", "tutorial.steps.production.content.2": "Schedule production operations, assign resources, and track progress through manufacturing stages", "tutorial.steps.production.content.3": "Monitor production efficiency, quality checkpoints, and completion status in real-time", "tutorial.steps.production.outcome": "Efficient production management that ensures on-time delivery and quality standards", "tutorial.steps.production.next": "Implement quality control processes to ensure product standards and customer satisfaction", "tutorial.steps.quality.title": "Quality Control", "tutorial.steps.quality.description": "Implement comprehensive quality inspections and compliance management", "tutorial.steps.quality.content.1": "Create quality inspection protocols for incoming materials, in-process checks, and final product validation", "tutorial.steps.quality.content.2": "Document test results, defect tracking, and corrective actions with full audit trails", "tutorial.steps.quality.content.3": "Generate quality certificates and compliance documentation for export requirements", "tutorial.steps.quality.outcome": "Robust quality management system that ensures product excellence and regulatory compliance", "tutorial.steps.quality.next": "Update inventory with quality-approved products ready for shipment", "tutorial.steps.inventory.title": "Inventory Management", "tutorial.steps.inventory.description": "Track stock levels, locations, and movements with real-time inventory control", "tutorial.steps.inventory.content.1": "Monitor stock levels across multiple locations with real-time quantity tracking and alerts", "tutorial.steps.inventory.content.2": "Manage stock movements including receipts, transfers, adjustments, and shipments", "tutorial.steps.inventory.content.3": "Track lot numbers, expiration dates, and quality status for complete traceability", "tutorial.steps.inventory.outcome": "Accurate inventory control that prevents stockouts and ensures product availability", "tutorial.steps.inventory.next": "Prepare shipments for approved orders with proper documentation and tracking", "tutorial.steps.shipping.title": "Shipping & Logistics", "tutorial.steps.shipping.description": "Manage shipments, tracking, and delivery coordination for customer orders", "tutorial.steps.shipping.content.1": "Create shipments from sales contracts with carrier selection, packaging, and documentation requirements", "tutorial.steps.shipping.content.2": "Generate shipping documents, track packages, and coordinate delivery schedules with customers", "tutorial.steps.shipping.content.3": "Monitor delivery status, handle exceptions, and maintain customer communication throughout shipping", "tutorial.steps.shipping.outcome": "Efficient shipping operations that ensure on-time delivery and customer satisfaction", "tutorial.steps.shipping.next": "Prepare export documentation for international shipments and trade compliance", "tutorial.steps.export.title": "Export Documentation", "tutorial.steps.export.description": "Create export declarations and trade documentation for international compliance", "tutorial.steps.export.content.1": "Generate export declarations with accurate product classifications, values, and destination details", "tutorial.steps.export.content.2": "Prepare required trade documents including certificates of origin, packing lists, and commercial invoices", "tutorial.steps.export.content.3": "Ensure compliance with international trade regulations and customs requirements", "tutorial.steps.export.outcome": "Complete export documentation that facilitates smooth international trade and customs clearance", "tutorial.steps.export.next": "Generate invoices and manage financial transactions for completed orders", "tutorial.steps.finance.title": "Financial Management", "tutorial.steps.finance.description": "Handle invoicing, payments, and financial tracking for business operations", "tutorial.steps.finance.content.1": "Generate invoices automatically from shipped orders with accurate pricing and payment terms", "tutorial.steps.finance.content.2": "Track accounts receivable, payment status, and follow up on overdue accounts", "tutorial.steps.finance.content.3": "Manage accounts payable, supplier payments, and cash flow monitoring", "tutorial.steps.finance.outcome": "Comprehensive financial management that ensures healthy cash flow and accurate accounting", "tutorial.steps.finance.next": "Access advanced analytics and reporting for business insights and decision making", "tutorial.steps.analytics.title": "Analytics & Reporting", "tutorial.steps.analytics.description": "Access comprehensive business intelligence and performance analytics", "tutorial.steps.analytics.content.1": "View real-time dashboards with key performance indicators for all business areas", "tutorial.steps.analytics.content.2": "Generate detailed reports on sales, production, quality, and financial performance", "tutorial.steps.analytics.content.3": "Analyze trends, identify opportunities, and make data-driven business decisions", "tutorial.steps.analytics.outcome": "Complete business visibility with actionable insights for continuous improvement and growth", "tutorial.steps.analytics.next": "Congratulations! You've completed the full ERP workflow tour. Start your free trial to begin using the system."}