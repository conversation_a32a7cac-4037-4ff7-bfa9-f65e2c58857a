{"landing.login": "登录", "landing.getStarted": "开始使用", "landing.learnMore": "参观导览", "landing.badge": "500+ 出口制造商信赖之选", "landing.hero.title": "一体化纺织制造与出口 ERP", "landing.hero.subtitle": "从生产到出口，简化您的纺织制造业务流程。在一个平台上管理客户、产品、质量控制和国际贸易合规。", "landing.features.noCredit": "无需信用卡", "landing.features.freeTrial": "30天免费试用", "landing.features.quickSetup": "几分钟完成设置", "landing.features.title": "出口制造所需的一切功能", "landing.features.subtitle": "从原材料到国际运输，管理您的整个制造工作流程", "landing.features.crm.title": "客户与供应商管理", "landing.features.crm.description": "全面的客户关系管理系统，管理国际客户和供应商的联系信息、付款条款和贸易历史。", "landing.features.inventory.title": "产品目录与库存", "landing.features.inventory.description": "详细的产品管理，包括SKU、规格、质量标准和实时库存跟踪。", "landing.features.production.title": "生产管理", "landing.features.production.description": "工单管理、生产调度，从裁剪到包装的实时跟踪。", "landing.features.quality.title": "质量控制", "landing.features.quality.description": "集成质量检验、缺陷跟踪和出口标准合规管理。", "landing.features.export.title": "出口文档", "landing.features.export.description": "自动化出口报关、运输文件和国际贸易合规管理。", "landing.features.analytics.title": "分析与报告", "landing.features.analytics.description": "实时仪表板、生产分析和全面的业务洞察报告。", "landing.benefits.title": "为什么选择我们的制造 ERP？", "landing.benefits.subtitle": "专为出口导向的纺织制造商打造", "landing.benefits.speed.title": "提升 50% 运营效率", "landing.benefits.speed.description": "简化的工作流程减少手工作业，加速生产周期", "landing.benefits.compliance.title": "100% 合规保障", "landing.benefits.compliance.description": "内置出口合规功能，确保满足所有国际贸易要求", "landing.benefits.global.title": "全球化就绪", "landing.benefits.global.description": "多币种、多语言支持，满足国际业务运营需求", "landing.hero.mock.last30days": "近30天", "landing.hero.mock.onTimeShipments": "准时发货率", "landing.cta.title": "准备好转型您的制造业务了吗？", "landing.cta.subtitle": "加入数百家已通过我们的 ERP 解决方案简化运营的制造商", "landing.cta.button": "立即开始免费试用", "landing.cta.features": "无需信用卡 • 30天免费试用 • 几分钟完成设置", "landing.footer.copyright": "© 2024 FC-CHINA. 为全球出口制造商而建。", "tutorial.badge": "互动 ERP 教程", "tutorial.hero.title": "掌握您的制造 ERP 系统", "tutorial.hero.subtitle": "通过全面的引导式教程，深入了解纺织制造和出口业务的各个方面。学习如何简化从客户管理到财务报告的整个工作流程。", "tutorial.startTour": "开始互动教程", "tutorial.skipToLogin": "跳转到登录", "tutorial.backToHome": "返回首页", "tutorial.progress.title": "教程进度", "tutorial.progress.of": "共", "tutorial.stepByStep": "分步指导", "tutorial.expectedOutcome": "预期结果", "tutorial.nextAction": "下一步操作", "tutorial.tryModule": "试用此模块", "tutorial.previous": "上一步", "tutorial.next": "下一步", "tutorial.workflow.title": "完整工作流程", "tutorial.tips.title": "快速提示", "tutorial.tips.tip1": "每个步骤都建立在前一个步骤的基础上 - 按顺序进行以获得最佳效果。", "tutorial.tips.tip2": "您可以点击任何步骤跳转，但我们建议按顺序进行。", "tutorial.tips.tip3": "在新标签页中尝试每个模块，查看真实系统的运行情况。", "tutorial.getStarted.title": "准备开始了吗？", "tutorial.getStarted.description": "开始免费试用，将所学知识付诸实践。", "tutorial.getStarted.button": "开始免费试用", "tutorial.steps.setup.title": "公司设置", "tutorial.steps.setup.description": "使用公司资料和基本配置初始化您的 ERP 系统", "tutorial.steps.setup.content.1": "创建包含业务详情、联系信息和运营偏好的公司资料", "tutorial.steps.setup.content.2": "配置系统设置，包括货币、语言和时区等运营参数", "tutorial.steps.setup.content.3": "为团队成员设置用户权限和访问级别", "tutorial.steps.setup.outcome": "您的 ERP 系统已配置完成，具备适当的公司隔离和安全性，可以开始业务运营", "tutorial.steps.setup.next": "继续进行客户管理，开始建立业务关系", "tutorial.steps.customers.title": "客户管理", "tutorial.steps.customers.description": "建立和管理包含全面联系和业务信息的客户数据库", "tutorial.steps.customers.content.1": "添加新客户的完整业务详情，包括公司名称、联系人和沟通偏好", "tutorial.steps.customers.content.2": "为每个客户配置付款条款、信用额度和特殊定价安排", "tutorial.steps.customers.content.3": "跟踪客户历史、偏好和关系状态，以提高服务质量", "tutorial.steps.customers.outcome": "建立支持关系管理和业务增长的全面客户数据库", "tutorial.steps.customers.next": "设置产品目录，定义您制造和销售的产品", "tutorial.steps.products.title": "产品目录", "tutorial.steps.products.description": "创建包含 SKU、定价和质量标准的详细产品规格", "tutorial.steps.products.content.1": "定义产品的详细规格，包括 SKU、尺寸、材料和质量要求", "tutorial.steps.products.content.2": "设置定价结构、成本计算和利润率，以进行准确的财务规划", "tutorial.steps.products.content.3": "配置库存参数，包括最低库存水平、再订货点和存储要求", "tutorial.steps.products.outcome": "建立支持制造、定价和库存管理的完整产品目录", "tutorial.steps.products.next": "创建产品样品，用于客户评估和审批流程", "tutorial.steps.samples.title": "样品管理", "tutorial.steps.samples.description": "管理用于客户评估和审批工作流程的产品样品", "tutorial.steps.samples.content.1": "创建与客户和产品关联的样品请求，包含详细规格和用途", "tutorial.steps.samples.content.2": "跟踪样品在开发、测试和客户审批阶段的状态", "tutorial.steps.samples.content.3": "将已批准的样品转换为具有准确规格和定价的销售合同", "tutorial.steps.samples.outcome": "简化的样品到生产工作流程，确保客户满意度和订单准确性", "tutorial.steps.samples.next": "基于已批准的样品和客户要求创建销售合同", "tutorial.steps.contracts.title": "销售合同", "tutorial.steps.contracts.description": "使用专业模板和审批工作流程创建和管理销售合同", "tutorial.steps.contracts.content.1": "使用包含客户详情、产品规格和条款的专业模板生成合同", "tutorial.steps.contracts.content.2": "为每个合同配置付款条款、交付时间表和质量要求", "tutorial.steps.contracts.content.3": "管理合同审批工作流程，跟踪从草稿到完成的状态", "tutorial.steps.contracts.outcome": "保护您业务并确保客户期望明确的专业销售合同", "tutorial.steps.contracts.next": "基于已批准的合同生成工单以开始生产", "tutorial.steps.production.title": "生产管理", "tutorial.steps.production.description": "通过工单和生产调度规划和跟踪制造操作", "tutorial.steps.production.content.1": "从已批准的销售合同自动生成包含详细生产要求的工单", "tutorial.steps.production.content.2": "安排生产操作、分配资源，并跟踪制造阶段的进度", "tutorial.steps.production.content.3": "实时监控生产效率、质量检查点和完成状态", "tutorial.steps.production.outcome": "高效的生产管理，确保按时交付和质量标准", "tutorial.steps.production.next": "实施质量控制流程，确保产品标准和客户满意度", "tutorial.steps.quality.title": "质量控制", "tutorial.steps.quality.description": "实施全面的质量检验和合规管理", "tutorial.steps.quality.content.1": "为进料、过程检查和最终产品验证创建质量检验协议", "tutorial.steps.quality.content.2": "记录测试结果、缺陷跟踪和纠正措施，保持完整的审计跟踪", "tutorial.steps.quality.content.3": "生成质量证书和出口要求的合规文档", "tutorial.steps.quality.outcome": "强大的质量管理系统，确保产品卓越性和法规合规性", "tutorial.steps.quality.next": "使用质量批准的产品更新库存，准备发货", "tutorial.steps.inventory.title": "库存管理", "tutorial.steps.inventory.description": "通过实时库存控制跟踪库存水平、位置和移动", "tutorial.steps.inventory.content.1": "通过实时数量跟踪和警报监控多个位置的库存水平", "tutorial.steps.inventory.content.2": "管理库存移动，包括收货、转移、调整和发货", "tutorial.steps.inventory.content.3": "跟踪批号、到期日期和质量状态，实现完整的可追溯性", "tutorial.steps.inventory.outcome": "准确的库存控制，防止缺货并确保产品可用性", "tutorial.steps.inventory.next": "为已批准的订单准备发货，包含适当的文档和跟踪", "tutorial.steps.shipping.title": "运输物流", "tutorial.steps.shipping.description": "管理客户订单的发货、跟踪和交付协调", "tutorial.steps.shipping.content.1": "从销售合同创建发货，包括承运商选择、包装和文档要求", "tutorial.steps.shipping.content.2": "生成运输文档、跟踪包裹，并与客户协调交付时间表", "tutorial.steps.shipping.content.3": "监控交付状态、处理异常，并在整个运输过程中保持客户沟通", "tutorial.steps.shipping.outcome": "高效的运输操作，确保按时交付和客户满意度", "tutorial.steps.shipping.next": "为国际发货和贸易合规准备出口文档", "tutorial.steps.export.title": "出口文档", "tutorial.steps.export.description": "创建出口报关单和贸易文档以符合国际合规要求", "tutorial.steps.export.content.1": "生成包含准确产品分类、价值和目的地详情的出口报关单", "tutorial.steps.export.content.2": "准备所需的贸易文档，包括原产地证书、装箱单和商业发票", "tutorial.steps.export.content.3": "确保符合国际贸易法规和海关要求", "tutorial.steps.export.outcome": "完整的出口文档，促进顺畅的国际贸易和海关清关", "tutorial.steps.export.next": "为已完成的订单生成发票并管理财务交易", "tutorial.steps.finance.title": "财务管理", "tutorial.steps.finance.description": "处理业务运营的开票、付款和财务跟踪", "tutorial.steps.finance.content.1": "从已发货订单自动生成包含准确定价和付款条款的发票", "tutorial.steps.finance.content.2": "跟踪应收账款、付款状态，并跟进逾期账户", "tutorial.steps.finance.content.3": "管理应付账款、供应商付款和现金流监控", "tutorial.steps.finance.outcome": "全面的财务管理，确保健康的现金流和准确的会计", "tutorial.steps.finance.next": "访问高级分析和报告，获取业务洞察和决策支持", "tutorial.steps.analytics.title": "分析报告", "tutorial.steps.analytics.description": "访问全面的商业智能和绩效分析", "tutorial.steps.analytics.content.1": "查看包含所有业务领域关键绩效指标的实时仪表板", "tutorial.steps.analytics.content.2": "生成销售、生产、质量和财务绩效的详细报告", "tutorial.steps.analytics.content.3": "分析趋势、识别机会，并做出数据驱动的业务决策", "tutorial.steps.analytics.outcome": "完整的业务可见性，提供可操作的洞察以实现持续改进和增长", "tutorial.steps.analytics.next": "恭喜！您已完成完整的 ERP 工作流程教程。开始免费试用以开始使用系统。"}