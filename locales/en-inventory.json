{"inventory.inbound.title": "Inbound", "inventory.inbound.desc": "Receive inventory", "inventory.outbound.title": "Outbound", "inventory.outbound.desc": "Ship inventory", "inventory.stock.title": "Stock", "inventory.stock.desc": "Current inventory levels", "inventory.stock.empty": "No stock lots.", "inventory.txns.title": "Stock Transactions", "inventory.txns.desc": "FIFO is applied when shipping.", "inventory.txns.empty": "No transactions.", "inventory.title": "Inventory Management", "inventory.subtitle": "Manage stock levels, inbound and outbound operations", "inventory.tabs.inbound": "Inbound", "inventory.tabs.outbound": "Outbound", "inventory.tabs.stock": "Stock", "inventory.tabs.transactions": "Transactions", "inventory.inbound.form.product": "Product", "inventory.inbound.form.qty": "Quantity", "inventory.inbound.form.location": "Location", "inventory.inbound.form.ref": "Reference", "inventory.inbound.button": "Receive", "inventory.outbound.form.product": "Product", "inventory.outbound.form.qty": "Quantity", "inventory.outbound.form.location": "Location", "inventory.outbound.form.ref": "Reference", "inventory.outbound.button": "Ship", "inventory.stock.loading": "Loading inventory...", "inventory.stock.error": "Failed to load inventory data", "inventory.stock.retry": "Try again", "inventory.stock.table.lot": "Lot", "inventory.stock.table.location": "Location", "inventory.transactions.title": "Transactions", "inventory.transactions.desc": "Inventory movement history", "inventory.transaction_forms": "Transaction Forms", "inventory.transaction_history": "Transaction History", "inventory.transaction_success": "Transaction Successful", "inventory.transaction_error": "Transaction Failed", "inventory.inbound": "Inbound", "inventory.outbound": "Outbound", "inventory.transfer": "Transfer", "inventory.adjustment": "Adjustment", "inventory.product": "Product", "inventory.quantity": "Quantity", "inventory.location": "Location", "inventory.source_location": "Source Location", "inventory.destination_location": "Destination Location", "inventory.adjustment_quantity": "Adjustment Quantity", "inventory.reason_code": "Reason Code", "inventory.notes": "Notes", "inventory.reference": "Reference", "inventory.status": "Status", "inventory.date": "Date", "inventory.type": "Type", "inventory.select_product": "Select Product", "inventory.select_location": "Select Location", "inventory.reference_placeholder": "PO/SO number, receipt number, etc.", "inventory.notes_placeholder": "Additional notes or comments", "inventory.transfer_notes_placeholder": "Reason for transfer", "inventory.adjustment_notes_placeholder": "Explain the reason for adjustment", "inventory.positive_negative_allowed": "Positive or negative values allowed", "inventory.process_inbound": "Process Inbound", "inventory.process_outbound": "Process Outbound", "inventory.process_transfer": "Process Transfer", "inventory.process_adjustment": "Process Adjustment", "inventory.adjustment_warning": "Warning", "inventory.adjustment_warning_text": "Adjustments directly modify inventory quantities. Ensure proper authorization and documentation.", "inventory.search_transactions": "Search transactions...", "inventory.filter_by_type": "Filter by Type", "inventory.filter_by_location": "Filter by Location", "inventory.all_types": "All Types", "inventory.all_locations": "All Locations", "inventory.no_transactions": "No transactions found", "inventory.showing_transactions": "Showing {count} of {total} transactions", "inventory.fetch_error": "Failed to Load Data", "inventory.adjustment_notes": "Adjustment Notes", "inventory.reason_receipt": "Receipt", "inventory.reason_shipment": "Shipment", "inventory.reason_transfer": "Transfer", "inventory.reason_cycle_count": "Cycle Count", "inventory.reason_damage": "Damage", "inventory.reason_obsolete": "Obsolete", "inventory.reason_adjustment": "Adjustment", "inventory.reason_return": "Return", "inventory.reason_sample": "<PERSON><PERSON>", "inventory.status_pending": "Pending", "inventory.status_approved": "Approved", "inventory.status_rejected": "Rejected", "inventory.finishedGoods": "Finished Goods", "inventory.rawMaterials": "Raw Materials", "inventory.totalValue": "Total Value", "inventory.management.title": "Inventory Management", "inventory.management.subtitle": "Advanced inventory management with analytics and optimization", "inventory.tabs.finished_goods": "Finished Goods Inventory", "inventory.tabs.analytics": "Analytics Dashboard", "inventory.tabs.discrepancy": "Discrepancy Analysis", "inventory.overview.title": "Inventory Overview", "inventory.overview.last_updated": "Last updated", "inventory.finished_goods.title": "成品库存", "inventory.finished_goods.units_ready": "units ready to ship", "inventory.finished_goods.products": "products", "inventory.finished_goods.low_stock": "low stock", "inventory.finished_goods.value": "Value", "inventory.raw_materials.title": "原材料库存", "inventory.raw_materials.units_available": "units available for production", "inventory.raw_materials.lots": "lots", "inventory.raw_materials.expiring": "expiring", "inventory.raw_materials.value": "Value", "inventory.total_value.title": "总价值", "inventory.total_value.combined": "combined inventory value", "inventory.total_value.locations": "locations", "inventory.total_value.transactions": "transactions", "inventory.total_value.quality_alerts": "quality alerts", "inventory.raw_materials_nav.title": "Raw Materials Inventory", "inventory.raw_materials_nav.subtitle": "Manage raw materials, suppliers, and material lots separately", "inventory.raw_materials_nav.button": "View Raw Materials →", "inventory.quick_actions.title": "Quick Actions", "inventory.quick_actions.subtitle": "Common inventory operations", "inventory.quick_actions.receive": "Receive Stock", "inventory.quick_actions.ship": "Ship Stock", "inventory.quick_actions.transfer": "Transfer Stock", "inventory.quick_actions.adjust": "Adjust Stock", "inventory.stock_inventory.title": "Stock Inventory", "inventory.stock_inventory.subtitle": "Track stock levels, quality status, and inventory movements", "inventory.stock_inventory.search_placeholder": "Search products, SKU, lot number, or work order...", "inventory.stock_inventory.all_quality": "All Quality", "inventory.stock_inventory.all_locations": "All Locations", "inventory.stock_inventory.total_products": "Total Products", "inventory.stock_inventory.total_lots": "Total Lots", "inventory.stock_inventory.total_units": "Total Units", "inventory.stock_inventory.pending_quality": "Pending Quality", "inventory.stock_inventory.table.product": "Product", "inventory.stock_inventory.table.total_quantity": "Total Quantity", "inventory.stock_inventory.table.lots": "Lots", "inventory.stock_inventory.table.locations": "Locations", "inventory.stock_inventory.table.quality_status": "Quality Status", "inventory.stock_inventory.table.actions": "Actions", "inventory.stock_inventory.table.details": "Details", "inventory.recent_activity.title": "Recent Activity", "inventory.recent_activity.subtitle": "Latest inventory transactions", "inventory.recent_activity.no_activity": "No recent activity", "inventory.recent_activity.loading": "Loading recent activity...", "inventory.quality_status.approved": "已通过", "inventory.quality_status.pending": "待检验", "inventory.quality_status.rejected": "已拒绝", "inventory.quality_status.quarantined": "已隔离", "inventory.kpi.units_ready_to_ship": "units ready to ship", "inventory.kpi.products": "products", "inventory.kpi.low_stock": "low stock", "inventory.kpi.value": "Value", "inventory.kpi.units_available_for_production": "units available for production", "inventory.kpi.lots": "lots", "inventory.kpi.expiring": "expiring", "inventory.kpi.combined_inventory_value": "combined inventory value", "inventory.kpi.locations": "locations", "inventory.kpi.transactions": "transactions", "inventory.kpi.quality_alerts": "quality alerts"}