/**
 * Manufacturing ERP - Honeycomb Observability Configuration
 * 
 * Comprehensive observability setup for tracking:
 * - API performance and errors
 * - Database query performance
 * - Business process metrics
 * - User experience monitoring
 */

import { HoneycombSDK } from '@honeycombio/opentelemetry-node'
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node'
import { trace, context, SpanStatusCode, SpanKind } from '@opentelemetry/api'

// ============================================================================
// HONEYCOMB SDK INITIALIZATION
// ============================================================================

let sdk: HoneycombSDK | null = null

export function initializeHoneycomb() {
  // Only initialize in production or when explicitly enabled
  if (process.env.NODE_ENV !== 'production' && !process.env.HONEYCOMB_ENABLED) {
    console.log('🍯 Honeycomb: Skipping initialization (not in production)')
    return
  }

  if (!process.env.HONEYCOMB_API_KEY) {
    console.warn('🍯 Honeycomb: API key not found, skipping initialization')
    return
  }

  if (sdk) {
    console.log('🍯 Honeycomb: Already initialized')
    return
  }

  try {
    sdk = new HoneycombSDK({
      apiKey: process.env.HONEYCOMB_API_KEY,
      serviceName: 'manufacturing-erp',
      serviceVersion: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      instrumentations: [
        getNodeAutoInstrumentations({
          // Disable some noisy instrumentations
          '@opentelemetry/instrumentation-fs': {
            enabled: false,
          },
          '@opentelemetry/instrumentation-dns': {
            enabled: false,
          },
        }),
      ],
    })

    sdk.start()
    console.log('🍯 Honeycomb: Successfully initialized')
  } catch (error) {
    console.error('🍯 Honeycomb: Failed to initialize', error)
  }
}

// ============================================================================
// MANUFACTURING ERP SPECIFIC TRACING
// ============================================================================

const tracer = trace.getTracer('manufacturing-erp')

/**
 * Track API endpoint performance
 */
export function traceApiEndpoint<T>(
  endpoint: string,
  method: string,
  fn: () => Promise<T>
): Promise<T> {
  return tracer.startActiveSpan(
    `${method} ${endpoint}`,
    {
      kind: SpanKind.SERVER,
      attributes: {
        'http.method': method,
        'http.route': endpoint,
        'service.name': 'manufacturing-erp',
        'service.component': 'api',
      },
    },
    async (span) => {
      try {
        const result = await fn()
        span.setStatus({ code: SpanStatusCode.OK })
        return result
      } catch (error) {
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: error instanceof Error ? error.message : 'Unknown error',
        })
        span.recordException(error instanceof Error ? error : new Error(String(error)))
        throw error
      } finally {
        span.end()
      }
    }
  )
}

/**
 * Track database operations
 */
export function traceDatabaseOperation<T>(
  operation: string,
  table: string,
  fn: () => Promise<T>
): Promise<T> {
  return tracer.startActiveSpan(
    `db.${operation}`,
    {
      kind: SpanKind.CLIENT,
      attributes: {
        'db.system': 'postgresql',
        'db.operation': operation,
        'db.table': table,
        'service.component': 'database',
      },
    },
    async (span) => {
      try {
        const result = await fn()
        span.setStatus({ code: SpanStatusCode.OK })
        return result
      } catch (error) {
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: error instanceof Error ? error.message : 'Database error',
        })
        span.recordException(error instanceof Error ? error : new Error(String(error)))
        throw error
      } finally {
        span.end()
      }
    }
  )
}

/**
 * Track manufacturing business processes
 */
export function traceBusinessProcess<T>(
  processName: string,
  processType: 'contract' | 'work_order' | 'quality' | 'inventory' | 'shipping',
  entityId: string,
  fn: () => Promise<T>
): Promise<T> {
  return tracer.startActiveSpan(
    `business.${processName}`,
    {
      kind: SpanKind.INTERNAL,
      attributes: {
        'business.process': processName,
        'business.type': processType,
        'business.entity_id': entityId,
        'service.component': 'business-logic',
      },
    },
    async (span) => {
      try {
        const result = await fn()
        span.setStatus({ code: SpanStatusCode.OK })
        span.setAttributes({
          'business.success': true,
        })
        return result
      } catch (error) {
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: error instanceof Error ? error.message : 'Business process error',
        })
        span.setAttributes({
          'business.success': false,
          'business.error': error instanceof Error ? error.message : 'Unknown error',
        })
        span.recordException(error instanceof Error ? error : new Error(String(error)))
        throw error
      } finally {
        span.end()
      }
    }
  )
}

/**
 * Add custom attributes to current span
 */
export function addSpanAttributes(attributes: Record<string, string | number | boolean>) {
  const span = trace.getActiveSpan()
  if (span) {
    span.setAttributes(attributes)
  }
}

/**
 * Record a custom event
 */
export function recordEvent(name: string, attributes?: Record<string, string | number | boolean>) {
  const span = trace.getActiveSpan()
  if (span) {
    span.addEvent(name, attributes)
  }
}

// ============================================================================
// MANUFACTURING ERP METRICS
// ============================================================================

/**
 * Track manufacturing workflow metrics
 */
export const ManufacturingMetrics = {
  /**
   * Track contract processing time
   */
  trackContractProcessing: (contractId: string, status: string) => {
    addSpanAttributes({
      'contract.id': contractId,
      'contract.status': status,
      'metric.type': 'contract_processing',
    })
  },

  /**
   * Track work order completion
   */
  trackWorkOrderCompletion: (workOrderId: string, duration: number) => {
    addSpanAttributes({
      'work_order.id': workOrderId,
      'work_order.duration_ms': duration,
      'metric.type': 'work_order_completion',
    })
  },

  /**
   * Track quality inspection results
   */
  trackQualityInspection: (inspectionId: string, result: 'passed' | 'failed' | 'pending') => {
    addSpanAttributes({
      'quality.inspection_id': inspectionId,
      'quality.result': result,
      'metric.type': 'quality_inspection',
    })
  },

  /**
   * Track inventory movements
   */
  trackInventoryMovement: (productId: string, quantity: number, type: 'in' | 'out') => {
    addSpanAttributes({
      'inventory.product_id': productId,
      'inventory.quantity': quantity,
      'inventory.movement_type': type,
      'metric.type': 'inventory_movement',
    })
  },

  /**
   * Track shipping performance
   */
  trackShipmentDelivery: (shipmentId: string, onTime: boolean, delayDays?: number) => {
    addSpanAttributes({
      'shipment.id': shipmentId,
      'shipment.on_time': onTime,
      'shipment.delay_days': delayDays || 0,
      'metric.type': 'shipment_delivery',
    })
  },
}

// ============================================================================
// SHUTDOWN HANDLER
// ============================================================================

export function shutdownHoneycomb() {
  if (sdk) {
    sdk.shutdown()
    console.log('🍯 Honeycomb: Shutdown complete')
  }
}

// Handle graceful shutdown
process.on('SIGTERM', shutdownHoneycomb)
process.on('SIGINT', shutdownHoneycomb)
