/**
 * Manufacturing ERP - Shipping Management Schema
 * Professional shipping module with complete workflow integration
 */

import { pgTable, text, decimal, timestamp, boolean, jsonb, index } from "drizzle-orm/pg-core"
import { relations } from "drizzle-orm"
import { companies, customers, products, salesContracts, stockLots, qualityCertificates, declarations } from "./schema-postgres"

// ============================================================================
// SHIPPING TABLES
// ============================================================================

/**
 * Shipments - Main shipping records
 * Integrates with sales contracts, customers, and inventory
 */
export const shipments = pgTable("shipments", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),

  // Shipment Identification
  shipment_number: text("shipment_number").notNull(), // Auto-generated: SH-2025-001
  sales_contract_id: text("sales_contract_id").references(() => salesContracts.id),
  customer_id: text("customer_id").notNull().references(() => customers.id),

  // ✅ EXPORT INTEGRATION: Link to export declaration
  export_declaration_id: text("export_declaration_id").references(() => declarations.id),

  // Shipping Details
  shipping_method: text("shipping_method").notNull(), // 'sea_freight', 'air_freight', 'express', 'truck'
  carrier: text("carrier"), // 'DHL', 'FedEx', 'Maersk', 'COSCO', 'UPS'
  service_type: text("service_type"), // 'standard', 'express', 'economy'
  tracking_number: text("tracking_number"),

  // Addresses (JSONB for flexibility)
  pickup_address: jsonb("pickup_address"), // Warehouse/factory address
  delivery_address: jsonb("delivery_address"), // Customer delivery address

  // ✅ PHASE 1 ENHANCEMENT: Location Integration
  pickup_location_id: text("pickup_location_id"), // Links to location system
  staging_location_id: text("staging_location_id"), // Shipping staging area
  pickup_instructions: jsonb("pickup_instructions"), // Location-specific instructions
  location_validated: boolean("location_validated").default(false), // Capacity validation flag

  // Status and Timeline
  status: text("status").default("preparing"), // 'preparing', 'ready', 'shipped', 'in_transit', 'delivered', 'cancelled'
  ship_date: text("ship_date"), // ISO date string
  estimated_delivery: text("estimated_delivery"), // ISO date string
  actual_delivery: text("actual_delivery"), // ISO date string

  // Financial Information
  shipping_cost: text("shipping_cost"), // Decimal as text for precision
  insurance_cost: text("insurance_cost"), // Decimal as text
  total_value: text("total_value"), // Total shipment value
  currency: text("currency").default("USD"),

  // Shipping Specifications
  total_weight: text("total_weight"), // In kg
  total_volume: text("total_volume"), // In cubic meters
  package_count: text("package_count").default("1"),

  // Documentation and Notes
  notes: text("notes"),
  special_instructions: text("special_instructions"),
  customs_declaration: text("customs_declaration"),

  // Quality and Compliance
  requires_certificate: boolean("requires_certificate").default(false),
  certificate_attached: boolean("certificate_attached").default(false),

  // Audit Trail
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
  created_by: text("created_by"),
  updated_by: text("updated_by"),
}, (table) => ({
  // Performance Indexes
  companyIdIdx: index("shipments_company_id_idx").on(table.company_id),
  statusIdx: index("shipments_status_idx").on(table.status),
  shipDateIdx: index("shipments_ship_date_idx").on(table.ship_date),
  customerIdIdx: index("shipments_customer_id_idx").on(table.customer_id),
  contractIdIdx: index("shipments_contract_id_idx").on(table.sales_contract_id),
  trackingIdx: index("shipments_tracking_idx").on(table.tracking_number),
  // ✅ PHASE 1 ENHANCEMENT: Location indexes
  pickupLocationIdx: index("shipments_pickup_location_idx").on(table.pickup_location_id),
  stagingLocationIdx: index("shipments_staging_location_idx").on(table.staging_location_id),
  // ✅ EXPORT INTEGRATION: Export declaration index
  exportDeclarationIdx: index("shipments_export_declaration_idx").on(table.export_declaration_id),
}))

/**
 * Shipment Items - Products being shipped
 * Links shipments to products and inventory
 */
export const shipmentItems = pgTable("shipment_items", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  shipment_id: text("shipment_id").notNull().references(() => shipments.id),
  product_id: text("product_id").notNull().references(() => products.id),
  stock_lot_id: text("stock_lot_id").references(() => stockLots.id), // Inventory allocation

  // Quantity and Pricing
  quantity: text("quantity").notNull(), // Decimal as text
  unit_price: text("unit_price"), // Decimal as text
  total_price: text("total_price"), // Calculated: quantity * unit_price

  // Product Specifications
  weight_per_unit: text("weight_per_unit"), // In kg
  volume_per_unit: text("volume_per_unit"), // In cubic meters

  // Quality Documentation
  quality_certificate_id: text("quality_certificate_id").references(() => qualityCertificates.id),
  batch_number: text("batch_number"),
  lot_number: text("lot_number"),

  // Item Status
  status: text("status").default("pending"), // 'pending', 'allocated', 'packed', 'shipped'

  // Audit
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("shipment_items_company_id_idx").on(table.company_id),
  shipmentIdIdx: index("shipment_items_shipment_id_idx").on(table.shipment_id),
  productIdIdx: index("shipment_items_product_id_idx").on(table.product_id),
  stockLotIdIdx: index("shipment_items_stock_lot_id_idx").on(table.stock_lot_id),
}))

/**
 * Shipping Documents - BOL, invoices, certificates, etc.
 * Professional document management for shipping
 */
export const shippingDocuments = pgTable("shipping_documents", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  shipment_id: text("shipment_id").notNull().references(() => shipments.id),

  // Document Information
  document_type: text("document_type").notNull(), // 'bill_of_lading', 'commercial_invoice', 'packing_list', 'certificate', 'customs_form'
  document_number: text("document_number"),
  document_name: text("document_name").notNull(),

  // File Information
  file_path: text("file_path"),
  file_size: text("file_size"), // In bytes
  file_type: text("file_type"), // MIME type

  // Document Status
  status: text("status").default("draft"), // 'draft', 'final', 'sent', 'archived'

  // Metadata
  description: text("description"),

  // Audit
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  created_by: text("created_by"),
}, (table) => ({
  companyIdIdx: index("shipping_documents_company_id_idx").on(table.company_id),
  shipmentIdIdx: index("shipping_documents_shipment_id_idx").on(table.shipment_id),
  typeIdx: index("shipping_documents_type_idx").on(table.document_type),
}))

/**
 * Shipping Tracking - Status updates and tracking history
 * Professional tracking with timeline
 */
export const shippingTracking = pgTable("shipping_tracking", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  shipment_id: text("shipment_id").notNull().references(() => shipments.id),

  // Tracking Information
  status: text("status").notNull(), // 'preparing', 'ready', 'shipped', 'in_transit', 'out_for_delivery', 'delivered', 'exception'
  location: text("location"), // Current location
  description: text("description").notNull(), // Status description

  // Carrier Information
  carrier_status: text("carrier_status"), // Carrier-specific status
  carrier_location: text("carrier_location"), // Carrier-provided location

  // Timing
  timestamp: timestamp("timestamp", { withTimezone: true }).notNull(),
  estimated_delivery: text("estimated_delivery"), // Updated ETA

  // Additional Information
  notes: text("notes"),

  // Audit
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  created_by: text("created_by"),
}, (table) => ({
  companyIdIdx: index("shipping_tracking_company_id_idx").on(table.company_id),
  shipmentIdIdx: index("shipping_tracking_shipment_id_idx").on(table.shipment_id),
  timestampIdx: index("shipping_tracking_timestamp_idx").on(table.timestamp),
}))

// ============================================================================
// RELATIONS
// ============================================================================

export const shipmentsRelations = relations(shipments, ({ one, many }) => ({
  company: one(companies, {
    fields: [shipments.company_id],
    references: [companies.id],
  }),
  customer: one(customers, {
    fields: [shipments.customer_id],
    references: [customers.id],
  }),
  salesContract: one(salesContracts, {
    fields: [shipments.sales_contract_id],
    references: [salesContracts.id],
  }),
  // ✅ EXPORT INTEGRATION: Export declaration relationship
  exportDeclaration: one(declarations, {
    fields: [shipments.export_declaration_id],
    references: [declarations.id],
  }),
  items: many(shipmentItems),
  documents: many(shippingDocuments),
  tracking: many(shippingTracking),
}))

export const shipmentItemsRelations = relations(shipmentItems, ({ one }) => ({
  company: one(companies, {
    fields: [shipmentItems.company_id],
    references: [companies.id],
  }),
  shipment: one(shipments, {
    fields: [shipmentItems.shipment_id],
    references: [shipments.id],
  }),
  product: one(products, {
    fields: [shipmentItems.product_id],
    references: [products.id],
  }),
  stockLot: one(stockLots, {
    fields: [shipmentItems.stock_lot_id],
    references: [stockLots.id],
  }),
  qualityCertificate: one(qualityCertificates, {
    fields: [shipmentItems.quality_certificate_id],
    references: [qualityCertificates.id],
  }),
}))

export const shippingDocumentsRelations = relations(shippingDocuments, ({ one }) => ({
  company: one(companies, {
    fields: [shippingDocuments.company_id],
    references: [companies.id],
  }),
  shipment: one(shipments, {
    fields: [shippingDocuments.shipment_id],
    references: [shipments.id],
  }),
}))

export const shippingTrackingRelations = relations(shippingTracking, ({ one }) => ({
  company: one(companies, {
    fields: [shippingTracking.company_id],
    references: [companies.id],
  }),
  shipment: one(shipments, {
    fields: [shippingTracking.shipment_id],
    references: [shipments.id],
  }),
}))
