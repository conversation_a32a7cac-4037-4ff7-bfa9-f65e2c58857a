/**
 * Manufacturing ERP - Location Utilization Service
 * 
 * Professional location-inventory integration service that calculates
 * real-time capacity utilization, provides capacity validation, and
 * generates utilization analytics for warehouse management.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Location-Inventory Integration
 */

import { db } from "@/lib/db"
import { stockLots, locations, rawMaterialLots, qualityInspections } from "@/lib/schema-postgres"
import { shipments } from "@/lib/schema-shipping"
import { eq, and, sql } from "drizzle-orm"

export interface LocationUtilization {
  locationId: string
  locationName: string
  locationIcon: string
  locationType: string
  capacity: number
  currentStock: number
  utilizationPercentage: number
  availableCapacity: number
  status: 'normal' | 'warning' | 'critical' | 'full'
  productCount: number
  lastUpdated: Date
}

export interface CapacityValidationResult {
  isValid: boolean
  canAccommodate: boolean
  availableCapacity: number
  utilizationAfter: number
  status: 'normal' | 'warning' | 'critical' | 'full'
  message: string
  alternativeLocations?: string[]
}

/**
 * Calculate real-time utilization for a specific location
 */
export async function calculateLocationUtilization(
  locationId: string,
  companyId: string
): Promise<LocationUtilization | null> {
  try {
    // Get location from database
    const locationConfig = await db.query.locations.findFirst({
      where: and(
        eq(locations.id, locationId),
        eq(locations.company_id, companyId)
      ),
    })
    if (!locationConfig) {
      return null
    }

    // ✅ ENHANCED: Calculate utilization based on location type
    const [stockData, rawMaterialData, qualityData, shippingData] = await Promise.all([
      // Finished goods stock lots
      db.query.stockLots.findMany({
        where: and(
          eq(stockLots.company_id, companyId),
          eq(stockLots.location, locationId)
        ),
        with: {
          product: true
        }
      }),
      // Raw material lots
      db.query.rawMaterialLots.findMany({
        where: and(
          eq(rawMaterialLots.company_id, companyId),
          eq(rawMaterialLots.location, locationId),
          eq(rawMaterialLots.status, 'available') // Only count available lots
        ),
        with: {
          rawMaterial: true
        }
      }),
      // Quality inspections (for quality_control locations)
      locationConfig.type === 'quality_control' ?
        db.query.qualityInspections.findMany({
          where: and(
            eq(qualityInspections.company_id, companyId),
            sql`${qualityInspections.status} IN ('pending', 'in-progress')`
          ),
          with: {
            workOrder: {
              with: {
                product: true
              }
            }
          }
        }) : Promise.resolve([]),
      // Shipments (for shipping locations)
      locationConfig.type === 'shipping' ?
        db.query.shipments?.findMany({
          where: and(
            eq(shipments.company_id, companyId),
            sql`(${shipments.pickup_location_id} = ${locationId} OR ${shipments.staging_location_id} = ${locationId})`,
            sql`${shipments.status} IN ('preparing', 'ready')`
          ),
          with: {
            items: {
              with: {
                product: true
              }
            }
          }
        }) || Promise.resolve([]) : Promise.resolve([])
    ])

    // ✅ ENHANCED: Calculate utilization based on location type
    let currentStock = 0
    let utilizationDescription = ""

    if (locationConfig.type === 'quality_control') {
      // For Quality Control Lab: count active inspections
      currentStock = qualityData.length
      utilizationDescription = `${qualityData.length} active inspections`
    } else if (locationConfig.type === 'shipping') {
      // For Shipping Dock: count items being staged/prepared
      const totalStagedItems = shippingData.reduce((total, shipment) => {
        return total + (shipment.items?.length || 0)
      }, 0)
      currentStock = totalStagedItems
      utilizationDescription = `${shippingData.length} shipments, ${totalStagedItems} items`
    } else {
      // For storage locations: count inventory quantities
      const finishedGoodsStock = stockData.reduce((total, lot) => {
        return total + parseFloat(lot.qty || '0')
      }, 0)

      const rawMaterialsStock = rawMaterialData.reduce((total, lot) => {
        return total + parseFloat(lot.qty || '0')
      }, 0)

      currentStock = finishedGoodsStock + rawMaterialsStock
      utilizationDescription = `${currentStock} units stored`
    }

    // ✅ ENHANCED: Count items based on location type
    let productCount = 0
    if (locationConfig.type === 'quality_control') {
      // For Quality Control: count unique products under inspection
      const productsUnderInspection = new Set(
        qualityData
          .filter(inspection => inspection.workOrder?.product_id)
          .map(inspection => inspection.workOrder!.product_id)
      )
      productCount = productsUnderInspection.size
    } else if (locationConfig.type === 'shipping') {
      // For Shipping: count unique products being shipped
      const productsBeingShipped = new Set()
      shippingData.forEach(shipment => {
        shipment.items?.forEach(item => {
          if (item.product_id) productsBeingShipped.add(item.product_id)
        })
      })
      productCount = productsBeingShipped.size
    } else {
      // For storage locations: count unique products and raw materials
      const finishedGoodsCount = new Set(stockData.map(lot => lot.product_id)).size
      const rawMaterialsCount = new Set(rawMaterialData.map(lot => lot.raw_material_id)).size
      productCount = finishedGoodsCount + rawMaterialsCount
    }
    const capacity = locationConfig.capacity || 1000
    const utilizationPercentage = Math.min((currentStock / capacity) * 100, 100)
    const availableCapacity = Math.max(capacity - currentStock, 0)

    // Determine status
    let status: 'normal' | 'warning' | 'critical' | 'full' = 'normal'
    if (utilizationPercentage >= 100) {
      status = 'full'
    } else if (utilizationPercentage >= 90) {
      status = 'critical'
    } else if (utilizationPercentage >= 80) {
      status = 'warning'
    }

    return {
      locationId,
      locationName: `📦 ${locationConfig.name}`,
      locationIcon: '📦',
      locationType: locationConfig.type,
      capacity,
      currentStock: Math.round(currentStock * 100) / 100,
      utilizationPercentage: Math.round(utilizationPercentage * 10) / 10,
      availableCapacity: Math.round(availableCapacity * 100) / 100,
      status,
      productCount,
      lastUpdated: new Date()
    }
  } catch (error) {
    console.error(`Error calculating utilization for location ${locationId}:`, error)
    return null
  }
}

/**
 * Calculate utilization for all locations with inventory
 */
export async function calculateAllLocationUtilization(
  companyId: string
): Promise<LocationUtilization[]> {
  try {
    // ✅ ENHANCED: Get all locations that have activity (inventory, inspections, shipments)
    const [finishedGoodsLocations, rawMaterialLocations, allLocations] = await Promise.all([
      db.query.stockLots.findMany({
        where: eq(stockLots.company_id, companyId),
        columns: {
          location: true
        }
      }),
      db.query.rawMaterialLots.findMany({
        where: and(
          eq(rawMaterialLots.company_id, companyId),
          eq(rawMaterialLots.status, 'available')
        ),
        columns: {
          location: true
        }
      }),
      // Get all locations to check for quality control and shipping activity
      db.query.locations.findMany({
        where: eq(locations.company_id, companyId),
        columns: {
          id: true,
          type: true
        }
      })
    ])

    // Include all locations that have inventory
    const inventoryLocations = [
      ...finishedGoodsLocations.map(lot => lot.location),
      ...rawMaterialLocations.map(lot => lot.location)
    ]

    // Include all quality control and shipping locations (they should always be checked)
    const activityLocations = allLocations
      .filter(loc => loc.type === 'quality_control' || loc.type === 'shipping')
      .map(loc => loc.id)

    const uniqueLocations = [...new Set([...inventoryLocations, ...activityLocations])]

    // Calculate utilization for each location
    const utilizationPromises = uniqueLocations.map(locationId =>
      calculateLocationUtilization(locationId, companyId)
    )

    const results = await Promise.all(utilizationPromises)
    return results.filter(result => result !== null) as LocationUtilization[]
  } catch (error) {
    console.error('Error calculating all location utilization:', error)
    return []
  }
}

/**
 * Validate if a location can accommodate additional stock
 */
export async function validateCapacity(
  locationId: string,
  additionalQuantity: number,
  companyId: string
): Promise<CapacityValidationResult> {
  try {
    const utilization = await calculateLocationUtilization(locationId, companyId)

    if (!utilization) {
      return {
        isValid: false,
        canAccommodate: false,
        availableCapacity: 0,
        utilizationAfter: 0,
        status: 'full',
        message: 'Location not found or invalid',
        alternativeLocations: []
      }
    }

    const canAccommodate = utilization.availableCapacity >= additionalQuantity
    const utilizationAfter = ((utilization.currentStock + additionalQuantity) / utilization.capacity) * 100

    let status: 'normal' | 'warning' | 'critical' | 'full' = 'normal'
    let message = ''

    if (!canAccommodate) {
      status = 'full'
      message = `Cannot accommodate ${additionalQuantity} units. Available capacity: ${utilization.availableCapacity} units.`
    } else if (utilizationAfter >= 90) {
      status = 'critical'
      message = `Warning: This will bring utilization to ${Math.round(utilizationAfter)}%. Consider alternative locations.`
    } else if (utilizationAfter >= 80) {
      status = 'warning'
      message = `Caution: This will bring utilization to ${Math.round(utilizationAfter)}%.`
    } else {
      message = `OK: Utilization will be ${Math.round(utilizationAfter)}% after receiving.`
    }

    // Get alternative locations if needed
    let alternativeLocations: string[] = []
    if (!canAccommodate || status === 'critical') {
      const allUtilizations = await calculateAllLocationUtilization(companyId)
      alternativeLocations = allUtilizations
        .filter(loc =>
          loc.locationId !== locationId &&
          loc.availableCapacity >= additionalQuantity &&
          loc.status !== 'full'
        )
        .sort((a, b) => b.availableCapacity - a.availableCapacity)
        .slice(0, 3)
        .map(loc => loc.locationName)
    }

    return {
      isValid: true,
      canAccommodate,
      availableCapacity: utilization.availableCapacity,
      utilizationAfter: Math.round(utilizationAfter * 10) / 10,
      status,
      message,
      alternativeLocations: alternativeLocations.length > 0 ? alternativeLocations : undefined
    }
  } catch (error) {
    console.error('Error validating capacity:', error)
    return {
      isValid: false,
      canAccommodate: false,
      availableCapacity: 0,
      utilizationAfter: 0,
      status: 'full',
      message: 'Error validating capacity',
      alternativeLocations: []
    }
  }
}

/**
 * Get location utilization summary for dashboard
 */
export async function getLocationUtilizationSummary(companyId: string) {
  try {
    const utilizations = await calculateAllLocationUtilization(companyId)

    const totalCapacity = utilizations.reduce((sum, loc) => sum + loc.capacity, 0)
    const totalUsed = utilizations.reduce((sum, loc) => sum + loc.currentStock, 0)
    const overallUtilization = totalCapacity > 0 ? (totalUsed / totalCapacity) * 100 : 0

    const statusCounts = {
      normal: utilizations.filter(loc => loc.status === 'normal').length,
      warning: utilizations.filter(loc => loc.status === 'warning').length,
      critical: utilizations.filter(loc => loc.status === 'critical').length,
      full: utilizations.filter(loc => loc.status === 'full').length
    }

    return {
      totalLocations: utilizations.length,
      totalCapacity,
      totalUsed: Math.round(totalUsed * 100) / 100,
      availableCapacity: Math.round((totalCapacity - totalUsed) * 100) / 100,
      overallUtilization: Math.round(overallUtilization * 10) / 10,
      statusCounts,
      locations: utilizations.sort((a, b) => b.utilizationPercentage - a.utilizationPercentage)
    }
  } catch (error) {
    console.error('Error getting utilization summary:', error)
    return {
      totalLocations: 0,
      totalCapacity: 0,
      totalUsed: 0,
      availableCapacity: 0,
      overallUtilization: 0,
      statusCounts: { normal: 0, warning: 0, critical: 0, full: 0 },
      locations: []
    }
  }
}

// ✅ PHASE 1 ENHANCEMENT: Shipping-Specific Utilization Functions

/**
 * Get shipping staging area utilization
 */
export async function getShippingStagingUtilization(companyId: string): Promise<LocationUtilization[]> {
  const shippingLocations = LocationManager.getLocationsByType('shipping_staging')
  const utilizations: LocationUtilization[] = []

  for (const location of shippingLocations) {
    const utilization = await calculateLocationUtilization(location.id, companyId)
    if (utilization) {
      utilizations.push(utilization)
    }
  }

  return utilizations
}

/**
 * Validate shipping capacity for staging areas
 */
export async function validateShippingCapacity(
  locationId: string,
  requiredCapacity: number,
  companyId: string
): Promise<CapacityValidationResult> {
  const utilization = await calculateLocationUtilization(locationId, companyId)

  if (!utilization) {
    return {
      isValid: false,
      canAccommodate: false,
      availableCapacity: 0,
      utilizationAfter: 0,
      status: 'full',
      message: 'Location not found or unavailable',
      alternativeLocations: []
    }
  }

  const canAccommodate = utilization.availableCapacity >= requiredCapacity
  const utilizationAfter = canAccommodate
    ? ((utilization.currentStock + requiredCapacity) / utilization.capacity) * 100
    : utilization.utilizationPercentage

  let status: 'normal' | 'warning' | 'critical' | 'full' = 'normal'
  if (utilizationAfter >= 100) status = 'full'
  else if (utilizationAfter >= 90) status = 'critical'
  else if (utilizationAfter >= 75) status = 'warning'

  // Get alternative locations if current location can't accommodate
  let alternativeLocations: string[] = []
  if (!canAccommodate) {
    const shippingLocations = await getShippingStagingUtilization(companyId)
    alternativeLocations = shippingLocations
      .filter(loc => loc.locationId !== locationId && loc.availableCapacity >= requiredCapacity)
      .map(loc => loc.locationId)
      .slice(0, 3) // Top 3 alternatives
  }

  return {
    isValid: true,
    canAccommodate,
    availableCapacity: utilization.availableCapacity,
    utilizationAfter,
    status,
    message: canAccommodate
      ? `✅ Can accommodate ${requiredCapacity} units. Utilization will be ${utilizationAfter.toFixed(1)}%`
      : `⚠️ Cannot accommodate ${requiredCapacity} units. Available: ${utilization.availableCapacity}`,
    alternativeLocations
  }
}
