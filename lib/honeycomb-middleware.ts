/**
 * Manufacturing ERP - Honeycomb API Middleware
 * 
 * Automatic tracing middleware for API routes with ERP-specific context
 */

import { NextRequest, NextResponse } from 'next/server'
import { traceApiEndpoint, addSpanAttributes, ManufacturingMetrics } from './honeycomb'

/**
 * Honeycomb middleware for API routes
 * Automatically traces all API calls with relevant context
 */
export function withHoneycombTracing<T extends any[]>(
  handler: (req: NextRequest, ...args: T) => Promise<NextResponse>,
  options?: {
    operationName?: string
    skipTracing?: boolean
  }
) {
  return async (req: NextRequest, ...args: T): Promise<NextResponse> => {
    // Skip tracing if disabled
    if (options?.skipTracing || process.env.HONEYCOMB_DISABLED === 'true') {
      return handler(req, ...args)
    }

    const method = req.method
    const pathname = new URL(req.url).pathname
    const operationName = options?.operationName || `${method} ${pathname}`

    return traceApiEndpoint(operationName, method, async () => {
      // Add request context
      addSpanAttributes({
        'http.url': req.url,
        'http.user_agent': req.headers.get('user-agent') || 'unknown',
        'http.content_length': req.headers.get('content-length') || '0',
      })

      // Add ERP-specific context from URL
      const pathSegments = pathname.split('/').filter(Boolean)
      if (pathSegments.length >= 2 && pathSegments[0] === 'api') {
        const module = pathSegments[1]
        addSpanAttributes({
          'erp.module': module,
          'erp.operation': method.toLowerCase(),
        })

        // Add entity ID if present
        if (pathSegments.length >= 3 && pathSegments[2] !== 'route.ts') {
          addSpanAttributes({
            'erp.entity_id': pathSegments[2],
          })
        }
      }

      const startTime = Date.now()
      
      try {
        const response = await handler(req, ...args)
        
        const duration = Date.now() - startTime
        
        // Add response context
        addSpanAttributes({
          'http.status_code': response.status,
          'http.response_time_ms': duration,
          'erp.success': response.status < 400,
        })

        // Track performance metrics
        if (duration > 1000) {
          addSpanAttributes({
            'erp.slow_request': true,
            'erp.performance_warning': 'Request took longer than 1 second',
          })
        }

        return response
      } catch (error) {
        const duration = Date.now() - startTime
        
        addSpanAttributes({
          'http.status_code': 500,
          'http.response_time_ms': duration,
          'erp.success': false,
          'erp.error': error instanceof Error ? error.message : 'Unknown error',
        })

        throw error
      }
    })
  }
}

/**
 * Enhanced middleware that includes tenant context
 */
export function withHoneycombTenantTracing<T extends any[]>(
  handler: (req: NextRequest, context: { companyId: string; userId: string }, ...args: T) => Promise<NextResponse>,
  options?: {
    operationName?: string
    skipTracing?: boolean
  }
) {
  return async (req: NextRequest, ...args: T): Promise<NextResponse> => {
    return withHoneycombTracing(async (req: NextRequest) => {
      // Extract tenant context (this would integrate with your existing auth)
      const session = await getSessionFromRequest(req)
      if (!session) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      const context = {
        companyId: session.user.companyId,
        userId: session.user.id,
      }

      // Add tenant context to trace
      addSpanAttributes({
        'erp.company_id': context.companyId,
        'erp.user_id': context.userId,
        'erp.tenant_isolated': true,
      })

      return handler(req, context, ...args)
    }, options)(req, ...args)
  }
}

/**
 * Helper to extract session from request
 * This should integrate with your existing auth system
 */
async function getSessionFromRequest(req: NextRequest) {
  try {
    // This would integrate with your Auth0 session handling
    // For now, return null to indicate no session
    return null
  } catch (error) {
    return null
  }
}

/**
 * Utility to track specific ERP operations
 */
export const ERPTracing = {
  /**
   * Track contract operations
   */
  trackContractOperation: (operation: string, contractId: string, status?: string) => {
    addSpanAttributes({
      'erp.contract.operation': operation,
      'erp.contract.id': contractId,
      'erp.contract.status': status || 'unknown',
    })
    
    if (status) {
      ManufacturingMetrics.trackContractProcessing(contractId, status)
    }
  },

  /**
   * Track work order operations
   */
  trackWorkOrderOperation: (operation: string, workOrderId: string, duration?: number) => {
    addSpanAttributes({
      'erp.work_order.operation': operation,
      'erp.work_order.id': workOrderId,
    })
    
    if (duration) {
      ManufacturingMetrics.trackWorkOrderCompletion(workOrderId, duration)
    }
  },

  /**
   * Track quality operations
   */
  trackQualityOperation: (operation: string, inspectionId: string, result?: string) => {
    addSpanAttributes({
      'erp.quality.operation': operation,
      'erp.quality.inspection_id': inspectionId,
      'erp.quality.result': result || 'pending',
    })
    
    if (result && ['passed', 'failed', 'pending'].includes(result)) {
      ManufacturingMetrics.trackQualityInspection(inspectionId, result as any)
    }
  },

  /**
   * Track inventory operations
   */
  trackInventoryOperation: (operation: string, productId: string, quantity?: number, type?: 'in' | 'out') => {
    addSpanAttributes({
      'erp.inventory.operation': operation,
      'erp.inventory.product_id': productId,
      'erp.inventory.quantity': quantity || 0,
      'erp.inventory.type': type || 'unknown',
    })
    
    if (quantity && type) {
      ManufacturingMetrics.trackInventoryMovement(productId, quantity, type)
    }
  },

  /**
   * Track shipping operations
   */
  trackShippingOperation: (operation: string, shipmentId: string, onTime?: boolean, delayDays?: number) => {
    addSpanAttributes({
      'erp.shipping.operation': operation,
      'erp.shipping.shipment_id': shipmentId,
      'erp.shipping.on_time': onTime || false,
      'erp.shipping.delay_days': delayDays || 0,
    })
    
    if (typeof onTime === 'boolean') {
      ManufacturingMetrics.trackShipmentDelivery(shipmentId, onTime, delayDays)
    }
  },
}
