# 🧪 Export Declaration Module - Comprehensive Testing Instructions

## **📋 OVERVIEW**

This document provides comprehensive testing instructions for the enhanced Export Declaration module with shipping integration, improved document management, and complete workflow integration.

---

## **🔧 PRE-TESTING SETUP**

### **1. Database Schema Updates**
The following schema changes have been implemented:

```sql
-- ✅ SHIPPING INTEGRATION: Added export_declaration_id to shipments table
ALTER TABLE shipments ADD COLUMN export_declaration_id TEXT REFERENCES declarations(id);
CREATE INDEX shipments_export_declaration_idx ON shipments(export_declaration_id);
```

### **2. Required Test Data**
Ensure you have the following test data in your system:
- At least 2 active customers
- At least 3 products with HS codes
- At least 1 approved sales contract
- At least 1 completed work order
- At least 1 shipped shipment (status = "shipped")
- At least 1 export declaration in draft status

---

## **🧪 TESTING SCENARIOS**

### **SCENARIO 1: Shipping Integration Testing**

#### **Test 1.1: Link Shipment to Export Declaration**
1. **Navigate** to `/export` page
2. **Click** on an existing export declaration
3. **Verify** the "Linked Shipments" section is visible
4. **Click** the dropdown in "Link New Shipment" section
5. **Verify** only shipped shipments appear in the dropdown
6. **Select** a shipment and click "Link"
7. **Expected Result**: 
   - Success toast notification appears
   - Shipment appears in the linked shipments table
   - Shipment shows correct customer, status, and ship date

#### **Test 1.2: Unlink Shipment from Export Declaration**
1. **From** the linked shipments table
2. **Click** the unlink button (chain icon) for a linked shipment
3. **Expected Result**:
   - Success toast notification appears
   - Shipment disappears from linked shipments table
   - Shipment becomes available in the dropdown again

#### **Test 1.3: Multi-Tenant Security Verification**
1. **Login** as user from Company A
2. **Create** an export declaration and link a shipment
3. **Login** as user from Company B
4. **Navigate** to export declarations
5. **Expected Result**:
   - Company A's export declarations are not visible
   - Company A's shipments are not available for linking

### **SCENARIO 2: Enhanced Document Management Testing**

#### **Test 2.1: Multiple File Upload**
1. **Navigate** to an export declaration detail page
2. **In** the "Export Documents" section
3. **Select** multiple files (PDF, DOC, XLS, JPG)
4. **Click** upload
5. **Expected Result**:
   - Upload progress indicator appears
   - Success toast shows number of files uploaded
   - All files appear in the uploaded documents list
   - Each file shows appropriate icon based on type

#### **Test 2.2: File Type Icons and Actions**
1. **Upload** different file types (PDF, DOC, XLS, JPG)
2. **Verify** each file shows the correct colored icon:
   - PDF: Red FileText icon
   - DOC/DOCX: Blue FileText icon
   - XLS/XLSX: Green FileSpreadsheet icon
   - JPG/PNG: Purple Image icon
3. **Test** each action button:
   - Eye icon: Opens file in new tab
   - Download icon: Downloads file
   - Trash icon: Shows delete confirmation

#### **Test 2.3: Document Deletion**
1. **Click** the trash icon for a document
2. **Confirm** deletion in the alert dialog
3. **Expected Result**:
   - Success toast notification
   - Document disappears from list
   - File is removed from storage

### **SCENARIO 3: Bilingual Support Testing**

#### **Test 3.1: English Interface**
1. **Set** language to English
2. **Navigate** through export declaration pages
3. **Verify** all text displays in English:
   - "Export Documents" section title
   - "Linked Shipments" section title
   - Button labels ("Link", "Upload Documents")
   - Table headers and status labels

#### **Test 3.2: Chinese Interface**
1. **Set** language to Chinese
2. **Navigate** through export declaration pages
3. **Verify** all text displays in Chinese:
   - "出口文档" section title
   - "关联运输" section title
   - Button labels ("关联", "上传文档")
   - Table headers and status labels

### **SCENARIO 4: Complete Workflow Integration Testing**

#### **Test 4.1: End-to-End Workflow**
1. **Create** a sales contract with customer and products
2. **Approve** the sales contract
3. **Generate** work orders from the contract
4. **Complete** the work orders
5. **Create** a shipment from the contract
6. **Ship** the shipment (status = "shipped")
7. **Verify** inventory is depleted when shipment is shipped
8. **Create** an export declaration
9. **Link** the shipment to the export declaration
10. **Upload** required export documents
11. **Expected Result**: Complete traceability from contract to export

#### **Test 4.2: Inventory Depletion Verification**
1. **Check** inventory levels before shipping
2. **Ship** a shipment with specific quantities
3. **Verify** inventory is reduced by exact shipped quantities
4. **Create** export declaration and link shipment
5. **Verify** inventory is NOT reduced again (no double depletion)

---

## **🔍 VERIFICATION CHECKLIST**

### **✅ Functional Requirements**
- [ ] Export declarations can be linked to multiple shipments
- [ ] Shipments can only be linked to one export declaration
- [ ] Document upload supports multiple file types
- [ ] File type icons display correctly
- [ ] Document actions (view, download, delete) work properly
- [ ] Multi-tenant security prevents cross-company access
- [ ] Bilingual support works for all new features

### **✅ Performance Requirements**
- [ ] Page load times under 2 seconds
- [ ] File upload completes within reasonable time
- [ ] Database queries execute efficiently with proper indexing
- [ ] No N+1 query problems in shipment loading

### **✅ Security Requirements**
- [ ] All API endpoints use withTenantAuth middleware
- [ ] Database queries filter by company_id
- [ ] File uploads are stored securely with proper naming
- [ ] Cross-tenant data access is impossible

### **✅ User Experience Requirements**
- [ ] Professional toast notifications for all actions
- [ ] Loading states during async operations
- [ ] Responsive design works on mobile/tablet/desktop
- [ ] Error messages are user-friendly and actionable

---

## **🚨 CRITICAL TEST CASES**

### **Security Test Cases**
1. **Attempt** to access another company's export declaration by URL manipulation
2. **Try** to link a shipment from another company
3. **Attempt** to upload files to another company's declaration

### **Data Integrity Test Cases**
1. **Verify** shipment can only be linked to one declaration at a time
2. **Check** that unlinking doesn't affect other relationships
3. **Ensure** document deletion doesn't break declaration integrity

### **Error Handling Test Cases**
1. **Upload** invalid file types
2. **Try** to link already-linked shipment
3. **Test** network failures during upload/linking operations

---

## **📊 EXPECTED OUTCOMES**

### **Successful Implementation Indicators**
- All test scenarios pass without errors
- Professional user experience with proper feedback
- Complete workflow traceability maintained
- Multi-tenant security verified
- Bilingual support fully functional
- Performance benchmarks met

### **Integration Success Metrics**
- Export declarations properly linked to shipments
- Document management follows established patterns
- Inventory depletion occurs only during shipping (not export declaration)
- Database relationships maintain referential integrity
- API responses follow established patterns

---

## **🔧 TROUBLESHOOTING**

### **Common Issues and Solutions**

**Issue**: Shipments not appearing in dropdown
**Solution**: Verify shipment status is "shipped" and not already linked

**Issue**: File upload fails
**Solution**: Check file size limits and supported formats

**Issue**: Cross-tenant data visible
**Solution**: Verify withTenantAuth middleware is applied to all endpoints

**Issue**: Chinese translations not displaying
**Solution**: Check i18n configuration and translation file imports

---

## **📝 TESTING COMPLETION**

After completing all test scenarios:

1. **Document** any issues found during testing
2. **Verify** all critical functionality works as expected
3. **Confirm** the complete workflow integration is seamless
4. **Validate** that existing functionality remains unaffected
5. **Sign off** on the enhanced Export Declaration module

**Testing Status**: ⏳ Ready for Testing
**Expected Duration**: 2-3 hours for comprehensive testing
**Prerequisites**: Test data setup and schema updates applied
