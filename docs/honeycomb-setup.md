# 🍯 Honeycomb Setup Guide for Manufacturing ERP

## Overview

This guide helps you set up Honeycomb observability for your Manufacturing ERP system to gain deep insights into:

- **API Performance**: Track response times, error rates, and throughput
- **Database Operations**: Monitor query performance and identify bottlenecks  
- **Business Processes**: Observe manufacturing workflows end-to-end
- **User Experience**: Track page loads and user interactions
- **Multi-tenant Isolation**: Ensure proper tenant data separation

## 🚀 Quick Setup

### 1. Get Your Honeycomb API Key

1. Go to [Honeycomb](https://ui.honeycomb.io/account)
2. Sign in or create an account
3. Navigate to **Account Settings** → **API Keys**
4. Create a new API key for your Manufacturing ERP
5. Copy the API key

### 2. Configure Environment Variables

Add to your `.env.local` file:

```bash
# Honeycomb Configuration
HONEYCOMB_API_KEY=your_api_key_here
HONEYCOMB_ENABLED=true
HONEYCOMB_DATASET=manufacturing-erp
```

### 3. Test the Integration

1. Start your development server: `npm run dev`
2. Visit: `http://localhost:3000/api/honeycomb-test`
3. Check your Honeycomb dashboard for traces

## 📊 Manufacturing ERP Dashboards

### Recommended Queries for Your ERP

#### 1. API Performance Dashboard
```
BREAKDOWN BY http.route
| CALCULATE AVG(duration_ms), P95(duration_ms), COUNT
| ORDER BY AVG(duration_ms) DESC
```

#### 2. Business Process Monitoring
```
WHERE business.process EXISTS
| BREAKDOWN BY business.type, business.process
| CALCULATE AVG(duration_ms), COUNT, MAX(duration_ms)
```

#### 3. Database Performance
```
WHERE db.operation EXISTS  
| BREAKDOWN BY db.table, db.operation
| CALCULATE AVG(duration_ms), P99(duration_ms), COUNT
```

#### 4. Multi-tenant Isolation Check
```
WHERE erp.company_id EXISTS
| BREAKDOWN BY erp.company_id
| CALCULATE COUNT, COUNTDISTINCT(erp.user_id)
```

#### 5. Error Rate Monitoring
```
WHERE erp.success = false
| BREAKDOWN BY erp.module, http.status_code
| CALCULATE COUNT
```

## 🔧 Integration with Existing API Routes

### Basic API Route Tracing

```typescript
import { withHoneycombTracing } from '@/lib/honeycomb-middleware'

export const GET = withHoneycombTracing(
  async function GET(req: NextRequest) {
    // Your existing API logic
    return NextResponse.json({ data: 'your data' })
  }
)
```

### Advanced ERP-Specific Tracing

```typescript
import { ERPTracing } from '@/lib/honeycomb-middleware'
import { traceBusinessProcess } from '@/lib/honeycomb'

export const POST = withHoneycombTracing(
  async function POST(req: NextRequest) {
    const contractId = 'contract-123'
    
    return traceBusinessProcess(
      'contract_approval',
      'contract', 
      contractId,
      async () => {
        // Track the operation
        ERPTracing.trackContractOperation('approve', contractId, 'approved')
        
        // Your business logic
        const result = await approveContract(contractId)
        return NextResponse.json(result)
      }
    )
  }
)
```

## 📈 Key Metrics to Monitor

### Performance Metrics
- **API Response Times**: P50, P95, P99 latencies
- **Database Query Performance**: Slow queries > 100ms
- **Page Load Times**: Frontend performance
- **Error Rates**: 4xx and 5xx responses

### Business Metrics
- **Contract Processing Time**: From creation to approval
- **Work Order Completion**: Manufacturing cycle times
- **Quality Inspection Duration**: QC process efficiency
- **Inventory Movement Speed**: Stock transaction processing
- **Shipping Performance**: On-time delivery rates

### Operational Metrics
- **Multi-tenant Isolation**: Ensure no cross-tenant data access
- **User Activity**: Track module usage patterns
- **System Health**: Resource utilization and availability

## 🚨 Alerting Setup

### Critical Alerts
1. **High Error Rate**: > 5% error rate in any 5-minute window
2. **Slow API Responses**: P95 > 2 seconds
3. **Database Issues**: Query time > 1 second
4. **Multi-tenant Violations**: Cross-tenant data access detected

### Business Process Alerts
1. **Stuck Workflows**: Processes taking > 2x normal time
2. **Quality Issues**: High rejection rates
3. **Inventory Problems**: Stock movement failures
4. **Shipping Delays**: Late deliveries increasing

## 🔍 Debugging with Honeycomb

### Finding Performance Issues
1. Use the **Query Builder** to filter by slow requests
2. **Trace View** shows the complete request flow
3. **BubbleUp** identifies what makes slow requests different

### Investigating Errors
1. Filter by `erp.success = false`
2. Group by error type and module
3. Use trace details to see the full error context

### Multi-tenant Debugging
1. Filter by specific `erp.company_id`
2. Verify no cross-tenant data appears
3. Track user activity patterns

## 🎯 Next Steps

1. **Set up your API key** in environment variables
2. **Test the integration** with the test endpoint
3. **Add tracing** to your most critical API routes
4. **Create dashboards** for your key metrics
5. **Set up alerts** for critical issues
6. **Monitor and optimize** based on the data

## 📚 Additional Resources

- [Honeycomb Documentation](https://docs.honeycomb.io/)
- [OpenTelemetry Guide](https://opentelemetry.io/docs/)
- [Next.js Instrumentation](https://nextjs.org/docs/app/building-your-application/optimizing/instrumentation)

---

**🎉 With Honeycomb set up, you'll have unprecedented visibility into your Manufacturing ERP system's performance and behavior!**
