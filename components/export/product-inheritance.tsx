/**
 * Manufacturing ERP - Product Inheritance Component
 * 
 * Manages product inheritance from sales contracts and manual product addition
 * Following established ERP patterns with full user control
 */

"use client"

import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { SearchableSelect, SearchableSelectOption } from "@/components/ui/searchable-select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Plus, Trash2, Package, Download } from "lucide-react"
import { useI18n } from "@/components/i18n-provider"

interface Product {
  id: string
  name: string
  hs_code?: string
}

interface DeclarationItem {
  id: string // Temporary ID for UI
  product_id: string
  product?: Product
  qty: string
  hs_code: string
  quality_status: string
}

interface SalesContract {
  id: string
  number: string
  items: Array<{
    id: string
    product: Product
    qty: string
  }>
}

interface ProductInheritanceProps {
  selectedContract: SalesContract | null
  items: DeclarationItem[]
  onItemsChange: (items: DeclarationItem[]) => void
  className?: string
}

export function ProductInheritance({
  selectedContract,
  items,
  onItemsChange,
  className,
}: ProductInheritanceProps) {
  const { t } = useI18n()
  const [availableProducts, setAvailableProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(false)

  // ✅ PROFESSIONAL: Load available products for manual addition
  useEffect(() => {
    loadProducts()
  }, [])

  // ✅ PROFESSIONAL: Auto-populate from contract when selected
  useEffect(() => {
    if (selectedContract && items.length === 0) {
      inheritFromContract()
    }
  }, [selectedContract])

  const loadProducts = async () => {
    try {
      const response = await fetch('/api/products')
      if (response.ok) {
        const result = await response.json()
        // ✅ DEFENSIVE: Handle API response format { success: true, data: [...] }
        const productsArray = Array.isArray(result.data) ? result.data : Array.isArray(result) ? result : []
        setAvailableProducts(productsArray)
      }
    } catch (error) {
      console.error('Error loading products:', error)
    }
  }

  // ✅ BUSINESS LOGIC: Inherit products from selected contract
  const inheritFromContract = () => {
    if (!selectedContract) return

    const inheritedItems: DeclarationItem[] = selectedContract.items.map((contractItem, index) => ({
      id: `inherited-${index}`, // Temporary UI ID
      product_id: contractItem.product.id,
      product: contractItem.product,
      qty: contractItem.qty,
      hs_code: contractItem.product.hs_code || getDefaultHSCode(contractItem.product),
      quality_status: 'pending',
    }))

    onItemsChange(inheritedItems)
  }

  // ✅ PROFESSIONAL: Add new manual product
  const addManualProduct = () => {
    const newItem: DeclarationItem = {
      id: `manual-${Date.now()}`, // Temporary UI ID
      product_id: '',
      qty: '',
      hs_code: '',
      quality_status: 'pending',
    }

    onItemsChange([...items, newItem])
  }

  // ✅ SEARCHABLE OPTIONS: Convert products to searchable options
  const productOptions: SearchableSelectOption[] = availableProducts.map((product) => ({
    value: product.id,
    label: product.name,
    subtitle: product.hs_code ? `HS Code: ${product.hs_code}` : 'No HS code',
    description: `📦 Product for export declaration`,
  }))

  // ✅ PROFESSIONAL: Remove product
  const removeItem = (itemId: string) => {
    onItemsChange(items.filter(item => item.id !== itemId))
  }

  // ✅ PROFESSIONAL: Update item field
  const updateItem = (itemId: string, field: keyof DeclarationItem, value: string) => {
    const updatedItems = items.map(item => {
      if (item.id === itemId) {
        const updatedItem = { ...item, [field]: value }

        // ✅ BUSINESS LOGIC: Auto-populate HS code when product is selected
        if (field === 'product_id' && value) {
          const selectedProduct = availableProducts.find(p => p.id === value)
          if (selectedProduct) {
            updatedItem.product = selectedProduct
            updatedItem.hs_code = selectedProduct.hs_code || getDefaultHSCode(selectedProduct)
          }
        }

        return updatedItem
      }
      return item
    })

    onItemsChange(updatedItems)
  }

  // ✅ BUSINESS LOGIC: Get default HS code based on product
  const getDefaultHSCode = (product: Product): string => {
    // This should be enhanced with proper HS code mapping
    if (product.name.toLowerCase().includes('textile')) {
      return '5007.20' // Silk fabrics
    }
    if (product.name.toLowerCase().includes('garment')) {
      return '6204.42' // Women's garments
    }
    return '9999.00' // Default/unclassified
  }

  // ✅ PROFESSIONAL: Clear all items and re-inherit from contract
  const reInheritFromContract = () => {
    onItemsChange([])
    setTimeout(() => inheritFromContract(), 100) // Small delay to ensure state update
  }

  return (
    <div className={className}>
      <div className="flex items-center justify-between mb-4">
        <Label className="text-base font-medium">{t("export.form.declaration_items")}</Label>
        <div className="flex items-center gap-2">
          {selectedContract && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={reInheritFromContract}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              {t("export.products.inherit_button")}
            </Button>
          )}
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addManualProduct}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            {t("export.products.add_manual")}
          </Button>
        </div>
      </div>

      {items.length === 0 ? (
        <div className="border rounded-md p-8 text-center text-muted-foreground">
          <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <div className="text-lg font-medium mb-2">{t("export.products.no_items")}</div>
          <div className="text-sm mb-4">
            {selectedContract
              ? `${t("export.products.inherit_button")} ${selectedContract.number}, ${t("export.products.add_manual")}.`
              : t("export.products.no_items")
            }
          </div>
        </div>
      ) : (
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t("export.products.table.product")}</TableHead>
                <TableHead>{t("export.products.table.quantity")}</TableHead>
                <TableHead>{t("export.products.table.hs_code")}</TableHead>
                <TableHead>{t("export.products.table.quality")}</TableHead>
                <TableHead className="w-[50px]">{t("export.products.table.actions")}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {items.map((item) => (
                <TableRow key={item.id}>
                  <TableCell>
                    <SearchableSelect
                      options={productOptions}
                      value={item.product_id}
                      onValueChange={(value) => updateItem(item.id, 'product_id', value)}
                      placeholder={t("export.products.select_product")}
                      searchPlaceholder={t("export.products.select_product")}
                      emptyMessage={t("export.products.error")}
                      allowClear={true}
                    />
                  </TableCell>
                  <TableCell>
                    <Input
                      type="number"
                      value={item.qty}
                      onChange={(e) => updateItem(item.id, 'qty', e.target.value)}
                      placeholder={t("export.products.quantity_placeholder")}
                      min="0"
                    />
                  </TableCell>
                  <TableCell>
                    <Input
                      value={item.hs_code}
                      onChange={(e) => updateItem(item.id, 'hs_code', e.target.value)}
                      placeholder={t("export.products.hs_code_placeholder")}
                    />
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="text-xs">
                      {item.quality_status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeItem(item.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {selectedContract && items.length > 0 && (
        <div className="mt-2 text-sm text-muted-foreground">
          {t("export.products.inherited")} <strong>{selectedContract.number}</strong>.
        </div>
      )}
    </div>
  )
}
