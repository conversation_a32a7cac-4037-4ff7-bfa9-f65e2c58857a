/**
 * Manufacturing ERP - Export Declaration Shipment Manager
 * Professional component for managing shipment-declaration relationships
 */

"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Truck,
  Link as LinkIcon,
  Unlink,
  Package,
  Calendar,
  MapPin,
  User
} from "lucide-react"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"
import { safeJson } from "@/lib/safe-fetch"

interface Shipment {
  id: string
  shipment_number: string
  status: string
  ship_date?: string
  customer: {
    name: string
  }
  salesContract?: {
    number: string
  }
  items: Array<{
    id: string
    quantity: string
    product: {
      name: string
      sku: string
    }
  }>
}

interface ShipmentManagerProps {
  declarationId: string
}

export function ShipmentManager({ declarationId }: ShipmentManagerProps) {
  const { t } = useI18n()
  const { toast } = useSafeToast()
  const [linkedShipments, setLinkedShipments] = useState<Shipment[]>([])
  const [availableShipments, setAvailableShipments] = useState<Shipment[]>([])
  const [selectedShipmentId, setSelectedShipmentId] = useState<string>("")
  const [loading, setLoading] = useState(true)
  const [linking, setLinking] = useState(false)

  // Load linked shipments
  const loadLinkedShipments = async () => {
    try {
      const data = await safeJson(`/api/export/declarations/${declarationId}/shipments`, [])
      setLinkedShipments(data)
    } catch (error) {
      console.error("Failed to load linked shipments:", error)
    }
  }

  // Load available shipments (not linked to any declaration)
  const loadAvailableShipments = async () => {
    try {
      const response = await safeJson("/api/shipping/shipments?status=shipped&unlinked=true", { shipments: [] })
      // Handle both direct array and object with shipments property
      const shipmentsList = Array.isArray(response) ? response : (response.shipments || [])
      setAvailableShipments(shipmentsList)
    } catch (error) {
      console.error("Failed to load available shipments:", error)
      setAvailableShipments([])
    }
  }

  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      await Promise.all([
        loadLinkedShipments(),
        loadAvailableShipments()
      ])
      setLoading(false)
    }
    loadData()
  }, [declarationId])

  // Link shipment to declaration
  const handleLinkShipment = async () => {
    if (!selectedShipmentId) return

    setLinking(true)
    try {
      const response = await fetch(`/api/export/declarations/${declarationId}/shipments`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ shipment_id: selectedShipmentId }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to link shipment")
      }

      toast({
        title: t("export.shipments.link_success"),
        description: t("export.shipments.link_success_desc"),
      })

      // Refresh data
      await Promise.all([
        loadLinkedShipments(),
        loadAvailableShipments()
      ])
      setSelectedShipmentId("")
    } catch (error) {
      toast({
        title: t("export.shipments.link_failed"),
        description: error instanceof Error ? error.message : t("export.shipments.link_failed_desc"),
        variant: "destructive",
      })
    } finally {
      setLinking(false)
    }
  }

  // Unlink shipment from declaration
  const handleUnlinkShipment = async (shipmentId: string) => {
    try {
      const response = await fetch(`/api/export/declarations/${declarationId}/shipments`, {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ shipment_id: shipmentId }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to unlink shipment")
      }

      toast({
        title: t("export.shipments.unlink_success"),
        description: t("export.shipments.unlink_success_desc"),
      })

      // Refresh data
      await Promise.all([
        loadLinkedShipments(),
        loadAvailableShipments()
      ])
    } catch (error) {
      toast({
        title: t("export.shipments.unlink_failed"),
        description: error instanceof Error ? error.message : t("export.shipments.unlink_failed_desc"),
        variant: "destructive",
      })
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      preparing: { variant: "secondary" as const, label: "Preparing" },
      ready: { variant: "default" as const, label: "Ready" },
      shipped: { variant: "default" as const, label: "Shipped" },
      in_transit: { variant: "default" as const, label: "In Transit" },
      delivered: { variant: "default" as const, label: "Delivered" },
    }

    const config = statusConfig[status as keyof typeof statusConfig] || { variant: "secondary" as const, label: status }
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            {t("export.shipments.title")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">{t("export.shipments.loading")}</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Truck className="h-5 w-5" />
          {t("export.shipments.title")}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* ✅ PROFESSIONAL ERP: Link new shipment section */}
        <div className="space-y-4">
          <h4 className="font-semibold">{t("export.shipments.link_new")}</h4>
          <div className="flex gap-2">
            <Select value={selectedShipmentId} onValueChange={setSelectedShipmentId}>
              <SelectTrigger className="flex-1">
                <SelectValue placeholder={t("export.shipments.select_shipment")} />
              </SelectTrigger>
              <SelectContent>
                {availableShipments.map((shipment) => (
                  <SelectItem key={shipment.id} value={shipment.id}>
                    {shipment.shipment_number} - {shipment.customer.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button
              onClick={handleLinkShipment}
              disabled={!selectedShipmentId || linking}
            >
              <LinkIcon className="h-4 w-4 mr-2" />
              {linking ? t("export.shipments.linking") : t("export.shipments.link")}
            </Button>
          </div>
        </div>

        {/* ✅ PROFESSIONAL ERP: Linked shipments table */}
        <div className="space-y-4">
          <h4 className="font-semibold">{t("export.shipments.linked_title")}</h4>
          {linkedShipments.length === 0 ? (
            <p className="text-sm text-muted-foreground">{t("export.shipments.no_shipments")}</p>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("export.shipments.table.number")}</TableHead>
                    <TableHead>{t("export.shipments.table.customer")}</TableHead>
                    <TableHead>{t("export.shipments.table.status")}</TableHead>
                    <TableHead>{t("export.shipments.table.ship_date")}</TableHead>
                    <TableHead>{t("export.shipments.table.items")}</TableHead>
                    <TableHead>{t("export.shipments.table.actions")}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {linkedShipments.map((shipment) => (
                    <TableRow key={shipment.id}>
                      <TableCell className="font-medium">
                        {shipment.shipment_number}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          {shipment.customer.name}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(shipment.status)}
                      </TableCell>
                      <TableCell>
                        {shipment.ship_date ? (
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            {new Date(shipment.ship_date).toLocaleDateString()}
                          </div>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Package className="h-4 w-4 text-muted-foreground" />
                          {shipment.items.length} items
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleUnlinkShipment(shipment.id)}
                        >
                          <Unlink className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
