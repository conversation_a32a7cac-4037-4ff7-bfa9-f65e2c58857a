"use client"

import { useI18n } from "@/components/i18n-provider"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"

interface BackButtonProps {
  href: string
}

export function BackButton({ href }: BackButtonProps) {
  const { t } = useI18n()
  
  return (
    <Button variant="outline" size="sm" asChild>
      <Link href={href}>
        <ArrowLeft className="mr-2 h-4 w-4" />
        {t("export.edit.back_to_declarations")}
      </Link>
    </Button>
  )
}
