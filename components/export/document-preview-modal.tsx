"use client"

import { useState } from "react"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Download,
  FileText,
  X,
  AlertCircle,
  File,
  FileSpreadsheet,
  Image as ImageIcon
} from "lucide-react"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"

interface DocumentPreviewModalProps {
  isOpen: boolean
  onClose: () => void
  filename: string
  declarationId: string
  fileType?: string
}

export function DocumentPreviewModal({
  isOpen,
  onClose,
  filename,
  declarationId,
  fileType
}: DocumentPreviewModalProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useSafeToast()
  const { t } = useI18n()

  // ✅ PROFESSIONAL ERP: Determine file type and icon
  const getFileInfo = (filename: string) => {
    const extension = filename.toLowerCase().split('.').pop() || ''

    switch (extension) {
      case 'pdf':
        return { icon: FileText, type: 'PDF Document', color: 'text-red-500' }
      case 'doc':
      case 'docx':
        return { icon: FileText, type: 'Word Document', color: 'text-blue-500' }
      case 'xls':
      case 'xlsx':
        return { icon: FileSpreadsheet, type: 'Excel Spreadsheet', color: 'text-green-500' }
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'webp':
        return { icon: ImageIcon, type: 'Image', color: 'text-purple-500' }
      case 'txt':
        return { icon: FileText, type: 'Text Document', color: 'text-gray-500' }
      default:
        return { icon: File, type: 'Document', color: 'text-muted-foreground' }
    }
  }

  const fileInfo = getFileInfo(filename)
  const FileIcon = fileInfo.icon

  // ✅ PROFESSIONAL ERP: Download file handler
  const handleDownload = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/export/declarations/${declarationId}/files/${filename}`)

      if (!response.ok) {
        throw new Error('Download failed')
      }

      const blob = await response.blob()
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      toast({
        title: t("export.documents.download_success"),
        description: `${filename} ${t("export.documents.download_success_desc")}`,
      })
    } catch (error) {
      toast({
        title: t("export.documents.download_failed"),
        description: error instanceof Error ? error.message : t("export.documents.download_failed_desc"),
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // ✅ PROFESSIONAL ERP: Render preview content based on file type
  const renderPreviewContent = () => {
    const extension = filename.toLowerCase().split('.').pop() || ''

    if (extension === 'pdf') {
      return (
        <div className="w-full h-[600px] border rounded-lg overflow-hidden">
          <iframe
            src={`/api/export/declarations/${declarationId}/files/${filename}`}
            className="w-full h-full"
            title={`Preview of ${filename}`}
          />
        </div>
      )
    }

    if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension)) {
      return (
        <div className="flex justify-center items-center p-4">
          <img
            src={`/api/export/declarations/${declarationId}/files/${filename}`}
            alt={filename}
            className="max-w-full max-h-[600px] object-contain rounded-lg shadow-lg"
          />
        </div>
      )
    }

    if (extension === 'txt') {
      return (
        <div className="w-full h-[400px] border rounded-lg overflow-hidden">
          <iframe
            src={`/api/export/declarations/${declarationId}/files/${filename}`}
            className="w-full h-full"
            title={`Preview of ${filename}`}
          />
        </div>
      )
    }

    // ✅ FALLBACK: Non-previewable files
    return (
      <div className="flex flex-col items-center justify-center py-12 space-y-4">
        <FileIcon className="h-16 w-16 text-muted-foreground" />
        <div className="text-center">
          <h3 className="text-lg font-medium">{filename}</h3>
          <p className="text-muted-foreground">{fileInfo.type}</p>
        </div>
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            This file type cannot be previewed in the browser. Click the download button to view the file.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <FileIcon className={`h-5 w-5 ${fileInfo.color}`} />
              {filename}
            </DialogTitle>
          </div>
          <DialogDescription>
            Preview of {fileInfo.type.toLowerCase()} - {filename}
          </DialogDescription>
          <div className="flex items-center justify-end gap-2 mt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownload}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              {isLoading ? t("export.documents.downloading") : t("export.documents.download")}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>
        <div className="overflow-auto max-h-[calc(90vh-120px)]">
          {renderPreviewContent()}
        </div>
      </DialogContent>
    </Dialog>
  )
}
