/**
 * Manufacturing ERP - Declaration Edit Form Component
 * Comprehensive edit form matching create form functionality
 */

"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { FileText, Package, Ship, Upload, Plus, Trash2, Edit, Eye } from "lucide-react"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"
import { ContractSelector } from "@/components/export/contract-selector"
import { ProductInheritance } from "@/components/export/product-inheritance"
import { SearchableSelect } from "@/components/ui/searchable-select"

// ✅ CLIENT-SAFE: Simple UUID generator for client-side use
const generateId = () => Math.random().toString(36).substring(2) + Date.now().toString(36)

// ✅ COMPREHENSIVE: Types matching create form
interface SalesContract {
  id: string
  number: string
  status: string
  customer: { name: string }
  created_at: string
  items: Array<{
    id: string
    product: { id: string; name: string; hs_code?: string }
    qty: string
  }>
}

interface DeclarationItem {
  id: string
  product_id: string
  product?: { id: string; name: string; hs_code?: string }
  qty: string
  hs_code: string
  quality_status: string
}

interface Shipment {
  id: string
  shipment_number: string
  customer: { name: string }
  status: string
  created_at: string
  items: Array<{ product: { name: string } }>
}

interface DocumentUpload {
  id: string
  name: string
  type: string
  size: number
  file: File
}

interface DeclarationEditFormProps {
  declaration: any
}

export function DeclarationEditForm({ declaration }: DeclarationEditFormProps) {
  const router = useRouter()
  const { toast } = useSafeToast()
  const { t } = useI18n()

  // ✅ COMPREHENSIVE: State matching create form
  const [submitting, setSubmitting] = useState(false)
  const [declarationNumber, setDeclarationNumber] = useState(declaration.number || "")
  const [status, setStatus] = useState(declaration.status || "draft")

  // ✅ CONTRACT INTEGRATION: State
  const [selectedContract, setSelectedContract] = useState<SalesContract | null>(declaration.salesContract || null)

  // ✅ PRODUCT MANAGEMENT: State
  const [items, setItems] = useState<DeclarationItem[]>(
    declaration.items?.map((item: any) => ({
      id: item.id,
      product_id: item.product_id,
      product: item.product,
      qty: item.qty,
      hs_code: item.hs_code || "",
      quality_status: item.quality_status || "pending"
    })) || []
  )

  // ✅ SHIPMENT INTEGRATION: State
  const [selectedShipments, setSelectedShipments] = useState<string[]>(
    declaration.linkedShipments?.map((s: any) => s.id) || []
  )
  const [availableShipments, setAvailableShipments] = useState<Shipment[]>([])
  const [shipmentsLoading, setShipmentsLoading] = useState(false)

  // ✅ DOCUMENT MANAGEMENT: State
  const [documents, setDocuments] = useState<DocumentUpload[]>([])
  const [existingDocuments, setExistingDocuments] = useState(declaration.documents || [])

  // ✅ PROFESSIONAL: Status options following ERP standards
  const statusOptions = [
    { value: "draft", label: "Draft", color: "gray" },
    { value: "submitted", label: "Submitted", color: "yellow" },
    { value: "approved", label: "Approved", color: "blue" },
    { value: "cleared", label: "Cleared", color: "green" },
    { value: "rejected", label: "Rejected", color: "red" },
  ]

  const getStatusVariant = (status: string) => {
    switch (status) {
      case "draft": return "secondary"
      case "submitted": return "outline"
      case "approved": return "default"
      case "cleared": return "default"
      case "rejected": return "destructive"
      default: return "secondary"
    }
  }

  // ✅ PROFESSIONAL: Form submission with proper error handling
  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault()
    setLoading(true)

    try {
      const response = await fetch(`/api/export/declarations/${declaration.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to update declaration")
      }

      toast({
        title: "Success",
        description: "Declaration updated successfully",
      })

      router.push("/export")
      router.refresh()
    } catch (error) {
      console.error("Update error:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update declaration",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* ✅ PROFESSIONAL: Declaration Details Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Declaration Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="number">Declaration Number</Label>
                <Input
                  id="number"
                  value={formData.number}
                  onChange={(e) => setFormData({ ...formData, number: e.target.value })}
                  placeholder="Enter declaration number"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => setFormData({ ...formData, status: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          <Badge variant={getStatusVariant(option.value)}>
                            {option.label}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/export")}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                <Save className="mr-2 h-4 w-4" />
                {loading ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* ✅ PROFESSIONAL: Declaration Items Table */}
      <Card>
        <CardHeader>
          <CardTitle>Declaration Items</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>SKU</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>HS Code</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {declaration.items?.map((item: any) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{item.product?.name || "N/A"}</div>
                        {item.product?.description && (
                          <div className="text-sm text-muted-foreground">
                            {item.product.description}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="font-mono text-sm">
                      {item.product?.sku || "N/A"}
                    </TableCell>
                    <TableCell>
                      {item.qty} {item.product?.unit || "units"}
                    </TableCell>
                    <TableCell className="font-mono text-sm">
                      {item.hs_code || "N/A"}
                    </TableCell>
                  </TableRow>
                ))}
                {(!declaration.items || declaration.items.length === 0) && (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-8 text-muted-foreground">
                      No items found for this declaration
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
