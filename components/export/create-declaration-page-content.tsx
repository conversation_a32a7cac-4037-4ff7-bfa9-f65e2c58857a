/**
 * Manufacturing ERP - Create Export Declaration Page Content
 * Client component for localization support
 */

"use client"

import { 
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Globe, FileText } from "lucide-react"
import { useI18n } from "@/components/i18n-provider"
import { CreateDeclarationForm } from "@/components/export/create-declaration-form"

export function CreateDeclarationPageContent() {
  const { t } = useI18n()

  return (
    <div className="space-y-6">
      {/* Breadcrumb Navigation */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard" className="flex items-center gap-2">
              <Globe className="h-4 w-4" />
              {t("export.create.breadcrumb.dashboard")}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/export" className="flex items-center gap-2">
              <Globe className="h-4 w-4" />
              {t("export.create.breadcrumb.export")}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              {t("export.create.breadcrumb.new")}
            </BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Professional Header */}
      <div className="flex items-center gap-3">
        <div className="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center">
          <FileText className="h-5 w-5 text-blue-600" />
        </div>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t("export.create.title")}</h1>
          <p className="text-muted-foreground">{t("export.create.description")}</p>
        </div>
      </div>

      {/* Create Declaration Form */}
      <CreateDeclarationForm />
    </div>
  )
}
