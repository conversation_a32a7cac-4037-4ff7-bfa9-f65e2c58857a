/**
 * Manufacturing ERP - Comprehensive Export Declaration Create Form
 *
 * Professional multi-section form following ERP workflow patterns:
 * - Contract Integration (Optional)
 * - Product Management (Manual + Inherited)
 * - Shipment Integration
 * - Document Management
 * - Complete Data Entry with Validation
 */

"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { FileText, Package, Ship, Upload, Plus, Trash2, AlertCircle } from "lucide-react"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"
import { ContractSelector } from "@/components/export/contract-selector"
import { ProductInheritance } from "@/components/export/product-inheritance"
import { SearchableSelect } from "@/components/ui/searchable-select"

// ✅ CLIENT-SAFE: Simple UUID generator for client-side use
const generateId = () => Math.random().toString(36).substring(2) + Date.now().toString(36)

interface SalesContract {
  id: string
  number: string
  status: string
  customer: {
    name: string
  }
  created_at: string
  items: Array<{
    id: string
    product: {
      id: string
      name: string
      hs_code?: string
    }
    qty: string
  }>
}

interface DeclarationItem {
  id: string // Temporary UI ID
  product_id: string
  product?: {
    id: string
    name: string
    hs_code?: string
  }
  qty: string
  hs_code: string
  quality_status: string
}

// ✅ NEW COMPREHENSIVE TYPES
interface Shipment {
  id: string
  shipment_number: string
  status: string
  customer: {
    id: string
    name: string
  }
  ship_date?: string
  items: Array<{
    id: string
    product: {
      id: string
      name: string
    }
    quantity: string
  }>
}

interface DocumentUpload {
  id: string
  name: string
  type: string
  size: number
  file: File
}

export function CreateDeclarationForm() {
  const { t } = useI18n()
  const { toast } = useSafeToast()
  const router = useRouter()

  const [submitting, setSubmitting] = useState(false)

  // ✅ COMPREHENSIVE FORM STATE
  const [declarationNumber, setDeclarationNumber] = useState("")
  const [selectedContract, setSelectedContract] = useState<SalesContract | null>(null)
  const [items, setItems] = useState<DeclarationItem[]>([])

  // ✅ NEW: Shipment Integration State
  const [availableShipments, setAvailableShipments] = useState<Shipment[]>([])
  const [selectedShipments, setSelectedShipments] = useState<string[]>([])
  const [shipmentsLoading, setShipmentsLoading] = useState(false)

  // ✅ NEW: Document Management State
  const [documents, setDocuments] = useState<DocumentUpload[]>([])
  const [uploading, setUploading] = useState(false)

  // ✅ PROFESSIONAL: Auto-generate declaration number
  useEffect(() => {
    generateDeclarationNumber()
    loadAvailableShipments()
  }, [])

  // ✅ DEBUG: Log state changes
  useEffect(() => {
    console.log('🔍 Form State:', {
      declarationNumber,
      selectedContract: selectedContract?.number,
      itemsCount: items.length,
      shipmentsCount: availableShipments.length,
      selectedShipmentsCount: selectedShipments.length,
      documentsCount: documents.length
    })
  }, [declarationNumber, selectedContract, items, availableShipments, selectedShipments, documents])

  const generateDeclarationNumber = () => {
    const year = new Date().getFullYear()
    const timestamp = Date.now().toString().slice(-4)
    setDeclarationNumber(`EXP-${year}-${timestamp}`)
  }

  // ✅ PROFESSIONAL: Handle contract selection
  const handleContractSelect = (contract: SalesContract | null) => {
    setSelectedContract(contract)
    // Items will be auto-populated by ProductInheritance component
  }

  // ✅ PROFESSIONAL: Handle items change from ProductInheritance component
  const handleItemsChange = (newItems: DeclarationItem[]) => {
    setItems(newItems)
  }

  // ✅ NEW: Load available shipments for linking
  const loadAvailableShipments = async () => {
    try {
      setShipmentsLoading(true)
      console.log('🔍 ShipmentSelector: Loading shipments...')

      // ✅ LOAD ALL SHIPMENTS: Don't filter by status, let user choose any shipment
      const response = await fetch('/api/shipping/shipments')
      if (response.ok) {
        const data = await response.json()
        console.log('🔍 ShipmentSelector: API response:', data)

        // ✅ CORRECT FORMAT: Handle the API response structure
        const shipmentsArray = data.data || data.shipments || []
        console.log('🔍 ShipmentSelector: Shipments array:', shipmentsArray.length)

        setAvailableShipments(shipmentsArray)
      } else {
        console.error('🔍 ShipmentSelector: API error:', response.status, response.statusText)
      }
    } catch (error) {
      console.error('🔍 ShipmentSelector: Error loading shipments:', error)
    } finally {
      setShipmentsLoading(false)
    }
  }

  // ✅ NEW: Handle shipment selection (now handled inline in SearchableSelect)

  // ✅ NEW: Handle document upload
  const handleDocumentUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files) return

    const newDocuments: DocumentUpload[] = Array.from(files).map(file => ({
      id: generateId(),
      name: file.name,
      type: file.type,
      size: file.size,
      file
    }))

    setDocuments(prev => [...prev, ...newDocuments])
  }

  // ✅ NEW: Remove document
  const removeDocument = (documentId: string) => {
    setDocuments(prev => prev.filter(doc => doc.id !== documentId))
  }

  // ✅ PROFESSIONAL: Submit form with contract linking
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // ✅ VALIDATION
    if (!declarationNumber.trim()) {
      toast({
        title: t("common.validation_error"),
        description: t("export.validation.number_required"),
        variant: "destructive"
      })
      return
    }

    if (items.length === 0) {
      toast({
        title: t("common.validation_error"),
        description: t("export.validation.items_required"),
        variant: "destructive"
      })
      return
    }

    // ✅ VALIDATION: Check all items have required fields
    const invalidItems = items.filter(item => !item.product_id || !item.qty)
    if (invalidItems.length > 0) {
      toast({
        title: t("common.validation_error"),
        description: t("export.validation.items_invalid"),
        variant: "destructive"
      })
      return
    }

    setSubmitting(true)
    try {
      // ✅ STEP 1: Create the declaration
      const response = await fetch("/api/export/declarations", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          number: declarationNumber,
          // ✅ CONTRACT INTEGRATION: Include contract information if selected
          sales_contract_id: selectedContract?.id || null,
          contract_reference: selectedContract?.number || null,
          items: items.map(item => ({
            product_id: item.product_id,
            qty: item.qty,
            hs_code: item.hs_code,
            quality_status: item.quality_status
          }))
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to create declaration")
      }

      const result = await response.json()
      const declarationId = result.id
      console.log('🎯 Declaration created:', { declarationId, result })

      // ✅ STEP 2: Link selected shipments
      if (selectedShipments.length > 0) {
        for (const shipmentId of selectedShipments) {
          try {
            await fetch(`/api/export/declarations/${declarationId}/shipments`, {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ shipment_id: shipmentId })
            })
          } catch (error) {
            console.error(`Failed to link shipment ${shipmentId}:`, error)
          }
        }
      }

      // ✅ STEP 3: Upload documents
      if (documents.length > 0) {
        for (const doc of documents) {
          try {
            const formData = new FormData()
            formData.append('file', doc.file)
            formData.append('filename', doc.name)

            await fetch(`/api/export/declarations/${declarationId}/documents`, {
              method: "POST",
              body: formData
            })
          } catch (error) {
            console.error(`Failed to upload document ${doc.name}:`, error)
          }
        }
      }

      toast({
        title: t("common.success"),
        description: `${t("common.declaration")} ${declarationNumber} ${t("export.form.success")}`
      })

      // ✅ PROFESSIONAL: Navigate to the new declaration
      console.log('🎯 Redirecting to:', `/export/${declarationId}`)
      router.push(`/export/${declarationId}`)

    } catch (error) {
      console.error("Failed to create declaration:", error)
      toast({
        title: t("common.error"),
        description: error instanceof Error ? error.message : t("export.form.error"),
        variant: "destructive"
      })
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* ✅ SIMPLE FORM: Declaration Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {t("export.form.declaration_details")}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="declaration-number">{t("export.form.declaration_number")} *</Label>
              <Input
                id="declaration-number"
                value={declarationNumber}
                onChange={(e) => setDeclarationNumber(e.target.value)}
                placeholder="EXP-2025-001"
                required
              />
            </div>
            <div className="space-y-2">
              <Label>{t("export.form.status")}</Label>
              <div>
                <Badge variant="outline">{t("export.form.status_draft")}</Badge>
              </div>
            </div>
          </div>

          {/* ✅ SIMPLE INTEGRATION: Contract Selection */}
          <ContractSelector
            selectedContractId={selectedContract?.id}
            onContractSelect={handleContractSelect}
          />
        </CardContent>
      </Card>

      {/* ✅ COMPREHENSIVE: Product Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            {t("export.form.declaration_items")}
          </CardTitle>
          <CardDescription>
            Add products manually or inherit from selected sales contract
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ProductInheritance
            selectedContract={selectedContract}
            items={items}
            onItemsChange={handleItemsChange}
          />
        </CardContent>
      </Card>

      {/* ✅ NEW: Shipment Integration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Ship className="h-5 w-5" />
            Shipment Integration
          </CardTitle>
          <CardDescription>
            Link related shipments to this export declaration (optional)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Label htmlFor="shipment-select">Select Shipments to Link</Label>

            {shipmentsLoading ? (
              <div className="p-3 border rounded-md text-sm text-muted-foreground">
                Loading shipments...
              </div>
            ) : (
              <SearchableSelect
                options={[
                  {
                    value: 'none',
                    label: 'No shipment - Manual entry',
                    subtitle: 'Create declaration without linking shipments',
                    description: '📝 Manual declaration entry',
                  },
                  ...availableShipments.map((shipment) => ({
                    value: shipment.id,
                    label: shipment.shipment_number || `Shipment ${shipment.id.slice(-8)}`,
                    subtitle: `${shipment.customer?.name || 'Unknown Customer'} • ${new Date(shipment.created_at).toLocaleDateString()}`,
                    description: `Status: ${shipment.status} • ${shipment.items?.length || 0} items`,
                  }))
                ]}
                value={selectedShipments[0] || 'none'}
                onValueChange={(value) => {
                  if (value === 'none') {
                    setSelectedShipments([])
                  } else {
                    setSelectedShipments([value])
                  }
                }}
                placeholder="Select a shipment..."
                searchPlaceholder="Search shipments..."
                emptyMessage="No shipments found"
                allowClear={true}
              />
            )}

            {selectedShipments.length > 0 && (
              <div className="text-sm text-muted-foreground">
                Selected: <strong>{selectedShipments.length} shipment</strong>
              </div>
            )}

            {availableShipments.length === 0 && !shipmentsLoading && (
              <div className="text-center py-6 text-muted-foreground">
                <Ship className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No shipments available</p>
                <p className="text-xs">Create shipments first to link them to declarations</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* ✅ NEW: Document Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Export Documents
          </CardTitle>
          <CardDescription>
            Upload required documents for this export declaration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Upload Area */}
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <div className="space-y-2">
                <p className="text-sm font-medium">Upload Documents</p>
                <p className="text-xs text-muted-foreground">
                  Supported formats: PDF, DOC, DOCX, XLS, XLSX, TXT, JPG, PNG, GIF
                </p>
              </div>
              <input
                type="file"
                multiple
                accept=".pdf,.doc,.docx,.xls,.xlsx,.txt,.jpg,.jpeg,.png,.gif"
                onChange={handleDocumentUpload}
                className="hidden"
                id="document-upload"
                aria-label="Upload export documents"
              />
              <Button
                type="button"
                variant="outline"
                className="mt-4"
                onClick={() => document.getElementById('document-upload')?.click()}
              >
                <Plus className="mr-2 h-4 w-4" />
                Choose Files
              </Button>
            </div>

            {/* Uploaded Documents List */}
            {documents.length > 0 && (
              <div className="space-y-2">
                <Label>Uploaded Documents ({documents.length})</Label>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {documents.map((doc) => (
                    <div
                      key={doc.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <FileText className="h-4 w-4 text-blue-500" />
                        <div>
                          <div className="font-medium text-sm">{doc.name}</div>
                          <div className="text-xs text-muted-foreground">
                            {(doc.size / 1024 / 1024).toFixed(2)} MB • {doc.type}
                          </div>
                        </div>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeDocument(doc.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* ✅ PROFESSIONAL: Form Actions */}
      <div className="flex items-center justify-between">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.push('/export')}
        >
          {t("export.form.cancel")}
        </Button>

        <div className="flex items-center gap-4">
          {/* ✅ COMPREHENSIVE: Status Summary */}
          <div className="text-sm text-muted-foreground space-y-1">
            {selectedContract && (
              <div>Contract: <strong>{selectedContract.number}</strong></div>
            )}
            <div>Items: <strong>{items.length}</strong></div>
            {selectedShipments.length > 0 && (
              <div>Shipments: <strong>{selectedShipments.length}</strong></div>
            )}
            {documents.length > 0 && (
              <div>Documents: <strong>{documents.length}</strong></div>
            )}
          </div>

          <Separator orientation="vertical" className="h-12" />

          <Button
            type="submit"
            disabled={submitting || !declarationNumber.trim() || items.length === 0}
            className="min-w-[140px]"
          >
            {submitting ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                Creating...
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Create Declaration
              </div>
            )}
          </Button>
        </div>
      </div>
    </form>
  )
}
