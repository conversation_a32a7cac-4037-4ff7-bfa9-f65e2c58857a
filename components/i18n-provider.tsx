"use client"

import type React from "react"

import { createContext, useContext, useEffect, useMemo, useState } from "react"

export type Locale = "en" | "zh"

type Dict = Record<string, string>

const en: Dict = {
  // App + Nav
  "app.name": "FC-CHINA",
  "nav.group.overview": "Overview",
  "nav.group.master-data": "Master Data",
  "nav.group.sales-purchasing": "Sales & Purchasing", // ✅ LEGACY: Keep for backward compatibility
  "nav.group.sales-process": "Sales Process", // ✅ NEW: Optimized grouping
  "nav.group.production": "Production", // ✅ LEGACY: Keep for backward compatibility
  "nav.group.production-planning": "Production Planning", // ✅ NEW: Optimized grouping
  "nav.group.inventory-logistics": "Inventory & Logistics", // ✅ LEGACY: Keep for backward compatibility
  "nav.group.quality-inventory": "Quality & Inventory", // ✅ NEW: Optimized grouping
  "nav.group.shipping-export": "Shipping & Export", // ✅ NEW: Optimized grouping
  "nav.group.export-trade": "Export & Trade", // ✅ LEGACY: Keep for backward compatibility
  "nav.group.finance-reporting": "Finance & Reporting",
  "nav.group.settings": "Settings", // ✅ LEGACY: Keep for backward compatibility
  "nav.group.administration": "Administration", // ✅ NEW: Optimized grouping
  "nav.item.dashboard": "Dashboard",
  "nav.item.customers": "Customers",
  "nav.item.suppliers": "Suppliers",
  "nav.item.products": "Products",
  "nav.item.samples": "Samples",
  "nav.item.sales-contracts": "Sales Contracts",
  "nav.item.purchase-contracts": "Purchase Contracts",
  "nav.item.contract-templates": "Contract Templates",
  "nav.item.work-orders": "Work Orders",
  "nav.item.bom-management": "Bill of Materials",
  "nav.item.quality-control": "Quality Control",
  "nav.item.mrp-planning": "MRP Planning",
  "nav.item.inventory": "Inventory",
  "nav.item.raw-materials": "Raw Materials",
  "nav.item.locations": "Locations",
  "nav.item.shipping": "Shipping",
  "nav.item.export-declarations": "Export Declarations",
  "nav.item.trade-compliance": "Trade Compliance",
  "nav.item.documentation": "Documentation",
  "nav.item.accounting": "Accounting",
  "nav.item.financial-dashboard": "Financial Dashboard",
  "nav.item.accounts-receivable": "Accounts Receivable",
  "nav.item.accounts-payable": "Accounts Payable",
  "nav.item.reports": "Reports",
  "nav.item.company-profile": "Company Profile",

  // Command
  "cmd.placeholder": "Search or jump to...",

  // Dashboard
  "home.title": "Export Manufacturing ERP",
  "home.subtitle":
    "One place to manage master data, contracts, production, inventory, export declarations, and finance.",
  "home.quick.master": "Add Master Data",
  "home.quick.contract": "Create Contract",
  "home.quick.stock": "Record Stock",
  "home.quick.declaration": "New Declaration",
  "kpi.customers": "Customers",
  "kpi.products": "Products",
  "kpi.suppliers": "Suppliers",
  "kpi.contracts": "Contracts",
  "kpi.suppliers.desc": "Active suppliers",
  "kpi.contracts.desc": "Sales & purchase contracts",
  "kpi.onhand": "On-hand Stock (sum)",
  "kpi.openWos": "Open Work Orders",

  // Dashboard Quick Actions
  "dashboard.quick_actions.title": "Quick Actions",
  "dashboard.quick_actions.subtitle": "Common tasks to get you started",
  "dashboard.quick_actions.manage_customers": "Manage Customers",
  "dashboard.quick_actions.view_products": "View Products",
  "dashboard.quick_actions.create_contract": "Create Sales Contract",
  "dashboard.quick_actions.update_profile": "Update Company Profile",

  // Dashboard System Status
  "dashboard.system_status.title": "System Status",
  "dashboard.system_status.subtitle": "Your ERP system setup progress",
  "dashboard.system_status.company_profile": "Company Profile",
  "dashboard.system_status.customer_database": "Customer Database",
  "dashboard.system_status.product_catalog": "Product Catalog",
  "dashboard.system_status.first_contract": "First Sales Contract",
  "dashboard.system_status.inventory_setup": "Inventory Setup",
  "dashboard.system_status.complete": "Complete",
  "dashboard.system_status.active": "Active",
  "dashboard.system_status.ready": "Ready",
  "dashboard.system_status.pending": "Pending",

  // Dashboard Getting Started
  "dashboard.getting_started.title": "🚀 Getting Started",
  "dashboard.getting_started.subtitle": "Complete these steps to get the most out of your Manufacturing ERP system",
  "dashboard.getting_started.step1.title": "1. Company Setup",
  "dashboard.getting_started.step1.desc": "Your company profile is complete and ready for business.",
  "dashboard.getting_started.step2.title": "2. Create Your First Contract",
  "dashboard.getting_started.step2.desc": "Start generating revenue by creating your first sales contract.",
  "dashboard.getting_started.step2.action": "Create Contract",
  "dashboard.getting_started.step3.title": "3. Set Up Inventory",
  "dashboard.getting_started.step3.desc": "Track your raw materials and finished goods inventory.",
  "dashboard.getting_started.step3.action": "Setup Inventory",
  "sample.title": "Sample Data",
  "sample.desc": "This workspace includes realistic sample data to demonstrate relationships across modules.",
  "sample.reset": "Reset sample data",
  "sample.clear": "Clear sample data",
  "alert.db.title": "Database not configured",
  "alert.db.desc":
    "Set the DATABASE_URL (Neon Postgres). The app runs a safe, one-time auto-migration at first load and seeds a minimal dataset. You can also run scripts/sql/001_init.sql and 002_seed.sql manually.",
  "alert.prep.title": "Preparing your workspace",
  "alert.prep.desc": "The schema is initializing. Refresh in a few seconds.",

  // Common fields/actions
  "field.code": "Code",
  "field.name": "Name",
  "field.spec": "Specification",
  "field.moq": "MOQ",
  "field.inStock": "In Stock",
  "field.contact": "Contact",
  "field.incoterm": "Incoterm",
  "field.paymentTerm": "Payment Term",
  "field.address": "Address",
  "field.sku": "SKU",
  "field.unit": "Unit",
  "field.hsCode": "HS Code",
  "field.origin": "Origin",
  "field.packaging": "Packaging",
  "field.currency": "Currency",
  "field.number": "Number",
  "field.customer": "Customer",
  "field.supplier": "Supplier",
  "field.product": "Product",
  "field.qty": "Qty",
  "field.price": "Price",
  "field.total": "Total",
  "field.woNumber": "WO Number",
  "field.salesContract": "Sales Contract",
  "field.location": "Location",
  "field.note": "Note",
  "field.reference": "Reference",
  "field.declarationNo": "Declaration No.",
  "field.amount": "Amount",
  "field.received": "Received",
  "field.paid": "Paid",
  "field.invoiceNo": "Invoice No.",
  "table.actions": "Actions",
  "table.noData": "No data available.",
  "action.add": "Add",
  "action.addItem": "Add Item",
  "action.remove": "Remove",
  "action.delete": "Delete",
  "action.create": "Create",
  "action.createContract": "Create Contract",
  "action.createPO": "Create PO",
  "action.createWO": "Create WO",
  "action.addInbound": "Add Inbound",
  "action.addOutbound": "Add Outbound",
  "action.createDeclaration": "Create Declaration",
  "action.submit": "Submit",
  "action.createAR": "Create AR",
  "action.createAP": "Create AP",
  "cta.open": "Open",

  // Basic Info
  "basic.samples.title": "Sample Management Center",
  "basic.samples.desc": "Code, name, specs, MOQ, available from stock.",
  "basic.customers.title": "Customers",
  "basic.customers.desc": "Store customer profiles and trading terms.",
  "basic.suppliers.title": "Suppliers",
  "basic.suppliers.desc": "Approved suppliers and contacts.",
  "basic.products.title": "Products/SKUs",
  "basic.products.desc": "Units, HS codes, origin, packaging.",

  // Contracts
  "contracts.sales.title": "Sales Contracts",
  "contracts.sales.desc": "Create basic sales contracts from products and customers.",
  "contracts.sales.empty": "No sales contracts.",
  "contracts.purchase.title": "Purchase Contracts",
  "contracts.purchase.desc": "Issue POs against suppliers.",
  "contracts.purchase.empty": "No purchase contracts.",

  // Production
  "production.title": "Work Orders",
  "production.desc": "Track routing and operation progress.",
  "production.empty": "No work orders.",

  // Inventory
  "inventory.inbound.title": "Inbound (GRN)",
  "inventory.inbound.desc": "Receive goods into stock.",
  "inventory.outbound.title": "Outbound (GDN)",
  "inventory.outbound.desc": "Ship goods and reduce stock.",
  "inventory.stock.title": "Stock",
  "inventory.stock.desc": "Current lots and on-hand summary.",
  "inventory.stock.empty": "No stock lots.",
  "inventory.txns.title": "Stock Transactions",
  "inventory.txns.desc": "FIFO is applied when shipping.",
  "inventory.txns.empty": "No transactions.",

  // Export
  "export.title": "Export Declarations",
  "export.desc": "Validate HS codes and track submission.",
  "export.empty": "No declarations.",

  // Finance
  "finance.title": "Finance & Accounting",
  "finance.description": "Manage invoices, payments, and financial reporting",
  "finance.summary.totalAR": "Total Receivables",
  "finance.summary.outstandingAR": "Outstanding AR",
  "finance.summary.totalAP": "Total Payables",
  "finance.summary.netCashFlow": "Net Cash Flow",

  // Enhanced KPI Dashboard
  "finance.kpis.coreMetrics": "Core Financial Metrics",
  "finance.kpis.totalRevenue": "Total Revenue (YTD)",
  "finance.kpis.totalExpenses": "Total Expenses (YTD)",
  "finance.kpis.profitLoss": "Profit/Loss (YTD)",
  "finance.kpis.netCashFlow": "Net Cash Flow",
  "finance.kpis.overdueIntelligence": "Overdue Intelligence",
  "finance.kpis.overdueAR": "Overdue Receivables",
  "finance.kpis.overdueAP": "Overdue Payables",
  "finance.kpis.manufacturingIntelligence": "Manufacturing Intelligence",
  "finance.kpis.contractProfitability": "Contract Profitability",
  "finance.kpis.avgCollectionDays": "Avg Collection Days",
  "finance.kpis.manufacturingMargin": "Manufacturing Margin",
  "finance.kpis.activeContracts": "Active Contracts",

  // Additional Finance KPIs - Customer Payment Health
  "finance.kpis.customerPaymentHealth": "Customer Payment Health",
  "finance.kpis.paymentPerformance": "Payment Performance",
  "finance.kpis.cashFlowPerformance": "Cash Flow Performance",
  "finance.kpis.cashReceived": "Cash Received",
  "finance.kpis.mtd": "MTD",
  "finance.kpis.actualCashCollected": "Actual cash collected",
  "finance.kpis.pendingReceivables": "Pending Receivables",
  "finance.kpis.invoices": "invoices",
  "finance.kpis.allInvoicesPaid": "All invoices paid",
  "finance.kpis.collectionRate": "Collection Rate",
  "finance.kpis.cashRevenue": "Cash / Revenue",
  "finance.kpis.excellentCollectionEfficiency": "Excellent collection efficiency",
  "finance.kpis.overdueInvoices": "Overdue Invoices",
  "finance.kpis.overdue": "overdue",
  "finance.kpis.excellentNothingOverdue": "Excellent - nothing overdue",
  "finance.kpis.businessPerformance": "Business Performance",
  "finance.kpis.totalContracts": "Total Contracts",

  // Enhanced AR/AP
  "finance.ar.title": "Accounts Receivable",
  "finance.ar.description": "Track AR invoices and aging with contract integration",
  "finance.ar.invoiceNumber": "Invoice Number",
  "finance.ar.customer": "Customer",
  "finance.ar.salesContract": "Sales Contract",
  "finance.ar.amount": "Amount",
  "finance.ar.received": "Received",
  "finance.ar.currency": "Currency",
  "finance.ar.status": "Status",
  "finance.ar.invoiceDate": "Invoice Date",
  "finance.ar.dueDate": "Due Date",
  "finance.ar.paymentTerms": "Payment Terms",
  "finance.ar.aging": "Aging",
  "finance.ar.contract": "Contract",
  "finance.ar.createInvoice": "Create AR Invoice",
  "finance.ar.noInvoices": "No AR invoices found",

  // ✅ MISSING AR KEYS: Add missing translation keys used in AR pages
  "finance.ar.editInvoice": "Edit Invoice",
  "finance.ar.invoiceAmount": "Invoice Amount",
  "finance.ar.totalInvoiceValue": "Total invoice value",
  "finance.ar.amountReceived": "Amount Received",
  "finance.ar.paymentsReceived": "Payments received",
  "finance.ar.outstandingBalance": "Outstanding Balance",
  "finance.ar.outstandingBalanceDesc": "Amount still owed",
  "finance.ar.sinceInvoiceDate": "Since invoice date",
  "finance.ar.invoiceInformation": "Invoice Information",
  "finance.ar.notes": "Notes",
  "finance.ar.customerInformation": "Customer Information",
  "finance.ar.customerName": "Customer Name",
  "finance.ar.email": "Email",
  "finance.ar.phone": "Phone",
  "finance.ar.address": "Address",
  "finance.ar.viewCustomerDetails": "View Customer Details",
  "finance.ar.customerNotAvailable": "Customer information not available",
  "finance.ar.linkedSalesContract": "Linked Sales Contract",
  "finance.ar.contractDescription": "This invoice is linked to sales contract",
  "finance.ar.contractNumber": "Contract Number",
  "finance.ar.contractTitle": "Contract Title",
  "finance.ar.contractValue": "Contract Value",
  "finance.ar.contractItems": "Contract Items",
  "finance.ar.product": "Product",
  "finance.ar.sku": "SKU",
  "finance.ar.quantity": "Quantity",
  "finance.ar.unitPrice": "Unit Price",
  "finance.ar.lineTotal": "Line Total",
  "finance.ar.unknownProduct": "Unknown Product",

  // AR Form Placeholders and Messages
  "finance.ar.searchPlaceholder": "Search sales contracts...",
  "finance.ar.customerPlaceholder": "Select customer...",
  "finance.ar.contractSelected": "Contract Selected",
  "finance.ar.contractTotal": "Contract Total",

  // AR Edit Page - Navigation and Actions
  "finance.ar.backToInvoice": "Back to Invoice",
  "finance.ar.editTitle": "Edit AR Invoice",
  "finance.ar.editDescription": "Update invoice details and payment information",
  "finance.ar.cancel": "Cancel",
  "finance.ar.saveChanges": "Save Changes",

  // AR Edit Page - Form Sections
  "finance.ar.basicInvoiceDetails": "Basic invoice details",
  "finance.ar.financialInformation": "Financial Information",
  "finance.ar.invoiceAmountsAndCurrency": "Invoice amounts and currency",
  "finance.ar.customerAndContract": "Customer and Contract",
  "finance.ar.selectCustomerAndContract": "Select customer and contract",
  "finance.ar.selectedContract": "Selected Contract",
  "finance.ar.additionalInformation": "Additional Information",
  "finance.ar.optionalNotes": "Optional notes",

  "finance.ap.title": "Accounts Payable",
  "finance.ap.description": "Track AP invoices and payments with contract integration",
  "finance.ap.invoiceNumber": "Invoice Number",
  "finance.ap.supplier": "Supplier",
  "finance.ap.purchaseContract": "Purchase Contract",
  "finance.ap.amount": "Amount",
  "finance.ap.paid": "Paid",
  "finance.ap.currency": "Currency",
  "finance.ap.status": "Status",
  "finance.ap.invoiceDate": "Invoice Date",
  "finance.ap.dueDate": "Due Date",
  "finance.ap.paymentTerms": "Payment Terms",
  "finance.ap.aging": "Aging",
  "finance.ap.contract": "Contract",
  "finance.ap.createInvoice": "Create AP Invoice",
  "finance.ap.noInvoices": "No AP invoices found",

  // ✅ MISSING AP KEYS: Add missing translation keys used in AP pages
  "finance.ap.editInvoice": "Edit Invoice",
  "finance.ap.amountPaid": "Amount Paid",
  "finance.ap.invoiceAmount": "Invoice Amount",
  "finance.ap.invoiceInformation": "Invoice Information",
  "finance.ap.supplierInformation": "Supplier Information",
  "finance.ap.linkedPurchaseContract": "Linked Purchase Contract",
  "finance.ap.contractItems": "Contract Items",
  "finance.ap.product": "Product",
  "finance.ap.sku": "SKU",
  "finance.ap.quantity": "Quantity",
  "finance.ap.unitPrice": "Unit Price",
  "finance.ap.lineTotal": "Line Total",
  "finance.ap.outstandingBalance": "Outstanding Balance",
  "finance.ap.paymentStatus": "Payment Status",

  // AP Form Placeholders and Messages
  "finance.ap.searchPlaceholder": "Search purchase contracts...",
  "finance.ap.supplierPlaceholder": "Select supplier...",

  // AP View Page - Action Buttons and Labels
  "finance.ap.backToFinance": "Back to Finance",
  "finance.ap.generatePDF": "Generate PDF",
  "finance.ap.viewFullContract": "View Full Contract",
  "finance.ap.invoiceTitle": "AP Invoice",
  "finance.ap.invoiceDetailsSubtitle": "Accounts Payable Invoice Details",
  "finance.ap.balanceDue": "Balance Due",
  "finance.ap.outstandingBalanceDesc": "Outstanding balance",
  "finance.ap.aging": "Aging",
  "finance.ap.sinceInvoiceDate": "Since invoice date",
  "finance.ap.supplierName": "Supplier Name",
  "finance.ap.viewSupplierDetails": "View Supplier Details",

  // AP Edit Page - Navigation and Actions
  "finance.ap.backToInvoice": "Back to Invoice",
  "finance.ap.editTitle": "Edit AP Invoice",
  "finance.ap.editDescription": "Update invoice details and payment information",
  "finance.ap.cancel": "Cancel",
  "finance.ap.saveChanges": "Save Changes",

  // AP Edit Page - Form Sections
  "finance.ap.basicInvoiceDetails": "Basic invoice details",
  "finance.ap.financialInformation": "Financial Information",
  "finance.ap.invoiceAmountsAndCurrency": "Invoice amounts and currency",
  "finance.ap.supplierAndContract": "Supplier and Contract",
  "finance.ap.selectSupplierAndContract": "Select supplier and contract",
  "finance.ap.selectedContract": "Selected Contract",
  "finance.ap.additionalInformation": "Additional Information",
  "finance.ap.optionalNotes": "Optional notes",
  "finance.ap.enterNotes": "Enter additional notes or comments...",

  // Additional AP Labels
  "finance.ap.invoiceInformationTitle": "Invoice Information",

  // AP View Page - Additional Labels
  "finance.ap.totalInvoiceValue": "Total invoice value",
  "finance.ap.paymentsMade": "Payments made",
  "finance.ap.invoiceNumber": "Invoice Number",
  "finance.ap.status": "Status",
  "finance.ap.invoiceDate": "Invoice Date",
  "finance.ap.dueDate": "Due Date",
  "finance.ap.paymentTerms": "Payment Terms",
  "finance.ap.currency": "Currency",
  "finance.ap.email": "Email",
  "finance.ap.address": "Address",
  "finance.ap.linkedPurchaseContract": "Linked Purchase Contract",
  "finance.ap.contractDescription": "This invoice is linked to purchase contract",
  "finance.ap.contractNumber": "Contract Number",
  "finance.ap.contractTitle": "Contract Title",
  "finance.ap.contractValue": "Contract Value",

  // Manufacturing Payment Terms
  "finance.paymentTerms.tt": "TT (Telegraphic Transfer)",
  "finance.paymentTerms.dp": "DP (Documents against Payment)",
  "finance.paymentTerms.lc": "LC (Letter of Credit)",
  "finance.paymentTerms.deposit": "Deposit (Advance Payment)",
  "finance.paymentTerms.depositTT": "30% Deposit + 70% TT",
  "finance.paymentTerms.depositLC": "50% Deposit + 50% LC",

  // Manufacturing Invoice Statuses
  "finance.status.depositReceived": "Deposit Received",
  "finance.status.partialPaid": "Partial Paid",
  "finance.status.depositPaid": "Deposit Paid",
  "finance.status.paid": "Paid",

  // Additional AR Labels
  "finance.ar.invoiceInformationTitle": "Invoice Information",

  // AR View Page - Action Buttons
  "finance.ar.backToFinance": "Back to Finance",
  "finance.ar.generatePDF": "Generate PDF",
  "finance.ar.viewFullContract": "View Full Contract",
  "finance.ar.invoiceTitle": "AR Invoice",
  "finance.ar.title": "Accounts Receivable",
  "finance.ar.desc": "Track AR and aging.",
  "finance.ar.empty": "No AR invoices.",
  "finance.ap.title": "Accounts Payable",
  "finance.ap.desc": "Track AP and payments.",
  "finance.ap.empty": "No AP invoices.",

  // Docs
  "docs.plan.title": "Reference Mind Maps",
  "docs.plan.desc": "Original Chinese and translated English plan visuals used to guide implementation.",

  // Module cards
  "module.master.title": "Master Data",
  "module.master.desc": "Samples, customers, suppliers, products, and trading terms.",
  "module.contracts.title": "Contracts",
  "module.contracts.desc": "Create sales contracts and purchase orders from SKUs.",
  "module.production.title": "Production",
  "module.production.desc": "Generate work orders, track routing and QC.",
  "module.inventory.title": "Inventory",
  "module.inventory.desc": "Inbound/outbound, FIFO, and current lots.",
  "module.export.title": "Export",
  "module.export.desc": "Build declarations and validate HS codes.",
  "module.export.declarations": "Declarations",
  "module.finance.title": "Finance",
  "module.finance.desc": "Track receivables and payables with basic aging.",

  // Landing Page
  "landing.login": "Login",
  "landing.getStarted": "Get Started",
  "landing.learnMore": "Learn More",
  "landing.badge": "Trusted by 500+ Export Manufacturers",
  "landing.hero.title": "All-in-One ERP for Textile Manufacturing & Export",
  "landing.hero.subtitle": "Streamline your textile manufacturing operations from production to export. Manage customers, products, quality control, and international trade compliance in one platform.",
  "landing.features.noCredit": "No credit card required",
  "landing.features.freeTrial": "30-day free trial",
  "landing.features.quickSetup": "Setup in minutes",
  "landing.features.title": "Everything You Need for Export Manufacturing",
  "landing.features.subtitle": "From raw materials to international shipping, manage your entire manufacturing workflow",
  "landing.features.crm.title": "Customer & Supplier Management",
  "landing.features.crm.description": "Comprehensive CRM for managing international customers and suppliers with contact details, payment terms, and trade history.",
  "landing.features.inventory.title": "Product Catalog & Inventory",
  "landing.features.inventory.description": "Detailed product management with SKUs, specifications, quality standards, and real-time inventory tracking.",
  "landing.features.production.title": "Production Management",
  "landing.features.production.description": "Work order management, production scheduling, and real-time tracking from cutting to packaging.",
  "landing.features.quality.title": "Quality Control",
  "landing.features.quality.description": "Integrated quality inspections, defect tracking, and compliance management for export standards.",
  "landing.features.export.title": "Export Documentation",
  "landing.features.export.description": "Automated export declarations, shipping documents, and international trade compliance management.",
  "landing.features.analytics.title": "Analytics & Reporting",
  "landing.features.analytics.description": "Real-time dashboards, production analytics, and comprehensive reporting for business insights.",
  "landing.benefits.title": "Why Choose Our Manufacturing ERP?",
  "landing.benefits.subtitle": "Built specifically for export-oriented textile manufacturers",
  "landing.benefits.speed.title": "50% Faster Operations",
  "landing.benefits.speed.description": "Streamlined workflows reduce manual work and accelerate production cycles",
  "landing.benefits.compliance.title": "100% Compliance",
  "landing.benefits.compliance.description": "Built-in export compliance ensures all international trade requirements are met",
  "landing.benefits.global.title": "Global Ready",
  "landing.benefits.global.description": "Multi-currency, multi-language support for international business operations",
  "landing.cta.title": "Ready to Transform Your Manufacturing?",
  "landing.cta.subtitle": "Join hundreds of manufacturers who have streamlined their operations with our ERP solution",
  "landing.cta.button": "Start Your Free Trial Today",
  "landing.cta.features": "No credit card required • 30-day free trial • Setup in minutes",
  "landing.footer.copyright": "© 2024 FC-CHINA. Built for export manufacturers worldwide.",

  // Landing Page (Hero mock labels)
  "landing.hero.mock.last30days": "Last 30 days",
  "landing.hero.mock.onTimeShipments": "On-Time Shipments",


  // Dashboard Page
  "dashboard.loading": "Loading dashboard...",
  "dashboard.error.title": "Dashboard Error",
  "dashboard.error.description": "This may indicate you need to complete your company profile setup.",
  "dashboard.error.retry": "Retry",
  "dashboard.welcome": "Welcome back",
  "dashboard.subtitle": "Here's what's happening with your manufacturing operations today.",
  "dashboard.stats.customers.title": "Total Customers",
  "dashboard.stats.customers.description": "Active customer accounts",
  "dashboard.stats.products.title": "Products",
  "dashboard.stats.products.description": "Products in catalog",
  "dashboard.stats.suppliers.title": "Suppliers",
  "dashboard.stats.suppliers.description": "Active suppliers",
  "dashboard.stats.contracts.title": "Active Contracts",
  "dashboard.stats.contracts.description": "Sales and purchase contracts",

  // Common UI Elements
  "common.loading": "Loading...",
  "common.error": "Error",
  "common.success": "Success",
  "common.cancel": "Cancel",
  "common.save": "Save",
  "common.delete": "Delete",
  "common.edit": "Edit",
  "common.view": "View",
  "common.add": "Add",
  "common.create": "Create",
  "common.update": "Update",
  "common.search": "Search",
  "common.filter": "Filter",
  "common.actions": "Actions",
  "common.status": "Status",
  "common.name": "Name",
  "common.backTo": "Back to",
  "common.description": "Description",
  "common.date": "Date",
  "common.created": "Created",
  "common.updated": "Updated",
  "common.active": "Active",
  "common.inactive": "Inactive",
  "common.yes": "Yes",
  "common.no": "No",
  "common.confirm": "Confirm",
  "common.close": "Close",
  "common.retry": "Retry",
  "common.optional": "Optional",
  "common.of": "of",
  "common.na": "N/A",

  // ✅ DASHBOARD TRANSLATIONS: Missing keys for Dashboard page
  "common.welcome_back": "Welcome back",
  "common.ready_to_manage_textile_export_operations": "Ready to manage your textile export operations",
  "common.company": "Company",
  "common.financial_performance": "Financial Performance",
  "common.total_revenue": "Total Revenue",
  "common.export_sales_revenue": "Export sales revenue",
  "common.profit_margin": "Profit Margin",
  "common.manufacturing_margin": "Manufacturing margin",
  "common.pending_receivables": "Pending Receivables",
  "common.outstanding_invoices": "Outstanding invoices",
  "common.active_customers": "Active Customers",
  "common.business_relationships": "Business relationships",
  "common.production_operations": "Production Operations",
  "common.active_work_orders": "Active Work Orders",
  "common.in_production": "In production",
  "common.production_efficiency": "Production Efficiency",
  "common.completion_rate": "Completion rate",
  "common.active_shipments": "Active Shipments",
  "common.in_transit": "In transit",
  "common.ontime_delivery": "On-Time Delivery",
  "common.delivery_performance": "Delivery performance",
  "common.quality_control": "Quality Control",
  "common.manufacturing_quality_metrics": "Manufacturing quality metrics",
  "common.quality_pass_rate": "Quality Pass Rate",
  "common.pending_inspections": "Pending Inspections",
  "common.quality_score": "Quality Score",
  "common.export_operations": "Export Operations",
  "common.international_trade_metrics": "International trade metrics",
  "common.export_declarations": "Export Declarations",
  "common.active_sales_contracts": "Active Sales Contracts",
  "common.product_catalog": "Product Catalog",
  "common.quick_access": "Quick Access",
  "common.navigate_to_key_manufacturing_modules": "Navigate to key manufacturing modules",
  "common.reports": "Reports",
  "common.production": "Production",
  "common.quality": "Quality",
  "common.shipping": "Shipping",

  // Status Values
  "status.active": "Active",
  "status.inactive": "Inactive",
  "status.draft": "Draft",
  "status.approved": "Approved",
  "status.pending": "Pending",
  "status.completed": "Completed",
  "status.cancelled": "Cancelled",
  "status.done": "Done",

  // Table Headers
  "header.wo": "WO",
  "header.operations": "Operations",
  "header.type": "Type",
  "header.time": "Time",
  "header.lot": "Lot",

  // Loading States
  "loading.inventory": "Loading inventory...",
  "loading.try_again": "Try again",

  // Form Fields
  "field.name": "Name",
  "field.email": "Email",
  "field.phone": "Phone",
  "field.address": "Address",
  "field.company": "Company",
  "field.contact": "Contact",
  "field.qty": "Quantity",
  "field.price": "Price",
  "field.total": "Total",
  "field.currency": "Currency",
  "field.status": "Status",
  "field.type": "Type",
  "field.date": "Date",
  "field.notes": "Notes",
  "field.product": "Product",
  "field.customer": "Customer",
  "field.supplier": "Supplier",
  "field.contract": "Contract",
  "field.template": "Template",

  // Customers Page
  "customers.title": "Customers",
  "customers.subtitle": "Manage your customer database and relationships",
  "customers.add": "Add Customer",
  "customers.add.title": "Add New Customer",
  "customers.add.description": "Create a new customer record for your business.",
  "customers.edit.title": "Edit Customer",
  "customers.edit.description": "Update customer information.",
  "customers.delete.title": "Delete Customer",
  "customers.delete.description": "Are you sure you want to delete this customer? This action cannot be undone.",
  "customers.form.company_name": "Company Name",
  "customers.form.contact_name": "Contact Person",
  "customers.form.contact_phone": "Phone Number",
  "customers.form.contact_email": "Email Address",
  "customers.form.address": "Address",
  "customers.form.tax_id": "Tax ID",
  "customers.form.bank": "Bank Details",
  "customers.form.incoterm": "Incoterm",
  "customers.form.payment_term": "Payment Terms",
  "customers.form.status": "Status",
  "customers.table.company_name": "Company Name",
  "customers.table.contact_person": "Contact Person",
  "customers.table.phone": "Phone",
  "customers.table.email": "Email",
  "customers.table.address": "Address",
  "customers.table.incoterm": "Incoterm",
  "customers.table.payment_terms": "Payment Terms",
  "customers.table.status": "Status",
  "customers.table.actions": "Actions",
  "customers.success.created": "Customer created successfully!",
  "customers.success.updated": "Customer updated successfully!",
  "customers.success.deleted": "Customer deleted successfully!",
  "customers.error.create": "Failed to create customer",
  "customers.error.update": "Failed to update customer",
  "customers.error.delete": "Failed to delete customer",
  "customers.empty": "No customers found",
  "customers.empty.description": "Get started by adding your first customer.",

  // ✅ CUSTOMER VIEW & EDIT TRANSLATIONS: Missing keys for view and edit pages
  "customers.view.back_to_customers": "Back to Customers",
  "customers.view.edit_customer": "Edit Customer",
  "customers.view.contact_information": "Contact Information",
  "customers.view.details": "Details",
  "customers.view.address": "Address",
  "customers.view.incoterm": "Incoterm",
  "customers.view.payment_term": "Payment Term",
  "customers.view.status": "Status",
  "customers.view.related_samples": "Related Samples",
  "customers.view.related_samples_description": "Samples related to this customer (both inbound and outbound)",
  "customers.view.search_samples": "Search samples by name, code, direction, status...",
  "customers.view.no_samples_found": "No samples found",
  "customers.view.no_samples_found_description": "No samples match your search criteria. Try adjusting your search terms.",
  "customers.view.no_related_samples": "No Related Samples",
  "customers.view.no_related_samples_description": "No samples have been associated with this customer yet.",
  "customers.view.sales_contracts": "Sales Contracts",
  "customers.view.sales_contracts_description": "Sales contracts for this customer.",
  "customers.view.contract": "Contract",
  "customers.view.date": "Date",
  "customers.view.items": "Items",
  "customers.view.actions": "Actions",
  "customers.view.no_sales_contracts": "No Sales Contracts",
  "customers.view.no_sales_contracts_description": "No sales contracts have been created for this customer yet.",
  "customers.view.view_contract": "View Contract",
  "customers.view.view": "View",
  "customers.view.search_contracts": "Search contracts by number, status, date...",
  "customers.view.no_contracts_found": "No contracts found",
  "customers.view.no_contracts_found_description": "No contracts match your search criteria. Try adjusting your search terms.",
  "customers.edit.page_title": "Edit Customer",
  "customers.edit.page_description": "Update the details of {customerName}.",
  "customers.form.customer_name": "Customer Name",
  "customers.form.contact_name": "Contact Name",
  "customers.form.contact_email": "Contact Email",
  "customers.form.contact_phone": "Contact Phone",
  "customers.form.address": "Address",
  "customers.form.incoterm": "Incoterm",
  "customers.form.payment_term": "Payment Term",
  "customers.success.updated_toast": "Customer updated successfully.",
  "customers.error.update_toast": "Failed to update customer.",

  // Products Page
  "products.title": "Products",
  "products.subtitle": "Manage your product catalog and inventory",
  "products.add": "Add Product",
  "products.add.title": "Add New Product",
  "products.add.description": "Create a new product record with specifications and pricing information.",
  "products.edit.title": "Edit Product",
  "products.edit.description": "Update product information and specifications.",
  "products.delete.title": "Delete Product",
  "products.delete.description": "Are you sure you want to delete this product? This action cannot be undone.",
  "products.form.name": "Product Name",
  "products.form.sku": "SKU",
  "products.form.unit": "Unit",
  "products.form.hs_code": "HS Code",
  "products.form.origin": "Origin",
  "products.form.package": "Package",
  "products.form.status": "Status",

  // ✅ PRICING FIELDS: Enhanced product pricing system
  "products.form.pricing_information": "Pricing Information",
  "products.form.base_price": "Base Price",
  "products.form.cost_price": "Cost Price",
  "products.form.margin_percentage": "Margin %",
  "products.form.currency": "Currency",
  "products.table.name": "Product Name",
  "products.table.sku": "SKU",
  "products.table.unit": "Unit",
  "products.table.hs_code": "HS Code",
  "products.table.origin": "Origin",
  "products.table.package": "Package",
  "products.table.price": "Price",
  "products.table.status": "Status",
  "products.table.actions": "Actions",
  "products.success.created": "Product created successfully!",
  "products.success.updated": "Product updated successfully!",
  "products.success.deleted": "Product deleted successfully!",
  "products.error.create": "Failed to create product",
  "products.error.update": "Failed to update product",
  "products.error.delete": "Failed to delete product",

  // Product View Page
  "products.product": "Product",
  "products.details": "Details",
  "products.name": "Name",
  "products.sku": "SKU",
  "products.unit": "Unit",
  "products.pricing": "Pricing",
  "products.basePrice": "Base Price",
  "products.costPrice": "Cost Price",
  "products.margin": "Margin",
  "products.specifications": "Specifications",
  "products.hsCode": "HS Code",
  "products.origin": "Origin",
  "products.package": "Package",
  "products.billOfMaterials": "Bill of Materials",

  // BOM Management
  "bom.title": "Bill of Materials",
  "bom.description": "Materials required to produce",
  "bom.addMaterial": "Add Material",
  "bom.addMaterialDialog.title": "Add Material to BOM",
  "bom.addMaterialDialog.description": "Select a raw material and specify the quantity required to produce one unit of",
  "bom.editMaterialDialog.title": "Edit BOM Item",
  "bom.editMaterialDialog.description": "Update the material requirements for",
  "bom.table.material": "Material",
  "bom.table.sku": "SKU",
  "bom.table.qtyRequired": "Qty Required",
  "bom.table.unit": "Unit",
  "bom.table.wasteFactor": "Waste Factor",
  "bom.table.cost": "Cost",
  "bom.table.status": "Status",
  "bom.table.actions": "Actions",
  "bom.form.rawMaterial": "Raw Material",
  "bom.form.qtyRequired": "Quantity Required",
  "bom.form.unit": "Unit",
  "bom.form.wasteFactor": "Waste Factor",
  "bom.form.wasteFactorDescription": "Percentage of material waste (e.g., 0.05 = 5%)",
  "bom.empty": "No materials in BOM yet. Click \"Add Material\" to get started.",
  "bom.noSupplier": "No supplier",
  "bom.unknown": "Unknown",
  "bom.selectMaterial": "Select raw material",
  "bom.noMaterialsAvailable": "No raw materials available",
  "bom.adding": "Adding...",
  "bom.updating": "Updating...",
  "bom.updateMaterial": "Update Material",
  "products.empty": "No products found",
  "products.empty.description": "Get started by adding your first product.",
  "products.view.title": "Product Details",
  "products.view.description": "View product information (read-only).",

  // Sales Contracts Page
  "sales_contracts.title": "Sales Contracts",
  "sales_contracts.subtitle": "Manage your company's sales contracts.",
  "sales_contracts.add": "Add Contract",
  "sales_contracts.add.title": "Create Sales Contract",
  "sales_contracts.add.description": "Create a new sales contract for your customer.",
  "sales_contracts.edit.title": "Edit Sales Contract",
  "sales_contracts.edit.description": "Update sales contract information.",
  "sales_contracts.delete.title": "Are you sure?",
  "sales_contracts.delete.description": "This will permanently delete the contract. This action cannot be undone.",
  "sales_contracts.form.number": "Contract Number",
  "sales_contracts.form.customer": "Customer",
  "sales_contracts.form.template": "Template",
  "sales_contracts.form.currency": "Currency",
  "sales_contracts.form.items": "Items",
  "sales_contracts.table.contract_number": "Contract Number",
  "sales_contracts.table.number": "Contract #",
  "sales_contracts.table.customer": "Customer",
  "sales_contracts.table.date": "Date",
  "sales_contracts.table.currency": "Currency",
  "sales_contracts.table.items": "Items",
  "sales_contracts.table.total": "Total",
  "sales_contracts.table.status": "Status",
  "sales_contracts.table.created": "Created",
  "sales_contracts.table.actions": "Actions",
  "sales_contracts.search_placeholder": "Search contracts...",
  "sales_contracts.success.created": "Sales contract created successfully!",
  "sales_contracts.success.updated": "Sales contract updated successfully!",
  "sales_contracts.success.deleted": "Contract deleted successfully.",
  "sales_contracts.error.create": "Failed to create sales contract",
  "sales_contracts.error.update": "Failed to update sales contract",
  "sales_contracts.error.delete": "Failed to delete contract.",
  "sales_contracts.empty": "No sales contracts found",
  "sales_contracts.empty.description": "Create your first sales contract to get started.",

  // Purchase Contracts Page
  "purchase_contracts.title": "Purchase Contracts",
  "purchase_contracts.subtitle": "Manage your company's purchase contracts.",
  "purchase_contracts.add": "Add Contract",
  "purchase_contracts.add.title": "Create Purchase Contract",
  "purchase_contracts.add.description": "Create a new purchase contract with your supplier.",
  "purchase_contracts.edit.title": "Edit Purchase Contract",
  "purchase_contracts.edit.description": "Update purchase contract information.",
  "purchase_contracts.delete.title": "Are you sure?",
  "purchase_contracts.delete.description": "This will permanently delete the contract. This action cannot be undone.",
  "purchase_contracts.form.number": "Contract Number",
  "purchase_contracts.form.supplier": "Supplier",
  "purchase_contracts.form.template": "Template",
  "purchase_contracts.form.currency": "Currency",
  "purchase_contracts.form.items": "Items",
  "purchase_contracts.table.contract_number": "Contract Number",
  "purchase_contracts.table.number": "Contract #",
  "purchase_contracts.table.supplier": "Supplier",
  "purchase_contracts.table.date": "Date",
  "purchase_contracts.table.currency": "Currency",
  "purchase_contracts.table.items": "Items",
  "purchase_contracts.table.total": "Total",
  "purchase_contracts.table.status": "Status",
  "purchase_contracts.table.created": "Created",
  "purchase_contracts.table.actions": "Actions",
  "purchase_contracts.search_placeholder": "Search contracts...",
  "purchase_contracts.success.created": "Purchase contract created successfully!",
  "purchase_contracts.success.updated": "Purchase contract updated successfully!",
  "purchase_contracts.success.deleted": "Contract deleted successfully.",
  "purchase_contracts.error.create": "Failed to create purchase contract",
  "purchase_contracts.error.update": "Failed to update purchase contract",
  "purchase_contracts.error.delete": "Failed to delete contract.",
  "purchase_contracts.empty": "No purchase contracts found",
  "purchase_contracts.empty.description": "Create your first purchase contract to get started.",

  // Suppliers Page
  "suppliers.title": "Suppliers",
  "suppliers.subtitle": "Manage your supplier database and relationships",
  "suppliers.add": "Add Supplier",
  "suppliers.add.title": "Add New Supplier",
  "suppliers.add.description": "Create a new supplier record with their contact information and details.",
  "suppliers.edit.title": "Edit Supplier",
  "suppliers.edit.description": "Update supplier information.",
  "suppliers.delete.title": "Delete Supplier",
  "suppliers.delete.description": "Are you sure you want to delete this supplier? This action cannot be undone.",
  "suppliers.delete.confirmation": "This will permanently delete the supplier \"{name}\" and remove their data from our servers.",
  "suppliers.form.name": "Supplier Name",
  "suppliers.form.contact_name": "Contact Person",
  "suppliers.form.contact_phone": "Phone Number",
  "suppliers.form.contact_email": "Email Address",
  "suppliers.form.address": "Address",
  "suppliers.form.bank": "Bank Details",
  "suppliers.form.tax_id": "Tax ID",
  "suppliers.form.status": "Status",
  "suppliers.table.company_name": "Company Name",
  "suppliers.table.name": "Supplier Name",
  "suppliers.table.contact_person": "Contact Person",
  "suppliers.table.phone": "Phone",
  "suppliers.table.email": "Email",
  "suppliers.table.address": "Address",
  "suppliers.table.bank": "Bank Details",
  "suppliers.table.status": "Status",
  "suppliers.table.actions": "Actions",
  "suppliers.success.created": "Supplier created successfully!",
  "suppliers.success.updated": "Supplier updated successfully!",
  "suppliers.success.deleted": "Supplier deleted successfully!",
  "suppliers.error.create": "Failed to create supplier",
  "suppliers.error.update": "Failed to update supplier",
  "suppliers.error.delete": "Failed to delete supplier",
  "suppliers.empty": "No suppliers found",
  "suppliers.empty.description": "Get started by adding your first supplier.",
  "suppliers.view.subtitle": "View supplier details and related purchase contracts",
  "suppliers.view.supplier_info": "Supplier Information",
  "suppliers.view.supplier_info_desc": "Basic supplier details and contact information",
  "suppliers.view.purchase_contracts": "Purchase Contracts",
  "suppliers.view.purchase_contracts_desc": "Related purchase contracts with this supplier",
  "suppliers.view.no_contracts": "No Purchase Contracts",
  "suppliers.view.no_contracts_desc": "This supplier doesn't have any purchase contracts yet.",
  "suppliers.view.create_contract": "Create Purchase Contract",
  "suppliers.view.view_all_contracts": "View All Contracts",

  // Samples Page - Main
  "samples.title": "Sample Management",
  "samples.subtitle": "Manage product samples and approval workflow",
  "samples.add": "New Sample",
  "samples.refresh": "Refresh",
  "samples.loading": "Loading samples...",
  "samples.found": "{count} samples found",

  // Dashboard Cards
  "samples.cards.outbound.title": "📤 Outbound Samples",
  "samples.cards.outbound.description": "We send to customers",
  "samples.cards.inbound.title": "📥 Inbound Samples",
  "samples.cards.inbound.description": "From customers & suppliers",
  "samples.cards.internal.title": "🏭 Internal Samples",
  "samples.cards.internal.description": "R&D and testing",
  "samples.cards.quality.title": "🧪 Quality Pipeline",
  "samples.cards.quality.description": "Awaiting QC approval",

  // Table Headers
  "samples.table.sample": "Sample",
  "samples.table.direction": "Direction",
  "samples.table.purpose": "Purpose",
  "samples.table.relationship": "Relationship",
  "samples.table.product": "Product",
  "samples.table.status": "Status",
  "samples.table.priority": "Priority",
  "samples.table.created": "Created",
  "samples.table.actions": "Actions",
  "samples.table.empty": "No samples found. Create your first sample to get started.",

  // Actions
  "samples.actions.view": "View",
  "samples.actions.edit": "Edit",
  "samples.actions.delete": "Delete",
  "samples.actions.approve": "Approve",
  "samples.actions.reject": "Reject",

  // Sample Filters
  "samples.filters.search": "Search samples by name, code, or notes...",
  "samples.filters.status.all": "All Statuses",
  "samples.filters.status.pending": "Pending",
  "samples.filters.status.approved": "Approved",
  "samples.filters.status.rejected": "Rejected",
  "samples.filters.type.all": "All Types",
  "samples.filters.type.development": "Development",
  "samples.filters.type.production": "Production",
  "samples.filters.type.quality": "Quality",
  "samples.filters.type.prototype": "Prototype",
  "samples.filters.direction.all": "All Directions",
  "samples.filters.direction.outbound": "Outbound",
  "samples.filters.direction.inbound": "Inbound",
  "samples.filters.direction.internal": "Internal",
  "samples.filters.advanced": "Advanced",
  "samples.filters.clear": "Clear Filters",

  // Sample Delete
  "samples.delete.success.title": "Sample Deleted",
  "samples.delete.success.description": "Sample '{name}' has been successfully deleted",
  "samples.delete.error.title": "Delete Failed",
  "samples.delete.error.description": "Failed to delete sample. Please try again.",
  "samples.delete.dialog.title": "Delete Sample",
  "samples.delete.dialog.description": "Are you sure you want to delete this sample? This action cannot be undone.",
  "samples.delete.dialog.warning": "This action is permanent and cannot be undone.",
  "samples.delete.dialog.confirm": "Delete Sample",
  "samples.delete.dialog.deleting": "Deleting...",

  // Sample Fields
  "samples.fields.code": "Sample Code",
  "samples.fields.name": "Sample Name",
  "samples.fields.date": "Date",
  "samples.fields.status": "Status",
  "samples.fields.priority": "Priority",
  "samples.fields.type": "Sample Type",
  "samples.fields.direction": "Direction",
  "samples.fields.purpose": "Purpose",
  "samples.fields.customer": "Customer",
  "samples.fields.supplier": "Supplier",
  "samples.fields.product": "Product",
  "samples.fields.quantity": "Quantity",
  "samples.fields.unit": "Unit",
  "samples.fields.cost": "Cost",
  "samples.fields.currency": "Currency",
  "samples.fields.delivery_date": "Delivery Date",
  "samples.fields.specifications": "Technical Specifications",
  "samples.fields.quality_requirements": "Quality Requirements",
  "samples.fields.notes": "Notes",

  // Sample Create
  "samples.create.title": "Create New Sample",
  "samples.create.description": "Add a new sample to track customer requests and approvals",
  "samples.create.backToSamples": "Back to Samples",

  // Card 1: Sample Classification
  "samples.create.classification.title": "Sample Classification",
  "samples.create.classification.description": "Sample direction and basic information",
  "samples.create.sampleDirection": "Sample Direction",
  "samples.create.sampleDirectionRequired": "Sample Direction *",
  "samples.create.outbound": "📤 Outbound - We send to customer",
  "samples.create.inbound": "📥 Inbound - Customer/supplier sends to us",
  "samples.create.internal": "🏭 Internal - Internal testing/R&D",
  "samples.create.sampleCode": "Sample Code",
  "samples.create.sampleCodeRequired": "Sample Code *",
  "samples.create.sampleDate": "Sample Date",
  "samples.create.sampleDateRequired": "Sample Date *",
  "samples.create.receivedDate": "Received Date",
  "samples.create.receivedDateRequired": "Received Date *",
  "samples.create.sampleName": "Sample Name",
  "samples.create.sampleNameRequired": "Sample Name *",
  "samples.create.sampleType": "Sample Type",
  "samples.create.priority": "Priority",

  // Card 2: Product Information
  "samples.create.productInfo.title": "Product Information",
  "samples.create.productInfo.description": "Product details and specifications",
  "samples.create.product": "Product",
  "samples.create.productRequired": "Product *",
  "samples.create.quantity": "Quantity",
  "samples.create.unit": "Unit",
  "samples.create.technicalSpecs": "Technical Specifications",
  "samples.create.qualityRequirements": "Quality Requirements",

  // Card 3: Business Relations (Dynamic)
  "samples.create.customerDelivery": "Customer Delivery",
  "samples.create.sampleSource": "Sample Source",
  "samples.create.internalRelations": "Internal Relations",
  "samples.create.customerReceiving": "Customer receiving the sample",
  "samples.create.whoSentSample": "Who sent us this sample",
  "samples.create.internalDepartment": "Internal department/project",
  "samples.create.customerRequired": "Customer (Receiving Sample) *",
  "samples.create.samplePurpose": "Sample Purpose",
  "samples.create.deliveryInstructions": "Delivery Instructions",
  "samples.create.senderType": "Sample Sender Type",
  "samples.create.senderTypeRequired": "Sample Sender Type *",
  "samples.create.customerSender": "Customer (Sender)",
  "samples.create.customerSenderRequired": "Customer (Sender) *",
  "samples.create.supplierSender": "Supplier (Sender)",
  "samples.create.supplierSenderRequired": "Supplier (Sender) *",
  "samples.create.sampleCondition": "Sample Condition & Requirements",
  "samples.create.internalPurpose": "Internal Purpose",
  "samples.create.internalRequirements": "Internal Requirements",

  // Card 4: Commercial Details
  "samples.create.commercialDetails.title": "Commercial Details",
  "samples.create.commercialDetails.description": "Pricing and delivery information",
  "samples.create.deliveryDate": "Delivery Date",
  "samples.create.sampleCost": "Sample Cost",
  "samples.create.currency": "Currency",
  "samples.create.testingStatus": "Testing Status",
  "samples.create.testingResults": "Testing Results",
  "samples.create.quoteRequested": "Quote Requested",
  "samples.create.quoteProvided": "Quote Provided",
  "samples.create.targetCompletion": "Target Completion Date",

  // Form Actions
  "samples.create.cancel": "Cancel",
  "samples.create.save": "Create Sample",
  "samples.create.saving": "Creating...",

  // Dropdown Options
  "samples.create.options.development": "Development",
  "samples.create.options.production": "Production",
  "samples.create.options.quality": "Quality",
  "samples.create.options.prototype": "Prototype",
  "samples.create.options.low": "Low",
  "samples.create.options.normal": "Normal",
  "samples.create.options.high": "High",
  "samples.create.options.urgent": "Urgent",
  "samples.create.options.customer": "Customer",
  "samples.create.options.supplier": "Supplier",
  "samples.create.options.customerEvaluation": "Customer Evaluation",
  "samples.create.options.marketingDemo": "Marketing Demo",
  "samples.create.options.tradeShow": "Trade Show",
  "samples.create.options.salesPresentation": "Sales Presentation",
  "samples.create.options.manufacturingQuote": "Manufacturing Quote Request",
  "samples.create.options.materialTesting": "Material Testing",
  "samples.create.options.reverseEngineering": "Reverse Engineering",
  "samples.create.options.qualityComparison": "Quality Comparison",
  "samples.create.options.qualityControl": "Quality Control",
  "samples.create.options.rAndD": "R&D Testing",
  "samples.create.options.processImprovement": "Process Improvement",
  "samples.create.options.productDevelopment": "Product Development",
  "samples.create.options.notStarted": "Not Started",
  "samples.create.options.inProgress": "In Progress",
  "samples.create.options.completed": "Completed",
  "samples.create.options.failed": "Failed",

  // Placeholders
  "samples.create.placeholders.inboundCode": "e.g. IN-2024-001",
  "samples.create.placeholders.outboundCode": "e.g. OUT-2024-001",
  "samples.create.placeholders.customerFabric": "e.g. Customer Fabric Sample",
  "samples.create.placeholders.cottonFabric": "e.g. Cotton Fabric Sample",
  "samples.create.placeholders.quantity": "e.g. 100",
  "samples.create.placeholders.unit": "e.g. meters, pieces",
  "samples.create.placeholders.techSpecs": "Technical specifications...",
  "samples.create.placeholders.qualityStandards": "Quality standards...",
  "samples.create.placeholders.searchProducts": "Search products (required for outbound)...",
  "samples.create.placeholders.searchProductsOptional": "Search products (optional)...",
  "samples.create.placeholders.searchCustomers": "Search customers to send sample to...",
  "samples.create.placeholders.searchCustomerSender": "Search customer who sent sample...",
  "samples.create.placeholders.searchSupplierSender": "Search supplier who sent sample...",
  "samples.create.placeholders.whySending": "Why are we sending this sample?",
  "samples.create.placeholders.whySent": "Why did they send this sample?",
  "samples.create.placeholders.whoSent": "Who sent us this sample?",
  "samples.create.placeholders.internalPurpose": "Internal testing purpose",
  "samples.create.placeholders.deliveryInstructions": "Special delivery instructions, customer requirements...",
  "samples.create.placeholders.sampleCondition": "Sample condition when received, analysis requirements, customer specifications...",
  "samples.create.placeholders.internalRequirements": "Internal testing requirements, project details, department specifications...",
  "samples.create.placeholders.sampleCost": "e.g. 150.00",
  "samples.create.placeholders.testingResults": "Quality analysis, material properties, test results...",

  // Help Text
  "samples.create.help.outboundProduct": "📤 Outbound samples require product selection (sampling our own product)",
  "samples.create.help.inboundProduct": "📥 Product selection is optional (sample may not exist in our catalog yet)",

  // Success/Error Messages
  "samples.create.success.title": "Sample Created",
  "samples.create.success.description": "Sample has been created successfully",
  "samples.create.error.title": "Creation Failed",
  "samples.create.error.description": "Failed to create sample. Please try again.",

  // Sample View
  "samples.view.back": "Back to Samples",
  "samples.view.pending_request": "Pending Request",
  "samples.view.approved": "Approved",
  "samples.view.rejected": "Rejected",
  "samples.view.edit": "Edit",
  "samples.view.sample_info": "Sample Information",
  "samples.view.sample_type": "Sample Type",
  "samples.view.priority": "Priority",
  "samples.view.sample_date": "Sample Date",
  "samples.view.quantity": "Quantity",
  "samples.view.delivery_date": "Delivery Date",
  "samples.view.cost": "Cost",
  "samples.view.relationships": "Relationships",
  "samples.view.customer": "Customer",
  "samples.view.contact": "Contact",
  "samples.view.email": "Email",
  "samples.view.product": "Product",
  "samples.view.sku": "SKU",
  "samples.view.supplier": "Supplier",
  "samples.view.specifications": "Specifications & Notes",
  "samples.view.technical_specs": "Technical Specifications",
  "samples.view.quality_requirements": "Quality Requirements",
  "samples.view.notes": "Notes",
  "samples.view.approval_history": "Approval History",
  "samples.view.created": "Created",
  "samples.view.revised": "Revised",
  "samples.view.pending": "pending",
  "samples.view.revision_required": "revision_required",
  "samples.view.by_system": "by System",
  "samples.view.by_current_user": "by Current User",
  "samples.view.sample_created": "Sample created and submitted for approval",
  "samples.view.sample_processed": "Sample processed",
  "samples.view.metadata": "Metadata",
  "samples.view.created_date": "Created",
  "samples.view.approved_by": "Approved by",
  "samples.view.approved_date": "Approved Date",

  // Approval History Status Labels
  "samples.view.status.created": "Created",
  "samples.view.status.pending": "Pending",
  "samples.view.status.approved": "Approved",
  "samples.view.status.rejected": "Rejected",
  "samples.view.status.revised": "Revised",
  "samples.view.status.revision_required": "Revision Required",

  // Approval History Actions
  "samples.view.actions.sample_created": "Sample created and submitted for approval",
  "samples.view.actions.sample_processed": "Sample processed",
  "samples.view.actions.sample_approved": "Sample approved",
  "samples.view.actions.sample_rejected": "Sample rejected",
  "samples.view.actions.revision_requested": "Revision requested",

  // User References
  "samples.view.by_system_on": "by System on",
  "samples.view.by_current_user_on": "by Current User on",
  "samples.view.by_user_on": "by {user} on",

  // Sample Edit
  "samples.edit.title": "Edit Sample",
  "samples.edit.description": "Update sample information and specifications",
  "samples.edit.loading": "Loading sample data...",
  "samples.edit.success.title": "Sample Updated",
  "samples.edit.success.description": "Sample has been updated successfully",
  "samples.edit.error.title": "Update Failed",
  "samples.edit.error.description": "Failed to update sample. Please try again.",
  "samples.edit.error.load": "Failed to load sample data. Please try again.",
  "samples.edit.back": "Back to Sample",
  "samples.edit.cancel": "Cancel",
  "samples.edit.save": "Update Sample",
  "samples.edit.saving": "Updating...",
  "samples.edit.validation.name": "Sample name is required",
  "samples.edit.code.readonly": "Sample code cannot be changed",
  "samples.edit.basic_info": "Basic Information",
  "samples.edit.basic_info_desc": "Update the basic sample information",
  "samples.edit.sample_code": "Sample Code",
  "samples.edit.sample_name": "Sample Name",
  "samples.edit.sample_name_placeholder": "Enter sample name",
  "samples.edit.sample_type": "Sample Type",
  "samples.edit.priority": "Priority",
  "samples.edit.relationships": "Relationships",
  "samples.edit.relationships_desc": "Associate this sample with customers, products, and suppliers",
  "samples.edit.customer": "Customer",
  "samples.edit.customer_placeholder": "Search customers...",
  "samples.edit.product": "Product",
  "samples.edit.product_placeholder": "Search products...",
  "samples.edit.supplier": "Supplier",
  "samples.edit.supplier_placeholder": "Search suppliers...",
  "samples.edit.specifications": "Specifications & Details",
  "samples.edit.specifications_desc": "Add technical specifications and additional details",
  "samples.edit.quantity": "Quantity",
  "samples.edit.unit": "Unit",
  "samples.edit.cost": "Cost",
  "samples.edit.currency": "Currency",
  "samples.edit.delivery_date": "Delivery Date",
  "samples.edit.technical_specs": "Technical Specifications",
  "samples.edit.technical_specs_placeholder": "Enter technical specifications...",
  "samples.edit.quality_requirements": "Quality Requirements",
  "samples.edit.quality_requirements_placeholder": "Enter quality requirements...",
  "samples.edit.notes": "Notes",
  "samples.edit.notes_placeholder": "Enter additional notes...",

  // Search Dropdowns
  "samples.edit.search.no_results": "No results found",
  "samples.edit.search.add_new_customer": "Add new customer",
  "samples.edit.search.add_new_product": "Add new product",
  "samples.edit.search.add_new_supplier": "Add new supplier",
  "samples.edit.search.loading": "Loading...",
  "samples.edit.search.type_to_search": "Type to search...",

  // Inventory Page
  "inventory.title": "Inventory Management",
  "inventory.subtitle": "Manage stock levels, inbound and outbound operations",
  "inventory.tabs.inbound": "Inbound",
  "inventory.tabs.outbound": "Outbound",
  "inventory.tabs.stock": "Stock",
  "inventory.tabs.transactions": "Transactions",
  "inventory.inbound.form.product": "Product",
  "inventory.inbound.form.qty": "Quantity",
  "inventory.inbound.form.location": "Location",
  "inventory.inbound.form.ref": "Reference",
  "inventory.inbound.button": "Receive",
  "inventory.outbound.form.product": "Product",
  "inventory.outbound.form.qty": "Quantity",
  "inventory.outbound.form.location": "Location",
  "inventory.outbound.form.ref": "Reference",
  "inventory.outbound.button": "Ship",
  "inventory.stock.loading": "Loading inventory...",
  "inventory.stock.error": "Failed to load inventory data",
  "inventory.stock.retry": "Try again",
  "inventory.stock.table.lot": "Lot",
  "inventory.stock.table.location": "Location",

  // Additional inventory keys
  "inventory.inbound.title": "Inbound",
  "inventory.inbound.desc": "Receive inventory",
  "inventory.outbound.title": "Outbound",
  "inventory.outbound.desc": "Ship inventory",
  "inventory.stock.title": "Stock",
  "inventory.stock.desc": "Current inventory levels",
  "inventory.transactions.title": "Transactions",
  "inventory.transactions.desc": "Inventory movement history",
  "field.location": "Location",
  "field.note": "Note",
  "field.reference": "Reference",
  "action.addInbound": "Receive",
  "action.addOutbound": "Ship",

  // ✅ NEW: Comprehensive inventory transaction translations
  "inventory.transaction_forms": "Transaction Forms",
  "inventory.transaction_history": "Transaction History",
  "inventory.transaction_success": "Transaction Successful",
  "inventory.transaction_error": "Transaction Failed",
  "inventory.inbound": "Inbound",
  "inventory.outbound": "Outbound",
  "inventory.transfer": "Transfer",
  "inventory.adjustment": "Adjustment",
  "inventory.product": "Product",
  "inventory.quantity": "Quantity",
  "inventory.location": "Location",
  "inventory.source_location": "Source Location",
  "inventory.destination_location": "Destination Location",
  "inventory.adjustment_quantity": "Adjustment Quantity",
  "inventory.reason_code": "Reason Code",
  "inventory.notes": "Notes",
  "inventory.reference": "Reference",
  "inventory.status": "Status",
  "inventory.date": "Date",
  "inventory.type": "Type",
  "inventory.select_product": "Select Product",
  "inventory.select_location": "Select Location",
  "inventory.reference_placeholder": "PO/SO number, receipt number, etc.",
  "inventory.notes_placeholder": "Additional notes or comments",
  "inventory.transfer_notes_placeholder": "Reason for transfer",
  "inventory.adjustment_notes_placeholder": "Explain the reason for adjustment",
  "inventory.positive_negative_allowed": "Positive or negative values allowed",
  "inventory.process_inbound": "Process Inbound",
  "inventory.process_outbound": "Process Outbound",
  "inventory.process_transfer": "Process Transfer",
  "inventory.process_adjustment": "Process Adjustment",
  "inventory.adjustment_warning": "Warning",
  "inventory.adjustment_warning_text": "Adjustments directly modify inventory quantities. Ensure proper authorization and documentation.",
  "inventory.search_transactions": "Search transactions...",
  "inventory.filter_by_type": "Filter by Type",
  "inventory.filter_by_location": "Filter by Location",
  "inventory.all_types": "All Types",
  "inventory.all_locations": "All Locations",
  "inventory.no_transactions": "No transactions found",
  "inventory.showing_transactions": "Showing {count} of {total} transactions",
  "inventory.fetch_error": "Failed to Load Data",
  "inventory.adjustment_notes": "Adjustment Notes",
  "inventory.reason_receipt": "Receipt",
  "inventory.reason_shipment": "Shipment",
  "inventory.reason_transfer": "Transfer",
  "inventory.reason_cycle_count": "Cycle Count",
  "inventory.reason_damage": "Damage",
  "inventory.reason_obsolete": "Obsolete",
  "inventory.reason_adjustment": "Adjustment",
  "inventory.reason_return": "Return",
  "inventory.reason_sample": "Sample",
  "inventory.status_pending": "Pending",
  "inventory.status_approved": "Approved",
  "inventory.status_rejected": "Rejected",

  // ✅ NEW: Enhanced Inventory KPI Cards
  "inventory.finishedGoods": "Finished Goods",
  "inventory.rawMaterials": "Raw Materials",
  "inventory.totalValue": "Total Value",

  // ✅ MISSING INVENTORY KEYS: Add missing keys used in inventory pages
  "inventory.management.title": "Inventory Management",
  "inventory.management.subtitle": "Manage stock levels, inbound and outbound operations",
  "inventory.tabs.finished_goods": "Finished Goods",
  "inventory.tabs.analytics": "Analytics",
  "inventory.tabs.discrepancy": "Discrepancy",
  "inventory.quick_actions.title": "Quick Actions",
  "inventory.quick_actions.subtitle": "Common inventory operations",
  "inventory.quick_actions.receive": "Receive",
  "inventory.quick_actions.ship": "Ship",
  "inventory.dialogs.quantity": "Quantity",
  "inventory.dialogs.location": "Location",

  // Company Profile
  "company.profile.title": "Company Profile",
  "company.profile.subtitle": "Manage your company information and settings",
  "company.profile.not_found": "No Company Profile Found",
  "company.profile.not_found_desc": "It looks like you haven't completed your company profile setup yet.",
  "company.profile.complete_setup": "Complete Company Setup",
  "company.profile.complete": "Complete",
  "company.profile.incomplete": "Incomplete",
  "company.profile.edit": "Edit Profile",
  "company.profile.save": "Save Changes",
  "company.profile.cancel": "Cancel",
  "company.profile.tabs.basic": "Basic Information",
  "company.profile.tabs.business": "Business Details",
  "company.profile.tabs.banking": "Banking",
  "company.profile.tabs.export": "Export & Trade",
  "company.profile.basic.description": "Your company's basic contact and address information",
  "company.profile.business.description": "Business registration and operational information",
  "company.profile.banking.description": "Banking and financial account details",
  "company.profile.export.description": "Export licensing and trade compliance information",
  "company.profile.success.updated": "Company profile updated successfully!",
  "company.profile.error.update": "Failed to update profile",

  // Company Profile Form Fields
  "company.field.name": "Company Name",
  "company.field.legal_name": "Legal Company Name",
  "company.field.email": "Email Address",
  "company.field.phone": "Phone Number",
  "company.field.website": "Website",
  "company.field.country": "Country",
  "company.field.address_line1": "Street Address",
  "company.field.address_line2": "Address Line 2",
  "company.field.city": "City",
  "company.field.state_province": "State/Province",
  "company.field.postal_code": "Postal Code",
  "company.field.industry": "Industry",
  "company.field.business_type": "Business Type",
  "company.field.employee_count": "Employee Count",
  "company.field.annual_revenue": "Annual Revenue",
  "company.field.registration_number": "Registration Number",
  "company.field.tax_id": "Tax ID",
  "company.field.vat_number": "VAT Number",
  "company.field.bank_name": "Bank Name",
  "company.field.bank_account": "Account Number",
  "company.field.bank_swift": "SWIFT/BIC Code",
  "company.field.bank_address": "Bank Address",
  "company.field.export_license": "Export License",
  "company.field.customs_code": "Customs Code",
  "company.field.preferred_incoterms": "Preferred Incoterms",
  "company.field.preferred_payment_terms": "Preferred Payment Terms",

  // Quality Control
  "quality.title": "Quality Control",
  "quality.subtitle": "Manage quality inspections, defect tracking, and compliance reports",
  "quality.standards": "Quality Standards",
  "quality.metrics.title": "Quality Metrics",
  "quality.metrics.pass_rate": "Pass Rate",
  "quality.metrics.total_inspections": "Total Inspections",
  "quality.metrics.pending": "Pending Inspections",
  "quality.metrics.defect_rate": "Defect Rate",
  "quality.inspections.title": "Recent Inspections",
  "quality.inspections.subtitle": "Latest quality inspection results",
  "quality.defects.title": "Defect Tracking",
  "quality.defects.subtitle": "Track and manage quality defects",

  // Contract Templates
  "contract_templates.title": "Contract Templates",
  "contract_templates.subtitle": "Manage reusable contract templates for sales and purchase agreements",
  "contract_templates.add": "Add Template",
  "contract_templates.table.name": "Template Name",
  "contract_templates.table.type": "Type",
  "contract_templates.table.language": "Language",
  "contract_templates.table.version": "Version",
  "contract_templates.table.status": "Status",
  "contract_templates.table.actions": "Actions",

  // Contract Forms (Add/Edit)
  "contracts.form.number": "Contract Number",
  "contracts.form.customer": "Customer",
  "contracts.form.supplier": "Supplier",
  "contracts.form.currency": "Currency",
  "contracts.form.template": "Template",
  "contracts.form.items": "Contract Items",
  "contracts.form.product": "Product",
  "contracts.form.quantity": "Quantity",
  "contracts.form.price": "Price",
  "contracts.form.total": "Total",
  "contracts.form.add_item": "Add Item",
  "contracts.form.remove_item": "Remove",
  "contracts.form.contract_info": "Contract Information",
  "contracts.form.contract_info_desc": "Basic contract details and customer information",
  "contracts.form.items_section": "Contract Items",
  "contracts.form.items_section_desc": "Products and quantities for this contract",
  "contracts.form.template_section": "Contract Template",
  "contracts.form.template_section_desc": "Choose a template for contract document generation",

  // Contract View
  "contracts.view.back": "Back to Contracts",
  "contracts.view.back_sales": "Back to Sales Contracts",
  "contracts.view.back_purchase": "Back to Purchase Contracts",
  "contracts.view.edit_contract": "Edit Contract",
  "contracts.view.export_pdf": "Export PDF",
  "contracts.view.loading": "Loading contract document...",
  "contracts.view.no_document": "No contract document available",
  "contracts.view.contract_summary": "Contract Summary",
  "contracts.view.contract_document": "Contract Document",
  "contracts.view.customer": "Customer",
  "contracts.view.supplier": "Supplier",
  "contracts.view.contract_date": "Contract Date",
  "contracts.view.total_value": "Total Value",
  "contracts.view.template": "Template",
  "contracts.view.no_email": "No email",
  "contracts.view.items_count": "{count} items",
  "contracts.view.sales_template": "sales template",
  "contracts.view.purchase_template": "purchase template",

  // Contract Edit
  "contracts.edit.title_sales": "Edit Sales Contract",
  "contracts.edit.title_purchase": "Edit Purchase Contract",
  "contracts.edit.subtitle": "Update the details of contract {number}",
  "contracts.edit.back": "Back to Contracts",
  "contracts.edit.template_optional": "Contract Template (Optional)",
  "contracts.edit.template_desc": "sales contract template",
  "contracts.edit.preview": "Preview",
  "contracts.edit.items_title": "Contract Items",
  "contracts.edit.add_item": "Add Item",
  "contracts.edit.product": "Product",
  "contracts.edit.quantity": "Quantity",
  "contracts.edit.unit_price": "Unit Price",
  "contracts.edit.sku_label": "SKU: {sku}",
  "contracts.edit.unit_label": "Unit: {unit}",

  // Contract Creation
  "contracts.create.title_sales": "Create Sales Contract",
  "contracts.create.title_purchase": "Create Purchase Contract",
  "contracts.create.subtitle_sales": "Enter the details for your new sales contract",
  "contracts.create.subtitle_purchase": "Enter the details for your new purchase contract",
  "contracts.create.back_sales": "Sales Contracts",
  "contracts.create.back_purchase": "Purchase Contracts",
  "contracts.create.add_new": "Add New Contract",
  "contracts.create.contract_info": "Contract Information",
  "contracts.create.contract_info_desc": "Basic contract details and customer information",
  "contracts.create.contract_info_desc_purchase": "Basic contract details and supplier information",
  "contracts.create.number": "Contract Number",
  "contracts.create.number_placeholder": "e.g., PC-2025-001",
  "contracts.create.supplier": "Supplier",
  "contracts.create.supplier_placeholder": "Select supplier...",
  "contracts.create.customer": "Customer",
  "contracts.create.customer_placeholder": "Select customer...",
  "contracts.create.currency": "Currency",
  "contracts.create.currency_placeholder": "e.g., USD",
  "contracts.create.template": "Contract Template (Optional)",
  "contracts.create.template_placeholder": "Select template...",
  "contracts.create.items": "Contract Items",
  "contracts.create.items_desc": "Add products and quantities for this contract",
  "contracts.create.add_item": "Add Item",
  "contracts.create.remove_item": "Remove Item",
  "contracts.create.product": "Product",
  "contracts.create.product_placeholder": "Select product...",
  "contracts.create.quantity": "Quantity",
  "contracts.create.quantity_placeholder": "0",
  "contracts.create.unit_price": "Unit Price",
  "contracts.create.unit_price_placeholder": "0.00",
  "contracts.create.total": "Total",
  "contracts.create.cancel": "Cancel",
  "contracts.create.create": "Create Contract",
  "contracts.create.creating": "Creating...",
  "contracts.create.success": "Contract created successfully!",
  "contracts.create.error": "Failed to create contract",
  "contracts.create.summary": "Contract Summary",
  "contracts.create.summary_desc": "Review the total contract value and details",
  "contracts.create.items_count": "Items:",
  "contracts.create.currency_label": "Currency:",
  "contracts.create.total_value": "Total Contract Value:",

  // Contract Templates
  "contracts.templates.page_title": "Contract Templates",
  "contracts.templates.page_desc": "Manage contract templates for sales and purchase agreements",
  "contracts.templates.sales_section": "Sales Contract Templates",
  "contracts.templates.sales_desc": "Create and manage sales contract templates",
  "contracts.templates.purchase_section": "Purchase Contract Templates",
  "contracts.templates.purchase_desc": "Create and manage purchase contract templates",
  "contracts.templates.template_name": "Template Name",
  "contracts.templates.currency": "Currency",
  "contracts.templates.payment_terms": "Payment Terms",
  "contracts.templates.delivery_terms": "Delivery Terms",
  "contracts.templates.template_content": "Template Content",
  "contracts.templates.content_placeholder": "Enter contract template content with placeholders like {{customer_name}}, {{product_name}}, etc.",
  "contracts.templates.content_placeholder_purchase": "Enter contract template content with placeholders like {{supplier_name}}, {{material_name}}, etc.",
  "contracts.templates.create_template": "Create Template",
  "contracts.templates.existing_templates": "Existing Templates",
  "contracts.templates.name": "Name",
  "contracts.templates.actions": "Actions",
  "contracts.templates.payment_30_days": "30 days",
  "contracts.templates.payment_60_days": "60 days",
  "contracts.templates.delivery_fob": "FOB",
  "contracts.templates.delivery_cif": "CIF",
  "contracts.templates.delivery_exw": "EXW",

  // Contract Templates Cards
  "contract_templates.sales.title": "Sales Contract Templates",
  "contract_templates.sales.description": "Create and manage templates for sales contracts",
  "contract_templates.sales.sample": "Sample Templates",
  "contract_templates.sales.sample_title": "Professional Sales Contract Template",
  "contract_templates.sales.sample_desc": "Copy this professional template and paste it into the Template Content field below.",
  "contract_templates.purchase.title": "Purchase Contract Templates",
  "contract_templates.purchase.description": "Create and manage templates for purchase contracts",
  "contract_templates.purchase.sample": "Sample Templates",
  "contract_templates.purchase.sample_title": "Professional Purchase Contract Template",
  "contract_templates.purchase.sample_desc": "Copy this professional template and paste it into the Template Content field below.",

  // Quality Control
  "quality.title": "Quality Control",
  "quality.subtitle": "Manage quality inspections and certificates",
  "quality.attachments.documents.title": "Document Attachments",
  "quality.attachments.documents.upload": "Upload Documents",
  "quality.attachments.documents.formats": "Supported formats: PDF, DOC, DOCX, XLS, XLSX, TXT",
  "quality.attachments.documents.none": "No documents attached",
  "quality.attachments.photos.title": "Photo Attachments",
  "quality.attachments.photos.upload": "Upload Photos",
  "quality.attachments.photos.formats": "Supported formats: JPG, PNG, GIF, WebP",
  "quality.attachments.photos.none": "No photos attached",
  "quality.attachments.preview": "Preview",
  "quality.attachments.download": "Download",
  "quality.attachments.remove": "Remove",
  "quality.attachments.uploading": "Uploading files...",
  "quality.attachments.upload_success": "Upload Successful",
  "quality.attachments.upload_success_desc": "file(s) uploaded successfully",
  "quality.attachments.download_success": "Download Complete",
  "quality.attachments.download_success_desc": "downloaded successfully",
  "quality.attachments.download_failed": "Download Failed",
  "quality.attachments.download_failed_desc": "Failed to download file. Please try again.",
  "quality.attachments.remove_success": "File Removed",
  "quality.attachments.remove_success_desc": "File removed successfully",
  "quality.attachments.remove_failed": "Remove Failed",
  "quality.attachments.remove_failed_desc": "Failed to remove file. Please try again.",

  // Quality Status
  "quality.status": "Quality Status",
  "quality.status.pending": "Pending Quality",
  "quality.status.approved": "Approved",
  "quality.status.quarantined": "Quarantined",
  "quality.status.rejected": "Rejected",

  // Work Orders
  "workOrders.title": "Work Orders",
  "workOrders.forProduct": "Work orders for this product",
  "workOrder.title": "Work Order",
  "workOrder.number": "Work Order Number",
  "workOrder.status.completed": "Completed",
  "workOrder.status.pending": "Pending",
  "workOrder.status.in-progress": "In Progress",

  // Product Quality Requirements
  "products.form.quality_requirements": "Quality Requirements",
  "products.form.inspection_required": "Quality Inspection Required",
  "products.form.inspection_required_desc": "Enable this if the product requires quality inspection before approval",
  "products.form.quality_tolerance": "Quality Tolerance",
  "products.form.quality_notes": "Quality Notes",
  "products.form.quality_notes_placeholder": "Enter specific quality requirements, standards, or inspection criteria...",
  "products.quality.not_required": "Not Required",
  "products.success.updated": "Product Updated",
  "products.success.updated_desc": "Product quality requirements have been updated successfully.",
  "products.success.created_desc": "Product with quality requirements has been created successfully.",
  "products.error.update": "Update Failed",

  // Work Orders Quality Gate Modal
  "work_orders.quality_gate.title": "Quality Approval Required",
  "work_orders.quality_gate.description": "This work order cannot be completed until all required quality inspections are approved.",
  "work_orders.quality_gate.work_order_info": "Work Order Information",
  "work_orders.quality_gate.inspections_status": "Quality Inspections Status",
  "work_orders.quality_gate.no_inspections": "No quality inspections found. Inspection may need to be created.",
  "work_orders.quality_gate.completion_status": "Completion Status",
  "work_orders.quality_gate.can_complete": "All quality requirements met. Work order can be completed.",
  "work_orders.quality_gate.cannot_complete": "Quality approval required before completion.",
  "work_orders.quality_gate.pending_inspections": "Pending Inspections",
  "work_orders.quality_gate.complete_inspections_first": "Please complete all pending quality inspections before proceeding.",
  "work_orders.quality_gate.go_to_quality_control": "Go to Quality Control",
  "work_orders.quality_gate.complete_work_order": "Complete Work Order",

  // Additional translations for quality gate modal
  "quality.inspector": "Inspector",
  "quality.inspection_types.final": "Final Inspection",
  "quality.inspection_types.incoming": "Incoming Inspection",
  "quality.inspection_types.in_process": "In-Process Inspection",
  "quality.status.pending": "Pending",
  "quality.status.passed": "Passed",
  "quality.status.failed": "Failed",
  "common.close": "Close",
  "common.processing": "Processing...",

  // ✅ MISSING QUALITY KEYS: Add missing keys used in quality pages
  "quality.quality_inspections": "Quality Inspections",
  "quality.analytics_dashboard": "Analytics Dashboard",
  "quality.loading_quality_inspections": "Loading quality inspections...",
  "quality.loading_quality_analytics": "Loading quality analytics...",
  "quality.loading_quality_inspection": "Loading quality inspection...",
  "quality.loading_quality_inspection_editor": "Loading quality inspection editor...",
  "quality.total_inspections": "Total Inspections",
  "quality.across_contracts": "Across {count} contracts",

  // ✅ MISSING SHIPPING KEYS: Add missing keys used in shipping pages
  "shipping.back_to_shipping": "Back to Shipping",
  "shipping.shipment_to": "shipment to",

  // ✅ RAW MATERIALS TRANSLATIONS: Complete raw materials page localization
  "raw_materials.title": "Raw Materials",
  "raw_materials.subtitle": "Manage your raw materials inventory and supplier relationships",
  "raw_materials.new_material": "New Material",
  "raw_materials.total_materials": "Total Materials",
  "raw_materials.active_materials": "Active Materials",
  "raw_materials.with_stock": "With Stock",
  "raw_materials.discontinued": "Discontinued",
  "raw_materials.search_placeholder": "Search materials by name, SKU, or description...",
  "raw_materials.filter_by_category": "Filter by Category",
  "raw_materials.filter_by_status": "Filter by Status",
  "raw_materials.filters.all_categories": "All Categories",
  "raw_materials.filters.all_status": "All Status",
  "raw_materials.filters.active": "Active",
  "raw_materials.filters.inactive": "Inactive",
  "raw_materials.filters.discontinued": "Discontinued",
  "raw_materials.filters.yarn": "Yarn",
  "raw_materials.filters.fabric": "Fabric",
  "raw_materials.filters.dyes": "Dyes",
  "raw_materials.filters.chemicals": "Chemicals",
  "raw_materials.filters.accessories": "Accessories",
  "raw_materials.filters.other": "Other",
  "raw_materials.more_filters": "More Filters",
  "raw_materials.inventory_title": "Raw Materials Inventory",
  "raw_materials.showing_results": "Showing {count} of {total} materials",
  "raw_materials.table.sku": "SKU",
  "raw_materials.table.material_name": "Material Name",
  "raw_materials.table.category": "Category",
  "raw_materials.table.primary_supplier": "Primary Supplier",
  "raw_materials.table.unit": "Unit",
  "raw_materials.table.standard_cost": "Standard Cost",
  "raw_materials.table.stock_status": "Stock Status",
  "raw_materials.table.status": "Status",
  "raw_materials.table.actions": "Actions",
  "raw_materials.no_materials_found": "No raw materials found. Create your first material to get started.",
  "raw_materials.no_materials_match_filters": "No materials match your current filters. Try adjusting your search or filters.",
  "raw_materials.no_supplier": "No supplier",
  "raw_materials.no_stock": "No Stock",
  "raw_materials.low_stock": "Low Stock",
  "raw_materials.in_stock": "In Stock",
  "raw_materials.raw_materials_storage": "Raw Materials Storage",
  "raw_materials.main_finished_goods_warehouse": "Main Finished Goods Warehouse",
  "raw_materials.note": "Raw materials are the foundation of your manufacturing process. Maintain accurate inventory levels and supplier relationships for optimal production planning.",

  // ✅ RAW MATERIALS VIEW PAGE TRANSLATIONS
  "raw_materials.view.back_to_materials": "Back to Materials",
  "raw_materials.view.add_lot": "Add Lot",
  "raw_materials.view.edit": "Edit",
  "raw_materials.view.total_lots": "Total Lots",
  "raw_materials.view.available_qty": "Available Qty",
  "raw_materials.view.total_value": "Total Value",
  "raw_materials.view.standard_cost": "Standard Cost",
  "raw_materials.view.material_information": "Material Information",
  "raw_materials.view.primary_supplier": "Primary Supplier",
  "raw_materials.view.composition": "Composition",
  "raw_materials.view.quality_grade": "Quality Grade",
  "raw_materials.view.lead_time": "Lead Time",
  "raw_materials.view.days": "days",
  "raw_materials.view.inventory_settings": "Inventory Settings",
  "raw_materials.view.reorder_point": "Reorder Point",
  "raw_materials.view.max_stock_level": "Max Stock Level",
  "raw_materials.view.inspection_required": "Inspection Required",
  "raw_materials.view.yes": "Yes",
  "raw_materials.view.no": "No",
  "raw_materials.view.specifications": "Specifications",
  "raw_materials.view.not_specified": "Not specified",
  "raw_materials.view.no_supplier_assigned": "No supplier assigned",
  "raw_materials.view.tabs.inventory_lots": "Inventory Lots",
  "raw_materials.view.tabs.bom_usage": "BOM Usage",
  "raw_materials.view.tabs.consumption_history": "Consumption History",
  "raw_materials.view.lots.lot_number": "Lot Number",
  "raw_materials.view.lots.supplier": "Supplier",
  "raw_materials.view.lots.quantity": "Quantity",
  "raw_materials.view.lots.unit_cost": "Unit Cost",
  "raw_materials.view.lots.received_date": "Received Date",
  "raw_materials.view.lots.quality_status": "Quality Status",
  "raw_materials.view.lots.status": "Status",
  "raw_materials.view.lots.actions": "Actions",
  "raw_materials.view.lots.no_lots_found": "No lots found for this material.",
  "raw_materials.view.lots.unknown_supplier": "Unknown Supplier",

  // ✅ RAW MATERIALS EDIT PAGE TRANSLATIONS
  "raw_materials.edit.back_to_material": "Back to Material",
  "raw_materials.edit.title": "Edit Raw Material",
  "raw_materials.edit.update_material": "Update {name} ({sku})",
  "raw_materials.edit.material_information": "Material Information",
  "raw_materials.edit.sku_label": "SKU",
  "raw_materials.edit.sku_placeholder": "Enter material SKU",
  "raw_materials.edit.name_label": "Material Name",
  "raw_materials.edit.name_placeholder": "Enter material name",
  "raw_materials.edit.category_label": "Category",
  "raw_materials.edit.unit_label": "Unit",
  "raw_materials.edit.unit_placeholder": "e.g., kg, meters, liters",
  "raw_materials.edit.supplier_label": "Primary Supplier",
  "raw_materials.edit.supplier_placeholder": "Select primary supplier",
  "raw_materials.edit.composition_label": "Composition",
  "raw_materials.edit.composition_placeholder": "e.g., 100% Cotton",
  "raw_materials.edit.quality_grade_label": "Quality Grade",
  "raw_materials.edit.quality_grade_placeholder": "e.g., Premium, Standard",
  "raw_materials.edit.specifications_label": "Specifications",
  "raw_materials.edit.specifications_placeholder": "Enter detailed specifications and requirements...",
  "raw_materials.edit.cost_inventory_settings": "Cost & Inventory Settings",
  "raw_materials.edit.standard_cost_label": "Standard Cost",
  "raw_materials.edit.reorder_point_label": "Reorder Point",
  "raw_materials.edit.lead_time_label": "Lead Time (Days)",
  "raw_materials.edit.status_quality_control": "Status & Quality Control",
  "raw_materials.edit.status_label": "Status",
  "raw_materials.edit.inspection_required_label": "Inspection Required",
  "raw_materials.edit.quality_notes_label": "Quality Notes",
  "raw_materials.edit.quality_notes_placeholder": "Enter quality control notes and requirements...",
  "raw_materials.edit.update_button": "Update Material",
  "raw_materials.edit.updating": "Updating...",
  "raw_materials.edit.cancel": "Cancel",
  "raw_materials.edit.note_title": "Note:",
  "raw_materials.edit.note": "Changes to raw material specifications may affect existing BOMs and production planning. Review all related products after making changes.",
  "raw_materials.edit.category_placeholder": "Select category",
  "raw_materials.edit.success_message": "Raw material updated successfully",
  "raw_materials.edit.error_message": "Failed to update raw material",

  // ✅ LOCATIONS PAGE TRANSLATIONS
  "locations.title": "Location Management",
  "locations.subtitle": "Manage warehouse locations, capacity, and utilization across your facilities",
  "locations.add_location": "Add Location",
  "locations.kpi.total_locations": "Total Locations",
  "locations.kpi.total_capacity": "Total Capacity",
  "locations.kpi.active_locations": "Active Locations",
  "locations.kpi.average_utilization": "Average Utilization",
  "locations.table.location_directory": "Location Directory",
  "locations.table.location_directory_desc": "Comprehensive overview of all facility locations with capacity and utilization metrics",
  "locations.table.name": "Name",
  "locations.table.type": "Type",
  "locations.table.capacity": "Capacity",
  "locations.table.utilization": "Utilization",
  "locations.table.details": "Details",
  "locations.table.status": "Status",
  "locations.table.actions": "Actions",
  "locations.table.security": "Security",
  "locations.table.available": "available",
  "locations.table.loading": "Loading...",
  "locations.table.no_data": "No data",
  "locations.status.active": "Active",
  "locations.status.inactive": "Inactive",

  // Location Types
  "locations.type.warehouse": "Warehouse",
  "locations.type.raw_materials": "Raw Materials",
  "locations.type.finished_goods": "Finished Goods",
  "locations.type.work_in_progress": "Work in Progress",
  "locations.type.quality_control": "Quality Control",
  "locations.type.shipping": "Shipping",
  "locations.type.receiving": "Receiving",
  "locations.type.quarantine": "Quarantine",
  "locations.type.returns": "Returns",

  // Security Levels
  "locations.security.low": "Low",
  "locations.security.medium": "Medium",
  "locations.security.high": "High",

  // Dialog Titles (Edit only - Add dialog removed)
  "locations.dialog.edit_title": "Edit Location",

  // Form Labels and Placeholders
  "locations.form.name_label": "Location Name",
  "locations.form.name_placeholder": "Enter location name",
  "locations.form.type_label": "Location Type",
  "locations.form.type_placeholder": "Select location type",
  "locations.form.description_label": "Description",
  "locations.form.description_placeholder": "Enter location description",
  "locations.form.capacity_label": "Capacity",
  "locations.form.capacity_placeholder": "Enter capacity",
  "locations.form.code_label": "Location Code",
  "locations.form.code_placeholder": "Enter location code",
  "locations.form.zone_label": "Zone",
  "locations.form.zone_placeholder": "Enter zone",
  "locations.form.security_level_label": "Security Level",
  "locations.form.security_level_placeholder": "Select security level",
  "locations.form.temperature_controlled_label": "Temperature Controlled",
  "locations.form.allows_mixed_products_label": "Allows Mixed Products",
  "locations.form.cancel_button": "Cancel",
  "locations.form.create_button": "Create Location",
  "locations.form.update_button": "Update Location",

  // Loading and Messages
  "locations.loading": "Loading locations...",
  "locations.create.title": "Create New Location",
  "locations.create.description": "Add a new location to your facility management system",
  "locations.create.location_name_id": "Location Name (ID)",
  "locations.create.location_code": "Location Code",
  "locations.create.description_brief": "Brief description of the location purpose",
  "locations.create.type": "Type",
  "locations.create.capacity": "Capacity",
  "locations.create.zone": "Zone",
  "locations.create.cancel": "Cancel",
  "locations.create.create_location": "Create Location",

  // Toast Messages
  "locations.toast.load_error": "Failed to load locations",
  "locations.toast.validation_error": "Please fill in all required fields",
  "locations.toast.create_success": "Location created successfully",
  "locations.toast.create_error": "Failed to create location",
  "locations.toast.update_success": "Location updated successfully",
  "locations.toast.update_error": "Failed to update location",
  "locations.toast.delete_success": "Location deleted successfully",
  "locations.toast.delete_error": "Failed to delete location",
  "locations.toast.delete_confirm": "Are you sure you want to delete this location?",

  // ✅ SHIPPING PAGE TRANSLATIONS
  "shipping.title": "Shipping Management",
  "shipping.subtitle": "Manage shipments, track deliveries, and optimize logistics operations",
  "shipping.new_shipment": "New Shipment",
  "shipping.search_placeholder": "Search shipments by number, customer, or tracking...",
  "shipping.filter.all_statuses": "All Statuses",
  "shipping.filter.status_placeholder": "Filter by status",

  // Overview Section
  "shipping.overview.title": "Shipping Overview",
  "shipping.overview.subtitle": "Real-time shipping metrics and performance indicators",
  "shipping.overview.last_updated": "Last updated",

  // Statistics Cards
  "shipping.stats.total": "Total",
  "shipping.stats.total_desc": "All shipments",
  "shipping.stats.preparing": "Preparing",
  "shipping.stats.preparing_desc": "Being prepared",
  "shipping.stats.shipped": "Shipped",
  "shipping.stats.shipped_desc": "In transit",
  "shipping.stats.delivered": "Delivered",
  "shipping.stats.delivered_desc": "Successfully delivered",
  "shipping.stats.success_rate": "Success Rate",
  "shipping.stats.success_rate_desc": "Delivery success rate",

  // Pipeline Section
  "shipping.pipeline.title": "Shipping Pipeline",
  "shipping.pipeline.show_details": "Show Details",
  "shipping.pipeline.hide_details": "Hide Details",
  "shipping.pipeline.ready": "Ready",
  "shipping.pipeline.transit": "Transit",
  "shipping.pipeline.active_progress": "Active Progress",
  "shipping.pipeline.overall_progress": "Overall Progress",
  "shipping.pipeline.delivered_progress": "delivered",
  "shipping.pipeline.status_breakdown": "Status Breakdown",
  "shipping.pipeline.active_rate": "Active Rate",
  "shipping.pipeline.active_rate_desc": "Currently active",
  "shipping.pipeline.ontime_rate": "On-time Rate",
  "shipping.pipeline.ontime_rate_desc": "Delivered on time",

  // Table Headers
  "shipping.table.shipment": "Shipment",
  "shipping.table.customer": "Customer",
  "shipping.table.method": "Method",
  "shipping.table.status": "Status",
  "shipping.table.items_qty": "Items/Qty",
  "shipping.table.financial_impact": "Financial Impact",
  "shipping.table.ship_date": "Ship Date",
  "shipping.table.tracking": "Tracking",
  "shipping.table.actions": "Actions",

  // Table Content
  "shipping.table.no_shipments": "No Shipments Found",
  "shipping.table.no_shipments_desc": "Get started by creating your first shipment",
  "shipping.table.create_shipment": "Create Shipment",
  "shipping.table.not_scheduled": "Not scheduled",
  "shipping.table.no_tracking": "No tracking",
  "shipping.table.items": "items",
  "shipping.table.units": "units",
  "shipping.table.eta": "ETA",

  // Actions Menu
  "shipping.actions.open_menu": "Open menu",
  "shipping.actions.view_details": "View Details",
  "shipping.actions.edit_shipment": "Edit Shipment",
  "shipping.actions.track_shipment": "Track Shipment",
  "shipping.actions.cancel_shipment": "Cancel Shipment",

  // Status Labels
  "shipping.status.preparing": "Preparing",
  "shipping.status.ready": "Ready",
  "shipping.status.shipped": "Shipped",
  "shipping.status.in_transit": "In Transit",
  "shipping.status.out_for_delivery": "Out for Delivery",
  "shipping.status.delivered": "Delivered",
  "shipping.status.cancelled": "Cancelled",
  "shipping.status.exception": "Exception",

  // Shipping Methods
  "shipping.method.air_freight": "Air Freight",
  "shipping.method.sea_freight": "Sea Freight",
  "shipping.method.express": "Express",
  "shipping.method.truck": "Truck",

  // Quick Actions
  "shipping.quick_actions.air_freight": "Air Freight",
  "shipping.quick_actions.air_freight_desc": "Fast international shipping for urgent deliveries",
  "shipping.quick_actions.create_air": "Create Air Shipment",
  "shipping.quick_actions.sea_freight": "Sea Freight",
  "shipping.quick_actions.sea_freight_desc": "Cost-effective ocean shipping for large volumes",
  "shipping.quick_actions.create_sea": "Create Sea Shipment",
  "shipping.quick_actions.express_delivery": "Express Delivery",
  "shipping.quick_actions.express_delivery_desc": "Same-day or next-day delivery service",
  "shipping.quick_actions.create_express": "Create Express Shipment",

  // ✅ SHIPPING VIEW PAGE TRANSLATIONS
  "shipping.view.customer_info": "Customer Information",
  "shipping.view.company": "Company",
  "shipping.view.contact_person": "Contact Person",
  "shipping.view.email": "Email",
  "shipping.view.phone": "Phone",
  "shipping.view.address": "Address",
  "shipping.view.shipment_items": "Shipment Items",
  "shipping.view.items_total_qty": "items • Total quantity:",
  "shipping.view.product": "Product",
  "shipping.view.sku": "SKU",
  "shipping.view.quantity": "Quantity",
  "shipping.view.unit_price": "Unit Price",
  "shipping.view.total": "Total",
  "shipping.view.unit": "Unit:",
  "shipping.view.no_items": "No items in this shipment",
  "shipping.view.total_shipment_value": "Total Shipment Value:",
  "shipping.view.additional_info": "Additional Information",
  "shipping.view.notes": "Notes",
  "shipping.view.special_instructions": "Special Instructions",
  "shipping.view.shipping_details": "Shipping Details",
  "shipping.view.shipment_number": "Shipment Number",
  "shipping.view.tracking_number": "Tracking Number",
  "shipping.view.carrier": "Carrier",
  "shipping.view.ship_date": "Ship Date",
  "shipping.view.estimated_delivery": "Estimated Delivery",
  "shipping.view.created": "Created",
  "shipping.view.last_updated": "Last Updated",
  "shipping.view.related_contract": "Related Contract",
  "shipping.view.contract_number": "Contract Number",
  "shipping.view.contract_status": "Contract Status",
  "shipping.view.view_contract": "View Contract",
  "shipping.view.cost_breakdown": "Cost Breakdown",
  "shipping.view.shipping_cost": "Shipping Cost",
  "shipping.view.insurance_cost": "Insurance Cost",
  "shipping.view.total_shipping_cost": "Total Shipping Cost",

  // ✅ SHIPPING EDIT PAGE TRANSLATIONS
  "shipping.edit.current_status": "Current Shipment Status",
  "shipping.edit.created": "Created",
  "shipping.edit.current_status_label": "Current Status:",
  "shipping.edit.related_contract": "Related Contract:",
  "shipping.edit.customer_info": "Customer Information",
  "shipping.edit.customer": "Customer",
  "shipping.edit.select_customer": "Select a customer",
  "shipping.edit.shipping_details": "Shipping Details",
  "shipping.edit.shipping_method": "Shipping Method",
  "shipping.edit.status": "Status",
  "shipping.edit.sea_freight": "Sea Freight",
  "shipping.edit.air_freight": "Air Freight",
  "shipping.edit.express": "Express",
  "shipping.edit.truck": "Truck",
  "shipping.edit.carrier": "Carrier",
  "shipping.edit.service_type": "Service Type",
  "shipping.edit.standard": "Standard",
  "shipping.edit.economy": "Economy",
  "shipping.edit.dates_tracking": "Dates & Tracking",
  "shipping.edit.ship_date": "Ship Date",
  "shipping.edit.estimated_delivery": "Estimated Delivery",
  "shipping.edit.tracking_number": "Tracking Number",
  "shipping.edit.costs": "Costs",
  "shipping.edit.shipping_cost": "Shipping Cost",
  "shipping.edit.insurance_cost": "Insurance Cost",
  "shipping.edit.additional_info": "Additional Information",
  "shipping.edit.notes": "Notes",
  "shipping.edit.special_instructions": "Special Instructions",
  "shipping.edit.cancel": "Cancel",
  "shipping.edit.update_shipment": "Update Shipment",
  "shipping.edit.updating": "Updating...",

  // Missing edit page keys
  "shipping.edit.title": "Edit Shipment",
  "shipping.edit.back_to_shipment": "Back to Shipment",
  "shipping.view.shipment": "Shipment",

  // Toast Messages
  "shipping.toast.updated": "Shipment Updated",
  "shipping.toast.updated_desc": "has been updated successfully.",
  "shipping.toast.error": "Error",
  "shipping.toast.update_failed": "Failed to update shipment",

  // ✅ EXPORT DECLARATION KEYS: Complete localization
  // Main page
  "export.main.title": "Export Declarations",
  "export.main.description": "Manage export trade documentation and customs declarations",
  "export.main.new_declaration": "New Declaration",

  // Statistics
  "export.stats.total_declarations": "Total Declarations",
  "export.stats.success_rate": "Success Rate",
  "export.stats.draft": "Draft",
  "export.stats.draft_desc": "Pending completion",
  "export.stats.submitted": "Submitted",
  "export.stats.submitted_desc": "Under review",
  "export.stats.approved": "Approved",
  "export.stats.approved_desc": "Ready for export",
  "export.stats.cleared": "Cleared",
  "export.stats.cleared_desc": "Successfully exported",
  "export.stats.attention_required": "Attention Required",
  "export.stats.rejected_declarations": "Rejected Declarations",
  "export.stats.review_issues": "Review Issues",
  "export.stats.clear_filter": "Clear Filter",

  // Table
  "export.table.title": "Export Declarations",
  "export.table.description": "declarations total",
  "export.table.search_placeholder": "Search declarations...",
  "export.table.loading": "Loading declarations...",
  "export.table.declaration_no": "Declaration No.",
  "export.table.status": "Status",
  "export.table.contract": "Contract",
  "export.table.items": "Items",
  "export.table.documents": "Documents",
  "export.table.created": "Created",
  "export.table.no_match": "No declarations match your search",
  "export.empty": "No export declarations found",

  // Delete dialog
  "export.delete.title": "Delete Declaration",
  "export.delete.description": "Are you sure you want to delete this export declaration? This action cannot be undone.",
  "export.delete.cancel": "Cancel",
  "export.delete.confirm": "Delete",
  "export.delete.success": "Declaration deleted successfully",
  "export.delete.error": "Failed to delete declaration",

  // View page
  "export.loading_declaration": "Loading declaration...",
  "export.back_to_declarations": "Back to Declarations",
  "export.declaration_details": "Declaration Details",
  "export.edit_declaration": "Edit Declaration",
  "export.declaration_information": "Declaration Information",
  "export.declaration_number": "Declaration Number",
  "export.status": "Status",
  "export.created_date": "Created Date",
  "export.last_updated": "Last Updated",
  "export.summary": "Summary",
  "export.total_items": "Total Items",
  "export.declaration_id": "Declaration ID",
  "export.declaration_items": "Declaration Items",
  "export.product": "Product",
  "export.sku": "SKU",
  "export.quantity": "Quantity",
  "export.hs_code": "HS Code",
  "export.linked_shipments": "Linked Shipments",
  "export.linked_shipments_description": "Shipments associated with this declaration",
  "export.shipment_number": "Shipment Number",
  "export.customer": "Customer",
  "export.ship_date": "Ship Date",
  "export.items": "Items",
  "export.no_shipments_linked": "No shipments linked to this declaration",

  // Documents
  "export.documents.title": "Export Documents",
  "export.documents.view_description": "View and download declaration documents",
  "export.documents.no_documents": "No documents uploaded for this declaration",
  "export.documents.download_success": "Download Complete",
  "export.documents.download_success_desc": "downloaded successfully",
  "export.documents.download_failed": "Download Failed",
  "export.documents.download_failed_desc": "Unable to download file",

  // Additional view page keys
  "export.no_items_found": "No items found for this declaration",
  "export.unknown_product": "Unknown Product",
  "export.unknown_customer": "Unknown Customer",
  "export.items_count": "items",
  "export.unknown_type": "Unknown type",
  "export.manual_entry": "Manual entry",
  "export.files": "files",

  // Edit page
  "export.edit.title": "Edit Declaration",
  "export.edit.back_to_declarations": "Back to Declarations",
  "export.edit.declaration_details": "Declaration Details",
  "export.edit.declaration_items": "Declaration Items",
  "export.edit.manage_products": "Manage products for this export declaration",
  "export.edit.cancel": "Cancel",
  "export.edit.save_changes": "Save Changes",
  "export.edit.contract_label": "Contract",
  "export.edit.items_label": "Items",
  "export.edit.shipments_label": "Shipments",
  "export.edit.documents_label": "Documents",

  // Products section
  "export.products.inherit_button": "Inherit from Contract",
  "export.products.add_manual": "Add Manual Product",
  "export.products.table.product": "Product",
  "export.products.table.quantity": "Quantity",
  "export.products.table.hs_code": "HS Code",
  "export.products.table.quality": "Quality",
  "export.products.table.actions": "Actions",
  "export.products.inherited": "Inherited from",

  // Shipments section
  "export.shipments.integration_title": "Shipment Integration",
  "export.shipments.integration_description": "Link shipments to this export declaration",
  "export.shipments.selected_count": "Selected",
  "export.shipments.shipment_count": "shipments",

  // Documents section
  "export.documents.management_title": "Document Management",
  "export.documents.management_description": "Upload and manage export documentation",
  "export.documents.existing_document": "Existing Document",
  "export.documents.upload_new_title": "Upload New Documents",
  "export.documents.upload_new_description": "Add additional documentation for this declaration",
  "export.documents.choose_files": "Choose Files",

  // Additional edit form fields
  "export.edit.declaration_number": "Declaration Number",
  "export.edit.status": "Status",
  "export.contract.title": "Contract Information",
  "export.documents.current_title": "Current Documents",
  "export.form.declaration_items": "Declaration Items",
  "export.shipments.select_label": "Select Shipments",

  // ✅ NEW: MRP Planning Report - English
  "mrpPlanning.title": "MRP Planning Dashboard",
  "mrpPlanning.subtitle": "Comprehensive Material Requirements Planning with proven data relationships",
  "mrpPlanning.backToReports": "Back to Reports",
  "mrpPlanning.realTimeData": "Real-time Data",
  "mrpPlanning.fullMRPDashboard": "Full MRP Dashboard",
  "mrpPlanning.activeForecasts": "Active Forecasts",
  "mrpPlanning.demandForecastingPipeline": "Demand forecasting pipeline",
  "mrpPlanning.procurementPlans": "Procurement Plans",
  "mrpPlanning.materialProcurementPlanning": "Material procurement planning",
  "mrpPlanning.averageMargin": "Average Margin",
  "mrpPlanning.bomProfitabilityAnalysis": "BOM profitability analysis",
  "mrpPlanning.supplierPartners": "Supplier Partners",
  "mrpPlanning.leadTimeOptimization": "Lead time optimization",
  "mrpPlanning.provenMRPIntegration": "Proven MRP Integration",
  "mrpPlanning.integrationDescription": "This report integrates with the comprehensive MRP Planning Dashboard at /planning, which has been extensively tested with 50+ test scenarios and proven data relationships.",
  "mrpPlanning.provenDataFlow": "Proven Data Flow",
  "mrpPlanning.dataFlow1": "Products → BOMs → Raw Materials",
  "mrpPlanning.dataFlow2": "Demand Forecasts → Procurement Plans",
  "mrpPlanning.dataFlow3": "Supplier Lead Times → Container Optimization",
  "mrpPlanning.enterpriseFeatures": "Enterprise Features",
  "mrpPlanning.feature1": "BOM Profitability Analysis",
  "mrpPlanning.feature2": "Multi-Supplier Procurement Planning",
  "mrpPlanning.feature3": "Container Loading Optimization",
  "mrpPlanning.accessFullDashboard": "Access Full MRP Planning Dashboard",
  "mrpPlanning.demandForecasting": "Demand Forecasting",
  "mrpPlanning.demandForecastingDesc": "Create and manage demand forecasts with BOM profitability analysis",
  "mrpPlanning.viewForecasts": "View Forecasts",
  "mrpPlanning.procurementPlanningTitle": "Procurement Planning",
  "mrpPlanning.procurementPlanningDesc": "Material requirements planning with supplier optimization",
  "mrpPlanning.viewPlans": "View Plans",
  "mrpPlanning.containerOptimization": "Container Optimization",
  "mrpPlanning.containerOptimizationDesc": "Optimize container loading and shipping efficiency",
  "mrpPlanning.optimize": "Optimize",
  "mrpPlanning.supplierPerformance": "Supplier Performance",
  "mrpPlanning.supplierPerformanceDesc": "Track supplier lead times and performance metrics",
  "mrpPlanning.viewSuppliers": "View Suppliers",

  // ✅ NEW: MRP Planning Main Page - English
  "planning.title": "MRP Planning",
  "planning.subtitle": "Material Requirements Planning and Demand Forecasting",
  "planning.refreshPriorities": "Refresh Priorities",
  "planning.newForecast": "New Forecast",

  // Status Cards
  "planning.activeForecasts": "Active Forecasts",
  "planning.draft": "Draft",
  "planning.pending": "Pending",
  "planning.approved": "Approved",
  "planning.rejected": "Rejected",
  "planning.procurementStatus": "Procurement Status",
  "planning.pendingApproval": "pending approval",
  "planning.ordered": "ordered",
  "planning.readyToOrder": "Ready to Order",
  "planning.procurementPlans": "Procurement Plans",
  "planning.estimated": "estimated",
  "planning.allApproved": "All Approved",
  "planning.actionRequired": "Action Required",
  "planning.urgentHighPriority": "Urgent/high priority plans needing approval",
  "planning.overdue": "Overdue",
  "planning.supplierNetwork": "Supplier Network",
  "planning.excellentRated": "excellent rated",
  "planning.avgLeadTime": "Avg Lead Time",
  "planning.daysAverage": "days average delivery",
  "planning.approvedPlansReady": "approved plans ready for ordering",
  "planning.actionAvailable": "Action Available",
  "planning.forecastProfitability": "Forecast Profitability",
  "planning.averageMargin": "Average Margin",
  "planning.totalRevenue": "Total Revenue",
  "planning.totalProfit": "Total Profit",
  "planning.profitabilityDistribution": "Profitability Distribution",
  "planning.excellent": "Excellent",
  "planning.good": "Good",
  "planning.fair": "Fair",
  "planning.poor": "Poor",

  // Tabs
  "planning.tabs.overview": "Overview",
  "planning.tabs.forecasting": "Forecasting",
  "planning.tabs.procurement": "Procurement",
  "planning.tabs.analytics": "Analytics",

  // Overview Tab
  "planning.demandForecastsProduction": "Demand Forecasts & Production Planning",
  "planning.activeForecastsDescription": "Active forecasts driving material requirements",
  "planning.pending": "pending",
  "planning.units": "units",
  "planning.margin": "margin",
  "planning.generatingPlans": "Generating procurement plans",
  "planning.materials": "materials",
  "planning.viewAllForecasts": "View All Forecasts",

  // Material Procurement
  "planning.materialProcurementStatus": "Material Procurement Status",
  "planning.materialsNeeded": "Materials needed for approved forecasts",
  "planning.requireImmediateAction": "require immediate action",
  "planning.target": "Target",
  "planning.estimatedCost": "Estimated Cost",
  "planning.viewAllPlans": "View All Plans",

  // Supplier Network
  "planning.supplierNetworkSummary": "Supplier Network Summary",
  "planning.keySuppliersSupporting": "Key suppliers supporting your active procurement plans",
  "planning.activeSuppliers": "Active Suppliers",
  "planning.totalProcurementValue": "Total Procurement Value",
  "planning.priorityLevels": "Priority Levels",
  "planning.critical": "CRITICAL",
  "planning.immediate": "Immediate",
  "planning.high": "HIGH",
  "planning.soon": "Soon",
  "planning.normal": "NORMAL",
  "planning.standard": "Standard",
  "planning.low": "LOW",
  "planning.future": "Future",

  // Procurement Tab
  "planning.purchaseRecommendations": "Purchase Recommendations",
  "planning.material": "Material",
  "planning.quantity": "Quantity",
  "planning.supplier": "Supplier",
  "planning.targetDate": "Target Date",
  "planning.cost": "Cost",
  "planning.urgency": "Urgency",

  // Analytics Tab
  "planning.mrpSystemAnalytics": "MRP System Analytics",
  "planning.realTimeAnalytics": "Real-time analytics and performance insights from your MRP operations",
  "planning.performanceOverview": "Performance Overview",
  "planning.totalForecasts": "Total Forecasts",
  "planning.totalPlans": "Total Plans",
  "planning.totalValue": "Total Value",
  "planning.avgMargin": "Avg Margin",

  // ✅ RESTORED: Missing Secondary KPI Keys - English
  "planning.supplierNetwork": "Supplier Network",
  "planning.excellentRated": "excellent rated",
  "planning.daysAverage": "days average delivery",
  "planning.costEfficiency": "Cost Efficiency",
  "planning.containerUtilization": "container utilization",
  "planning.optimized": "Optimized",
  "planning.systemHealth": "System Health",
  "planning.operationalEfficiency": "operational efficiency",
  "planning.forecastsAnalyzed": "forecasts analyzed",

  // ✅ RESTORED: Quick Actions & Implementation Status - English
  "planning.quickActions": "Quick Actions",
  "planning.commonMRPTasks": "Common MRP planning tasks and workflows",
  "planning.createForecast": "Create Forecast",
  "planning.generateDemandForecast": "Generate demand forecast",
  "planning.procurementPlanning": "Procurement Planning",
  "planning.manageProcurementPlans": "Manage procurement plans",
  "planning.containerOptimization": "Container Optimization",
  "planning.optimizeShipping": "Optimize shipping",
  "planning.performanceReport": "Performance Report",
  "planning.viewAnalytics": "View analytics",
  "planning.mrpImplementationStatus": "MRP Implementation Status",
  "planning.databaseSchema": "Database Schema",
  "planning.complete": "Complete",
  "planning.demandForecastingService": "Demand Forecasting Service",
  "planning.procurementPlanningService": "Procurement Planning Service",
  "planning.supplierLeadTimeManagement": "Supplier Lead Time Management",
  "planning.containerOptimizationEngine": "Container Optimization Engine",
  "planning.apiEndpoints": "API Endpoints",
  "planning.mrpDashboard": "MRP Dashboard",
  "planning.workflowIntegration": "Workflow Integration",
  "planning.inProgress": "In Progress",
  "planning.advancedAnalytics": "Advanced Analytics",
  "planning.nextPhase": "Next Phase",

  // ✅ FIXED: Data Display Fields - English
  "planning.period": "Period",
  "planning.confidence": "Confidence",
  "planning.status": "Status",

  // ✅ NEW: Procurement Page - English
  "procurement.title": "Procurement Planning",
  "procurement.subtitle": "Material requirements planning and supplier management",
  "procurement.searchPlaceholder": "Search procurement plans...",
  "procurement.newPlan": "New Plan",
  "procurement.viewPlan": "View Plan",
  "procurement.editPlan": "Edit Plan",
  "procurement.deletePlan": "Delete Plan",
  "procurement.backToPlanning": "Back to Planning",
  "procurement.planDetails": "Plan Details",
  "procurement.materialInfo": "Material Information",
  "procurement.supplierInfo": "Supplier Information",
  "procurement.planningInfo": "Planning Information",
  "procurement.actions": "Actions",
  "procurement.save": "Save",
  "procurement.cancel": "Cancel",
  "procurement.delete": "Delete",
  "procurement.confirmDelete": "Are you sure you want to delete this procurement plan?",
  "procurement.deleteSuccess": "Procurement plan deleted successfully",
  "procurement.saveSuccess": "Procurement plan saved successfully",
  "procurement.loadError": "Failed to load procurement plan",
  "procurement.saveError": "Failed to save procurement plan",
  "procurement.deleteError": "Failed to delete procurement plan",

  // ✅ NEW: Main Reports Page - English
  "reports.title": "Reports & Analytics",
  "reports.subtitle": "Comprehensive business intelligence and performance analytics",
  "reports.kpiOverview": "KPI Overview",
  "reports.totalRevenue": "Total Revenue",
  "reports.activeCustomers": "Active Customers",
  "reports.completedOrders": "Completed Orders",
  "reports.qualityScore": "Quality Score",
  "reports.essentialReports": "Essential Reports",
  "reports.essentialReportsDesc": "Core business intelligence reports with real data integration",
  "reports.viewReport": "View Report",
  "reports.mrpPlanning": "MRP Planning Dashboard",
  "reports.mrpPlanningDesc": "Demand forecasting, BOM profitability, procurement planning, and supplier performance",
  "reports.financialPerformance": "Financial Performance",
  "reports.financialPerformanceDesc": "P&L analysis, AR/AP aging, and cash flow projections with real invoice data",
  "reports.productionAnalytics": "Production Analytics",
  "reports.productionAnalyticsDesc": "Work order efficiency, capacity analysis, and quality integration insights",
  "reports.qualityMetrics": "Quality Metrics",
  "reports.qualityMetricsDesc": "Inspection status, defect analysis, and quality trend monitoring",
  "reports.inventoryIntelligence": "Inventory Intelligence",
  "reports.inventoryIntelligenceDesc": "Stock composition, reorder recommendations, and turnover analysis",
  "reports.businessIntelligence": "Business Intelligence",
  "reports.businessIntelligenceDesc": "Executive KPIs, customer analytics, contract performance, and strategic insights",
  "reports.migrationNotice": "Migration Notice",
  "reports.migrationNoticeDesc": "Reports system has been optimized from 14 reports to 6 essential reports aligned with proven MRP workflows. Navigation simplified from 3-level to 2-level for better user experience. All reports use real data from tested database relationships with consistent professional UI/UX matching MRP Planning Dashboard quality standards.",
  "reports.simplifiedArchitecture": "Simplified Architecture",
  "reports.essentialReportsBadge": "6 Essential Reports",
  "reports.from": "from",
  "reports.arInvoices": "AR invoices",
  "reports.productionEfficiency": "Production Efficiency",
  "reports.of": "of",
  "reports.ordersCompleted": "orders completed",
  "reports.qualityPassRate": "Quality Pass Rate",
  "reports.inspectionsPassed": "inspections passed",
  "reports.inventoryValue": "Inventory Value",
  "reports.across": "across",
  "reports.stockItems": "stock items",
  "reports.optimized": "Optimized",
  "reports.optimizedDescription": "Reduced from 14 reports to 6 essential reports aligned with proven MRP workflows",
  "reports.simplified": "Simplified",
  "reports.simplifiedDescription": "Navigation flattened from 3-level to 2-level for better user experience",
  "reports.integrated": "Integrated",
  "reports.integratedDescription": "All reports use real data from tested database relationships",
  "reports.professional": "Professional",
  "reports.professionalDescription": "Consistent UI/UX matching MRP Planning Dashboard quality standards",

  // ✅ NEW: Report Status Labels - English
  "reports.status.active": "Active",
  "reports.status.beta": "Beta",
  "reports.status.coming_soon": "Coming Soon",

  // ✅ NEW: MRP Planning Report Card - English
  "reports.mrp-planning.title": "MRP Planning Dashboard",
  "reports.mrp-planning.description": "Demand forecasting, BOM profitability, procurement planning, and supplier performance",
  "reports.mrp-planning.metrics.0.label": "Active Forecasts",
  "reports.mrp-planning.metrics.1.label": "Procurement Plans",
  "reports.mrp-planning.metrics.2.label": "Integration",

  // ✅ NEW: Financial Performance Report Card - English
  "reports.financial-performance.title": "Financial Performance",
  "reports.financial-performance.description": "P&L analysis, AR/AP aging, and cash flow projections with real invoice data",
  "reports.financial-performance.metrics.0.label": "Current Revenue",
  "reports.financial-performance.metrics.1.label": "Current Expenses",
  "reports.financial-performance.metrics.2.label": "Active Customers",

  // ✅ NEW: Production Analytics Report Card - English
  "reports.production-analytics.title": "Production Analytics",
  "reports.production-analytics.description": "Work order efficiency, capacity analysis, and quality integration insights",
  "reports.production-analytics.metrics.0.label": "Total Work Orders",
  "reports.production-analytics.metrics.1.label": "Completed Orders",
  "reports.production-analytics.metrics.2.label": "Efficiency Rate",

  // ✅ NEW: Quality Metrics Report Card - English
  "reports.quality-metrics.title": "Quality Metrics",
  "reports.quality-metrics.description": "Inspection status, defect analysis, and quality trend monitoring",
  "reports.quality-metrics.metrics.0.label": "Total Inspections",
  "reports.quality-metrics.metrics.1.label": "Passed Inspections",
  "reports.quality-metrics.metrics.2.label": "Pass Rate",

  // ✅ NEW: Inventory Intelligence Report Card - English
  "reports.inventory-intelligence.title": "Inventory Intelligence",
  "reports.inventory-intelligence.description": "Stock composition, reorder recommendations, and turnover analysis",
  "reports.inventory-intelligence.metrics.0.label": "Stock Lots",
  "reports.inventory-intelligence.metrics.1.label": "Total Value",
  "reports.inventory-intelligence.metrics.2.label": "Health Status",

  // ✅ NEW: Business Intelligence Report Card - English
  "reports.business-intelligence.title": "Business Intelligence",
  "reports.business-intelligence.description": "Executive KPIs, customer analytics, contract performance, and strategic insights",
  "reports.business-intelligence.metrics.0.label": "Current Revenue",
  "reports.business-intelligence.metrics.1.label": "Active Customers",
  "reports.business-intelligence.metrics.2.label": "Contract Performance",

  // ✅ NEW: Report Status Labels - English
  "reports.status.active": "Active",
  "reports.status.beta": "Beta",
  "reports.status.coming_soon": "Coming Soon",

  // ✅ NEW: MRP Planning Report Card - English
  "reports.mrp-planning.title": "MRP Planning Dashboard",
  "reports.mrp-planning.description": "Demand forecasting, BOM profitability, procurement planning, and supplier performance",
  "reports.mrp-planning.metrics.0.label": "Active Forecasts",
  "reports.mrp-planning.metrics.1.label": "Procurement Plans",
  "reports.mrp-planning.metrics.2.label": "Integration",

  // ✅ NEW: Financial Performance Report Card - English
  "reports.financial-performance.title": "Financial Performance",
  "reports.financial-performance.description": "P&L analysis, AR/AP aging, and cash flow projections with real invoice data",
  "reports.financial-performance.metrics.0.label": "Current Revenue",
  "reports.financial-performance.metrics.1.label": "Current Expenses",
  "reports.financial-performance.metrics.2.label": "Active Customers",

  // ✅ NEW: Production Analytics Report Card - English
  "reports.production-analytics.title": "Production Analytics",
  "reports.production-analytics.description": "Work order efficiency, capacity analysis, and quality integration insights",
  "reports.production-analytics.metrics.0.label": "Total Work Orders",
  "reports.production-analytics.metrics.1.label": "Completed Orders",
  "reports.production-analytics.metrics.2.label": "Efficiency Rate",

  // ✅ NEW: Quality Metrics Report Card - English
  "reports.quality-metrics.title": "Quality Metrics",
  "reports.quality-metrics.description": "Inspection status, defect analysis, and quality trend monitoring",
  "reports.quality-metrics.metrics.0.label": "Total Inspections",
  "reports.quality-metrics.metrics.1.label": "Passed Inspections",
  "reports.quality-metrics.metrics.2.label": "Pass Rate",

  // ✅ NEW: Inventory Intelligence Report Card - English
  "reports.inventory-intelligence.title": "Inventory Intelligence",
  "reports.inventory-intelligence.description": "Stock composition, reorder recommendations, and turnover analysis",
  "reports.inventory-intelligence.metrics.0.label": "Stock Lots",
  "reports.inventory-intelligence.metrics.1.label": "Total Value",
  "reports.inventory-intelligence.metrics.2.label": "Health Status",

  // ✅ NEW: Business Intelligence Report Card - English
  "reports.business-intelligence.title": "Business Intelligence",
  "reports.business-intelligence.description": "Executive KPIs, customer analytics, contract performance, and strategic insights",
  "reports.business-intelligence.metrics.0.label": "Current Revenue",
  "reports.business-intelligence.metrics.1.label": "Active Customers",
  "reports.business-intelligence.metrics.2.label": "Contract Performance",

  // ✅ NEW: Financial Performance Report - English
  "financialPerformance.title": "Financial Performance",
  "financialPerformance.subtitle": "Comprehensive P&L, AR/AP aging, and cash flow analysis with real invoice data",
  "financialPerformance.backToReports": "Back to Reports",
  "financialPerformance.realData": "Real Data",
  "financialPerformance.totalRevenue": "Total Revenue",
  "financialPerformance.netProfit": "Net Profit",
  "financialPerformance.profitMargin": "Profit Margin",
  "financialPerformance.outstandingAR": "Outstanding AR",
  "financialPerformance.accountsReceivableAging": "Accounts Receivable Aging",
  "financialPerformance.growthFromLastMonth": "{growth}% from last month",
  "financialPerformance.currentPeriod": "Current Period",
  "financialPerformance.overview": "Overview",
  "financialPerformance.arAging": "AR Aging",
  "financialPerformance.apAging": "AP Aging",
  "financialPerformance.cashFlow": "Cash Flow",
  "financialPerformance.profitLossSummary": "Profit & Loss Summary",
  "financialPerformance.currentVsPrevious": "Current vs Previous Month",
  "financialPerformance.revenue": "Revenue",
  "financialPerformance.expenses": "Expenses",
  "financialPerformance.recentArInvoices": "Recent AR Invoices",
  "financialPerformance.latestReceivableTransactions": "Latest receivable transactions",
  "financialPerformance.unknownCustomer": "Unknown Customer",
  "financialPerformance.status.paid": "Paid",
  "financialPerformance.status.pending": "Pending",
  "financialPerformance.status.overdue": "Overdue",
  "financialPerformance.status.draft": "Draft",
  "financialPerformance.outstandingCustomerPayments": "Outstanding customer payments by aging",
  "financialPerformance.current": "Current",
  "financialPerformance.days1to30": "1-30 Days",
  "financialPerformance.days31to60": "31-60 Days",
  "financialPerformance.days60Plus": "60+ Days",
  "financialPerformance.currentPercentage": "Current ({percentage}%)",
  "financialPerformance.accountsPayableAging": "Accounts Payable Aging",
  "financialPerformance.outstandingSupplierPayments": "Outstanding supplier payments by aging",
  "financialPerformance.cashFlowAnalysis": "Cash Flow Analysis",
  "financialPerformance.projectedCashFlow": "Projected cash flow based on AR/AP aging",
  "financialPerformance.expectedInflows": "Expected Inflows (AR)",
  "financialPerformance.expectedOutflows": "Expected Outflows (AP)",
  "financialPerformance.thisMonth": "This Month",
  "financialPerformance.next30Days": "Next 30 Days",
  "financialPerformance.netCashFlowProjection": "Net Cash Flow Projection",
  "financialPerformance.realFinancialDataIntegration": "Real Financial Data Integration",
  "financialPerformance.dataSourceDescription": "This financial performance report is generated from your real Manufacturing ERP data: AR invoices ({arCount} current), AP invoices ({apCount} current), and sales/purchase contracts. All calculations use actual transaction data with proper multi-tenant security.",
  "financialPerformance.fromLastMonth": "from last month",

  // ✅ NEW: Production Analytics Report - English
  "productionAnalytics.title": "Production Analytics",
  "productionAnalytics.subtitle": "Work order efficiency, capacity analysis, and quality integration insights",
  "productionAnalytics.backToReports": "Back to Reports",
  "productionAnalytics.realTimeData": "Real-time Data",
  "productionAnalytics.totalWorkOrders": "Total Work Orders",
  "productionAnalytics.completedOrders": "Completed Orders",
  "productionAnalytics.averageEfficiency": "Average Efficiency",
  "productionAnalytics.qualityIntegration": "Quality Integration",
  "productionAnalytics.productionStatusBreakdown": "Production Status Breakdown",
  "productionAnalytics.workOrdersByStatus": "Work orders by current status",
  "productionAnalytics.status.pending": "Pending",
  "productionAnalytics.status.in_progress": "In Progress",
  "productionAnalytics.status.completed": "Completed",
  "productionAnalytics.status.cancelled": "Cancelled",
  "productionAnalytics.overview": "Overview",
  "productionAnalytics.efficiency": "Efficiency",
  "productionAnalytics.quality": "Quality",
  "productionAnalytics.trends": "Trends",
  "productionAnalytics.productionEfficiencyMetrics": "Production Efficiency Metrics",
  "productionAnalytics.workOrderCompletionRates": "Work order completion rates and performance analysis",
  "productionAnalytics.completionRate": "Completion Rate",
  "productionAnalytics.averageLeadTime": "Average Lead Time",
  "productionAnalytics.days": "days",
  "productionAnalytics.qualityIntegrationMetrics": "Quality Integration Metrics",
  "productionAnalytics.qualityInspectionIntegration": "Quality inspection integration with production workflow",
  "productionAnalytics.inspectionsCompleted": "Inspections Completed",
  "productionAnalytics.qualityPassRate": "Quality Pass Rate",
  "productionAnalytics.recentWorkOrders": "Recent Work Orders",
  "productionAnalytics.latestProductionActivity": "Latest production activity and progress tracking",
  "productionAnalytics.workOrder": "Work Order",
  "productionAnalytics.product": "Product",
  "productionAnalytics.status": "Status",
  "productionAnalytics.progress": "Progress",
  "productionAnalytics.unknownProduct": "Unknown Product",
  "productionAnalytics.noWorkOrders": "No work orders available",
  "productionAnalytics.workOrdersWillAppear": "Work orders will appear as production activities are created",
  "productionAnalytics.realProductionDataIntegration": "Real Production Data Integration",
  "productionAnalytics.dataSourceDescription": "This production analytics report integrates with your comprehensive work order management system, including production operations, quality inspections, and inventory integration. All metrics are calculated from real production data with proven workflow relationships.",

  // ✅ NEW: Missing Production Analytics Keys - English
  "productionAnalytics.workOrdersCount": "{count} work orders",
  "productionAnalytics.completionRate": "Completion Rate",
  "productionAnalytics.ordersCompleted": "orders completed",
  "productionAnalytics.averageEfficiency": "Average Efficiency",
  "productionAnalytics.productionEfficiencyMetric": "Production efficiency metric",
  "productionAnalytics.qualityPassRate": "Quality Pass Rate",
  "productionAnalytics.inspectionsPassed": "inspections passed",
  "productionAnalytics.inProgress": "In Progress",
  "productionAnalytics.activeWorkOrders": "Active work orders",
  "productionAnalytics.productionStatusBreakdown": "Production Status Breakdown",
  "productionAnalytics.workOrdersByStatus": "Work orders by current status",
  "productionAnalytics.ordersCount": "{count} orders",
  "productionAnalytics.productionOverview": "Production Overview",
  "productionAnalytics.comprehensiveMetrics": "Comprehensive production metrics",
  "productionAnalytics.totalWorkOrders": "Total Work Orders",
  "productionAnalytics.completedOrders": "Completed Orders",
  "productionAnalytics.inProgressOrders": "In Progress Orders",
  "productionAnalytics.overview": "Overview",
  "productionAnalytics.efficiencyAnalysis": "Efficiency Analysis",
  "productionAnalytics.qualityIntegration": "Quality Integration",
  "productionAnalytics.recentOrders": "Recent Orders",
  "productionAnalytics.comprehensiveMetricsInsights": "Comprehensive production metrics and insights",
  "productionAnalytics.efficiencyAnalysisByProduct": "Efficiency Analysis by Product",
  "productionAnalytics.productionEfficiencyMetricsProduct": "Production efficiency metrics by product",
  "productionAnalytics.product": "Product",
  "productionAnalytics.avgEfficiency": "Avg Efficiency",
  "productionAnalytics.totalOrders": "Total Orders",
  "productionAnalytics.completed": "Completed",
  "productionAnalytics.customer": "Customer",
  "productionAnalytics.qualityInspectionStatus": "Quality Inspection Status",
  "productionAnalytics.passedInspections": "Passed Inspections",
  "productionAnalytics.failedInspections": "Failed Inspections",
  "productionAnalytics.pendingInspections": "Pending Inspections",
  "productionAnalytics.qualityIntegrationRate": "Quality Integration Rate",
  "productionAnalytics.overallPassRate": "Overall pass rate",
  "productionAnalytics.recentWorkOrders": "Recent Work Orders",
  "productionAnalytics.latestProductionOrders": "Latest production orders and progress",
  "productionAnalytics.workOrder": "Work Order",
  "productionAnalytics.quality": "Quality",
  "productionAnalytics.stock": "Stock",
  "productionAnalytics.productionDataIntegration": "Production Data Integration",
  "productionAnalytics.workOrders": "work orders",
  "productionAnalytics.orders": "orders",

  // ✅ NEW: Quality Metrics Report - English
  "qualityMetrics.title": "Quality Metrics",
  "qualityMetrics.subtitle": "Inspection status, defect analysis, and quality trend monitoring",
  "qualityMetrics.backToReports": "Back to Reports",
  "qualityMetrics.realTimeData": "Real-time Data",
  "qualityMetrics.totalInspections": "Total Inspections",
  "qualityMetrics.passRate": "Pass Rate",
  "qualityMetrics.activeDefects": "Active Defects",
  "qualityMetrics.certificatesIssued": "Certificates Issued",
  "qualityMetrics.qualityStatusOverview": "Quality Status Overview",
  "qualityMetrics.inspectionsByStatus": "Inspections by current status",
  "qualityMetrics.status.pending": "Pending",
  "qualityMetrics.status.passed": "Passed",
  "qualityMetrics.status.failed": "Failed",
  "qualityMetrics.status.quarantined": "Quarantined",
  "qualityMetrics.overview": "Overview",
  "qualityMetrics.defects": "Defects",
  "qualityMetrics.inspections": "Inspections",
  "qualityMetrics.trends": "Trends",
  "qualityMetrics.defectSeverityAnalysis": "Defect Severity Analysis",
  "qualityMetrics.defectsByImpactLevel": "Defects categorized by impact level and severity",
  "qualityMetrics.critical": "Critical",
  "qualityMetrics.major": "Major",
  "qualityMetrics.minor": "Minor",
  "qualityMetrics.inspectionTypeBreakdown": "Inspection Type Breakdown",
  "qualityMetrics.inspectionsByType": "Quality inspections by type and frequency",
  "qualityMetrics.incoming": "Incoming",
  "qualityMetrics.inProcess": "In-Process",
  "qualityMetrics.final": "Final",
  "qualityMetrics.productQualityMetrics": "Product Quality Metrics",
  "qualityMetrics.qualityPerformanceByProduct": "Quality performance analysis by product category",
  "qualityMetrics.product": "Product",
  "qualityMetrics.inspections": "Inspections",
  "qualityMetrics.passRate": "Pass Rate",
  "qualityMetrics.defectRate": "Defect Rate",
  "qualityMetrics.unknownProduct": "Unknown Product",
  "qualityMetrics.recentInspections": "Recent Inspections",
  "qualityMetrics.latestQualityActivity": "Latest quality control activity and inspection results",
  "qualityMetrics.inspection": "Inspection",
  "qualityMetrics.type": "Type",
  "qualityMetrics.status": "Status",
  "qualityMetrics.date": "Date",
  "qualityMetrics.noInspections": "No inspections available",
  "qualityMetrics.inspectionsWillAppear": "Quality inspections will appear as quality control activities are performed",
  "qualityMetrics.qualityTrendsAnalysis": "Quality Trends Analysis",
  "qualityMetrics.historicalQualityPerformance": "Historical quality performance and improvement tracking",
  "qualityMetrics.noTrendData": "No trend data available yet",
  "qualityMetrics.trendsWillAppear": "Quality trends will appear as inspection data accumulates over time",
  "qualityMetrics.realQualityDataIntegration": "Real Quality Data Integration",
  "qualityMetrics.dataSourceDescription": "This quality metrics report integrates with your comprehensive quality control system, including inspections, defect tracking, certificates, and production workflow integration. All metrics are calculated from real quality data with regulatory compliance standards.",

  // ✅ NEW: Missing Quality Metrics Keys - English
  "qualityMetrics.liveQualityData": "Live Quality Data",
  "qualityMetrics.inspectionsCount": "{count} inspections",
  "qualityMetrics.overallPassRate": "Overall Pass Rate",
  "qualityMetrics.inspectionsPassed": "inspections passed",
  "qualityMetrics.defectRate": "Defect Rate",
  "qualityMetrics.defectsIdentified": "defects identified",
  "qualityMetrics.pendingInspections": "Pending Inspections",
  "qualityMetrics.awaitingQualityReview": "awaiting quality review",
  "qualityMetrics.certificateValidity": "Certificate Validity",
  "qualityMetrics.inspectionStatusBreakdown": "Inspection Status Breakdown",
  "qualityMetrics.qualityInspectionResultsDistribution": "Quality inspection results distribution",
  "qualityMetrics.passed": "Passed",
  "qualityMetrics.failed": "Failed",
  "qualityMetrics.pending": "Pending",
  "qualityMetrics.quarantined": "Quarantined",
  "qualityMetrics.defectSeverityAnalysis": "Defect Severity Analysis",
  "qualityMetrics.breakdownDefectsBySeverity": "Breakdown of defects by severity level",
  "qualityMetrics.inspectionTypes": "Inspection Types",
  "qualityMetrics.productQuality": "Product Quality",
  "qualityMetrics.qualityTrends": "Quality Trends",
  "qualityMetrics.recentInspections": "Recent Inspections",
  "qualityMetrics.qualityInspectionsByType": "Quality Inspections by Type",
  "qualityMetrics.performanceMetricsDifferentInspectionTypes": "Performance metrics for different inspection types",
  "qualityMetrics.inspectionType": "Inspection Type",
  "qualityMetrics.totalInspections": "Total Inspections",
  "qualityMetrics.performance": "Performance",
  "qualityMetrics.inspections": "inspections",
  "qualityMetrics.certificatesValid": "certificates valid",

  // ✅ NEW: Inventory Intelligence Report - English
  "inventoryIntelligence.title": "Inventory Intelligence",
  "inventoryIntelligence.subtitle": "Stock composition, reorder recommendations, and turnover analysis",
  "inventoryIntelligence.backToReports": "Back to Reports",
  "inventoryIntelligence.realTimeData": "Real-time Data",
  "inventoryIntelligence.totalStockLots": "Total Stock Lots",
  "inventoryIntelligence.totalValue": "Total Value",
  "inventoryIntelligence.lowStockItems": "Low Stock Items",
  "inventoryIntelligence.inventoryHealth": "Inventory Health",
  "inventoryIntelligence.inventoryComposition": "Inventory Composition",
  "inventoryIntelligence.stockByCategory": "Stock distribution by category and type",
  "inventoryIntelligence.finishedGoods": "Finished Goods",
  "inventoryIntelligence.rawMaterials": "Raw Materials",
  "inventoryIntelligence.overview": "Overview",
  "inventoryIntelligence.analysis": "Analysis",
  "inventoryIntelligence.recommendations": "Recommendations",
  "inventoryIntelligence.locations": "Locations",
  "inventoryIntelligence.costDistribution": "Cost Distribution",
  "inventoryIntelligence.inventoryValueByRange": "Inventory value distribution by cost ranges",
  "inventoryIntelligence.highValue": "High Value",
  "inventoryIntelligence.mediumValue": "Medium Value",
  "inventoryIntelligence.lowValue": "Low Value",
  "inventoryIntelligence.averageUnitCost": "Average Unit Cost",
  "inventoryIntelligence.stockLevelsAnalysis": "Stock Levels Analysis",
  "inventoryIntelligence.currentStockStatus": "Current stock status and availability analysis",
  "inventoryIntelligence.item": "Item",
  "inventoryIntelligence.currentStock": "Current Stock",
  "inventoryIntelligence.reorderPoint": "Reorder Point",
  "inventoryIntelligence.status": "Status",
  "inventoryIntelligence.status.healthy": "Healthy",
  "inventoryIntelligence.status.low": "Low",
  "inventoryIntelligence.status.critical": "Critical",
  "inventoryIntelligence.unknownItem": "Unknown Item",
  "inventoryIntelligence.reorderRecommendations": "Reorder Recommendations",
  "inventoryIntelligence.intelligentRestockingSuggestions": "Intelligent restocking suggestions based on usage patterns",
  "inventoryIntelligence.suggestedQuantity": "Suggested Quantity",
  "inventoryIntelligence.priority": "Priority",
  "inventoryIntelligence.priority.high": "High",
  "inventoryIntelligence.priority.medium": "Medium",
  "inventoryIntelligence.priority.low": "Low",
  "inventoryIntelligence.noRecommendations": "No reorder recommendations at this time",
  "inventoryIntelligence.recommendationsWillAppear": "Reorder recommendations will appear based on stock levels and usage patterns",
  "inventoryIntelligence.locationDistribution": "Location Distribution",
  "inventoryIntelligence.stockByLocation": "Stock distribution across warehouse locations",
  "inventoryIntelligence.location": "Location",
  "inventoryIntelligence.items": "Items",
  "inventoryIntelligence.utilization": "Utilization",
  "inventoryIntelligence.unknownLocation": "Unknown Location",
  "inventoryIntelligence.turnoverAnalysis": "Turnover Analysis",
  "inventoryIntelligence.inventoryTurnoverPerformance": "Inventory turnover performance and efficiency metrics",
  "inventoryIntelligence.fastMoving": "Fast Moving",
  "inventoryIntelligence.slowMoving": "Slow Moving",
  "inventoryIntelligence.noTurnoverData": "No turnover data available yet",
  "inventoryIntelligence.turnoverWillAppear": "Turnover analysis will appear as inventory movement data accumulates",
  "inventoryIntelligence.realInventoryDataIntegration": "Real Inventory Data Integration",
  "inventoryIntelligence.dataSourceDescription": "This inventory intelligence report integrates with your comprehensive inventory management system, including stock lots, raw materials, demand forecasting, and procurement planning. All data is sourced from real inventory transactions with advanced analytics for optimization and decision support.",

  // ✅ NEW: Missing Inventory Intelligence Keys - English
  "inventoryIntelligence.liveInventoryData": "Live Inventory Data",
  "inventoryIntelligence.includesRawMaterials": "Includes raw materials",
  "inventoryIntelligence.units": "units",
  "inventoryIntelligence.capacity": "capacity",

  "inventoryIntelligence.totalInventoryValue": "Total Inventory Value",
  "inventoryIntelligence.stockHealthScore": "Stock Health Score",
  "inventoryIntelligence.activeLots": "active lots",
  "inventoryIntelligence.lowStockAlerts": "Low Stock Alerts",
  "inventoryIntelligence.itemsBelowReorderPoint": "items below reorder point",
  "inventoryIntelligence.storageUtilization": "Storage Utilization",

  "inventoryIntelligence.inventoryComposition": "Inventory Composition",
  "inventoryIntelligence.breakdownInventoryByTypeValue": "Breakdown of inventory by type and value",
  "inventoryIntelligence.finishedGoods": "Finished Goods",
  "inventoryIntelligence.rawMaterials": "Raw Materials",
  "inventoryIntelligence.finishedGoodsValue": "Finished goods value",
  "inventoryIntelligence.rawMaterialsValue": "Raw materials value",
  "inventoryIntelligence.costDistributionAnalysis": "Cost Distribution Analysis",
  "inventoryIntelligence.inventoryValueDistributionCostTiers": "Inventory value distribution by cost tiers",
  "inventoryIntelligence.highValue": "High Value",
  "inventoryIntelligence.mediumValue": "Medium Value",
  "inventoryIntelligence.lowValue": "Low Value",
  "inventoryIntelligence.averageUnitCost": "Average Unit Cost",
  "inventoryIntelligence.stockLevels": "Stock Levels",
  "inventoryIntelligence.reorderAnalysis": "Reorder Analysis",
  "inventoryIntelligence.locationDistribution": "Location Distribution",
  "inventoryIntelligence.turnoverAnalysis": "Turnover Analysis",
  "inventoryIntelligence.stockLevelsByProduct": "Stock Levels by Product",
  "inventoryIntelligence.currentInventoryLevelsValuesByProduct": "Current inventory levels and values by product",
  "inventoryIntelligence.product": "Product",
  "inventoryIntelligence.sku": "SKU",
  "inventoryIntelligence.quantity": "Quantity",
  "inventoryIntelligence.avgCost": "Avg Cost",
  "inventoryIntelligence.stockLots": "stock lots",
  "inventoryIntelligence.locations": "locations",
  "inventoryIntelligence.lots": "Lots",
  "inventoryIntelligence.totalValue": "Total Value",
  "inventoryIntelligence.intelligentReorderSuggestionsBasedStockDemand": "Intelligent reorder suggestions based on stock and demand patterns",
  "inventoryIntelligence.currentStock": "Current Stock",
  "inventoryIntelligence.forecastDemand": "Forecast Demand",
  "inventoryIntelligence.recommendation": "Recommendation",
  "inventoryIntelligence.noReorderRecommendations": "No reorder recommendations at this time",
  "inventoryIntelligence.reorderRecommendationsWillAppear": "Reorder recommendations will appear based on stock levels and demand patterns",
  "inventoryIntelligence.inventoryByLocation": "Inventory by Location",
  "inventoryIntelligence.distributionInventoryAcrossStorageLocations": "Distribution of inventory across storage locations",
  "inventoryIntelligence.itemCount": "Item Count",
  "inventoryIntelligence.totalQuantity": "Total Quantity",
  "inventoryIntelligence.utilization": "Utilization",
  "inventoryIntelligence.unknownLocation": "Unknown Location",
  "inventoryIntelligence.noLocationData": "No location data available yet",
  "inventoryIntelligence.locationDataWillAppear": "Location data will appear as inventory is distributed across storage locations",
  "inventoryIntelligence.inventoryTurnoverAnalysis": "Inventory Turnover Analysis",
  "inventoryIntelligence.productMovementTurnoverPerformance": "Product movement and turnover performance analysis",
  "inventoryIntelligence.movementStatus": "Movement Status",
  "inventoryIntelligence.good": "Good",
  "inventoryIntelligence.review": "Review",
  "inventoryIntelligence.turnoverDataWillAppear": "Turnover data will appear as inventory movement patterns develop",
  "inventoryIntelligence.inventoryIntelligenceIntegration": "Inventory Intelligence Integration",
  "inventoryIntelligence.costTrackingNotImplemented": "Cost tracking not implemented - shows quantity only",
  "inventoryIntelligence.notAvailable": "N/A",
  "inventoryIntelligence.noTurnoverData": "No turnover data available yet",

  // ✅ NEW: Business Intelligence Report - English
  "businessIntelligence.title": "Business Intelligence",
  "businessIntelligence.subtitle": "Executive dashboard with comprehensive business analytics and strategic insights",
  "businessIntelligence.backToReports": "Back to Reports",
  "businessIntelligence.liveBusinessData": "Live Business Data",
  "businessIntelligence.customersCount": "{count} customers",
  "businessIntelligence.totalRevenue": "Total Revenue",
  "businessIntelligence.activeContracts": "Active Contracts",
  "businessIntelligence.customerBase": "Customer Base",
  "businessIntelligence.operationalEfficiency": "Operational Efficiency",
  "businessIntelligence.profitMargin": "profit margin",
  "businessIntelligence.completionRate": "completion rate",
  "businessIntelligence.suppliers": "suppliers",
  "businessIntelligence.of": "of",
  "businessIntelligence.orders": "orders",
  "businessIntelligence.financialPerformance": "Financial Performance",
  "businessIntelligence.revenueExpensesProfitabilityAnalysis": "Revenue, expenses, and profitability analysis",
  "businessIntelligence.revenue": "Revenue",
  "businessIntelligence.expenses": "Expenses",
  "businessIntelligence.netProfitMargin": "Net Profit Margin",
  "businessIntelligence.qualityOperations": "Quality & Operations",
  "businessIntelligence.qualityMetricsOperationalPerformance": "Quality metrics and operational performance",
  "businessIntelligence.qualityScore": "Quality Score",
  "businessIntelligence.efficiency": "Efficiency",
  "businessIntelligence.qualityInspections": "Quality Inspections",
  "businessIntelligence.passed": "passed",
  "businessIntelligence.workOrders": "Work Orders",
  "businessIntelligence.completed": "completed",
  "businessIntelligence.failed": "Failed",
  "businessIntelligence.inProgress": "In Progress",
  "businessIntelligence.customerAnalytics": "Customer Analytics",
  "businessIntelligence.contractPerformance": "Contract Performance",
  "businessIntelligence.revenueTrends": "Revenue Trends",
  "businessIntelligence.operations": "Operations",
  "businessIntelligence.topCustomersByRevenue": "Top Customers by Revenue",
  "businessIntelligence.customerRelationshipRevenueAnalysis": "Customer relationship and revenue analysis",
  "businessIntelligence.customer": "Customer",
  "businessIntelligence.contracts": "Contracts",
  "businessIntelligence.lastActivity": "Last Activity",
  "businessIntelligence.status": "Status",
  "businessIntelligence.unknownCustomer": "Unknown Customer",
  "businessIntelligence.active": "Active",
  "businessIntelligence.noCustomerData": "No customer data available yet",
  "businessIntelligence.customerDataWillAppear": "Customer analytics will appear as business relationships develop",
  "businessIntelligence.contractPerformanceMetrics": "Contract Performance Metrics",
  "businessIntelligence.salesPurchaseContractAnalytics": "Sales and purchase contract analytics",
  "businessIntelligence.totalSalesContracts": "Total Sales Contracts",
  "businessIntelligence.completedContracts": "Completed Contracts",
  "businessIntelligence.avgContractValue": "Avg Contract Value",
  "businessIntelligence.contractCompletionRate": "Contract Completion Rate",
  "businessIntelligence.revenueTrendsLast6Months": "Revenue Trends (Last 6 Months)",
  "businessIntelligence.monthlyRevenuePerformanceGrowthAnalysis": "Monthly revenue performance and growth analysis",
  "businessIntelligence.invoices": "invoices",
  "businessIntelligence.noRevenueTrendData": "No revenue trend data available yet",
  "businessIntelligence.revenueTrendsWillAppear": "Revenue trends will appear as invoice data accumulates",
  "businessIntelligence.operationalPerformance": "Operational Performance",
  "businessIntelligence.productionQualityLogisticsMetrics": "Production, quality, and logistics metrics",
  "businessIntelligence.production": "Production",
  "businessIntelligence.totalWorkOrders": "Total Work Orders",
  "businessIntelligence.quality": "Quality",
  "businessIntelligence.inspections": "inspections",
  "businessIntelligence.inventory": "Inventory",
  "businessIntelligence.stockLots": "Stock Lots",
  "businessIntelligence.shipments": "shipments",
  "businessIntelligence.businessIntelligenceIntegration": "Business Intelligence Integration",
  "businessIntelligence.dataSourceDescription": "This business intelligence report integrates data from all Manufacturing ERP modules, including sales/purchase contracts, financial management, production, quality control, inventory, and shipping. All metrics are calculated from real business data with comprehensive cross-module analytics for strategic decision making.",

  // Forecasting
  "forecasting.demandForecasting": "Demand Forecasting",
  "forecasting.manageForecastsScenarios": "Manage demand forecasts and planning scenarios",
  "forecasting.backToPlanning": "Back to Planning",
  "forecasting.newForecast": "New Forecast",
  "forecasting.filtersSearch": "Filters & Search",
  "forecasting.filterSearchForecasts": "Filter and search demand forecasts",
  "forecasting.search": "Search",
  "forecasting.searchForecasts": "Search forecasts...",
  "forecasting.status": "Status",
  "forecasting.allStatuses": "All statuses",
  "forecasting.allStatusesSelect": "All Statuses",
  "forecasting.method": "Method",
  "forecasting.allMethods": "All methods",
  "forecasting.allMethodsSelect": "All Methods",
  "forecasting.pipelineAnalysis": "Pipeline Analysis",
  "forecasting.manualEntry": "Manual Entry",
  "forecasting.historicalData": "Historical Data",
  "forecasting.hybridMethod": "Hybrid Method",
  "forecasting.confidence": "Confidence",
  "forecasting.allLevels": "All levels",
  "forecasting.allLevelsSelect": "All Levels",
  "forecasting.low": "Low",
  "forecasting.medium": "Medium",
  "forecasting.high": "High",
  "forecasting.demandForecastsCount": "Demand Forecasts",
  "forecasting.allForecastsCompany": "All demand forecasts for your company",
  "forecasting.product": "Product",
  "forecasting.period": "Period",
  "forecasting.demand": "Demand",
  "forecasting.profitMargin": "Profit Margin",
  "forecasting.supplierPrefs": "Supplier Prefs",
  "forecasting.created": "Created",
  "forecasting.actions": "Actions",
  "forecasting.units": "units",
  "forecasting.noBomPrice": "No BOM/Price",
  "forecasting.auto": "Auto",
  "forecasting.unknownSupplier": "Unknown Supplier",
  "forecasting.noForecastsCreated": "No Forecasts Created",
  "forecasting.startCreatingFirst": "Start by creating your first demand forecast to enable material planning",
  "forecasting.createFirstForecast": "Create First Forecast",

  // Forecasting Detail/View Page
  "forecasting.backToForecasts": "Back to Forecasts",
  "forecasting.edit": "Edit",
  "forecasting.forecastSummary": "Forecast Summary",
  "forecasting.quantity": "Quantity",
  "forecasting.notes": "Notes",
  "forecasting.forecastProfitability": "Forecast Profitability",
  "forecasting.profitMarginAnalysis": "Profit Margin Analysis",
  "forecasting.profitMarginUnavailable": "Profit margin analysis unavailable",
  "forecasting.needsBomPrice": "Product needs BOM and selling price configuration",
  "forecasting.statusActions": "Status & Actions",
  "forecasting.currentStatus": "Current Status",
  "forecasting.lastUpdated": "Last Updated",
  "forecasting.createdBy": "Created By",
  "forecasting.procurementPlans": "Procurement Plans",
  "forecasting.generatedPlans": "Generated Plans",
  "forecasting.noProcurementPlans": "No procurement plans generated yet",
  "forecasting.generatePlansDescription": "Procurement plans will be automatically generated when this forecast is approved",

  // Forecasting Edit Page
  "forecasting.backToForecast": "Back to Forecast",
  "forecasting.editForecast": "Edit Forecast",
  "forecasting.forecastDetails": "Forecast Details",

  // Forecasting Create Page
  "forecasting.createDemandForecast": "Create Demand Forecast",
  "forecasting.newDemandForecast": "New Demand Forecast",

  // User Display
  "common.system": "System",
  "common.user": "User",

  // Demand Forecast Form
  "demandForecast.createTitle": "Create Demand Forecast",
  "demandForecast.editTitle": "Edit Demand Forecast",
  "demandForecast.description": "Generate demand forecasts based on sales pipeline analysis and historical data",
  "demandForecast.productSelection": "Product Selection",
  "demandForecast.selectProduct": "Select the product for demand forecasting",
  "demandForecast.product": "Product",
  "demandForecast.searchSelectProduct": "Search and select a product...",
  "demandForecast.selected": "Selected",
  "demandForecast.supplierOptional": "Supplier (Optional)",
  "demandForecast.autoSelectSupplier": "Auto-select best supplier",
  "demandForecast.preferredSupplier": "Preferred supplier",
  "demandForecast.chooseSupplier": "Choose a specific supplier or let the system automatically select the best option",
  "demandForecast.forecastConfiguration": "Forecast Configuration",
  "demandForecast.configurePeriod": "Configure forecast period and demand parameters",
  "demandForecast.forecastPeriod": "Forecast Period",
  "demandForecast.selectPeriod": "Select period",
  "demandForecast.forecastedDemand": "Forecasted Demand",
  "demandForecast.enterQuantity": "Enter quantity",
  "demandForecast.expectedDemand": "Expected demand quantity for the selected period",
  "demandForecast.confidenceLevel": "Confidence Level",
  "demandForecast.lowConfidence": "60-70% confidence",
  "demandForecast.mediumConfidence": "70-85% confidence",
  "demandForecast.highConfidence": "85-95% confidence",
  "demandForecast.forecastMethod": "Forecast Method",
  "demandForecast.pipelineAnalysis": "Pipeline Analysis",
  "demandForecast.historicalData": "Historical Data",
  "demandForecast.manualEntry": "Manual Entry",
  "demandForecast.hybridMethod": "Hybrid Method",
  "demandForecast.advancedSettings": "Advanced Settings",
  "demandForecast.seasonalityAdjustment": "Seasonality Adjustment",
  "demandForecast.noAdjustment": "No Adjustment",
  "demandForecast.applySeasonality": "Apply Seasonality",
  "demandForecast.trendFactor": "Trend Factor",
  "demandForecast.growthMultiplier": "Growth multiplier (1.0 = no change, 1.2 = 20% growth)",
  "demandForecast.additionalNotes": "Additional Notes",
  "demandForecast.addNotes": "Add any additional notes or assumptions...",
  "demandForecast.cancel": "Cancel",
  "demandForecast.changesUpdate": "Changes will update the existing forecast",
  "demandForecast.saving": "Saving...",
  "demandForecast.createForecast": "Create Forecast",
  "demandForecast.updateForecast": "Update Forecast",
  "demandForecast.success": "Success",
  "demandForecast.createdSuccessfully": "created successfully",
  "demandForecast.updatedSuccessfully": "updated successfully",
  "demandForecast.error": "Error",
  "demandForecast.failedToSave": "Failed to save demand forecast",

  // BOM (Bill of Materials)
  "bom.title": "Bill of Materials",
  "bom.subtitle": "Manage product BOMs and material requirements",
  "bom.manage_products": "Manage Products",
  "bom.total_products": "Total Products",
  "bom.avg_materials_per_product": "avg materials per product",
  "bom.with_bom": "With BOM",
  "bom.total_materials": "total materials",
  "bom.without_bom": "Without BOM",
  "bom.need_bom_configuration": "need BOM configuration",
  "bom.total_value": "Total Value",
  "bom.incomplete_boms": "incomplete BOMs",
  "bom.overview": "Overview",
  "bom.view_and_manage_description": "View and manage all product BOMs",
  "bom.refresh": "Refresh",
  "bom.search_products": "Search products...",
  "bom.all_products": "All Products",
  "bom.add_product": "Add Product",
  "bom.product": "Product",
  "bom.bom_status": "BOM Status",
  "bom.materials": "Materials",
  "bom.categories": "Categories",
  "bom.material_cost": "Material Cost",
  "bom.selling_price": "Selling Price",
  "bom.profit": "Profit",
  "bom.margin_percent": "Margin %",
  "bom.last_updated": "Last Updated",
  "bom.actions": "Actions",
  "bom.complete": "Complete",
  "bom.incomplete": "Incomplete",
  "bom.no_bom": "No BOM",
  "bom.complete_boms": "Complete BOMs",
  "bom.incomplete_boms_filter": "Incomplete BOMs",
  "bom.view": "View",
  "bom.edit": "Edit",
  "bom.delete": "Delete",
  "bom.no_products_found": "No products found",
  "bom.no_products_message": "No products match your search criteria",
  "bom.loading": "Loading...",
  "bom.error": "Error",
  "bom.failed_to_load": "Failed to load BOM data",

  // Work Orders
  "workorders.title": "Work Orders",
  "workorders.subtitle": "Track routing and operation progress.",
  "workorders.refresh": "Refresh",
  "workorders.new_work_order": "New Work Order",
  "workorders.total_orders": "Total Orders",
  "workorders.all_work_orders": "all work orders",
  "workorders.pending": "Pending",
  "workorders.awaiting_start": "awaiting start",
  "workorders.in_progress": "In Progress",
  "workorders.currently_active": "currently active",
  "workorders.completed": "Completed",
  "workorders.finished_orders": "finished orders",
  "workorders.overdue": "Overdue",
  "workorders.past_due_date": "past due date",
  "workorders.high_priority": "High Priority",
  "workorders.urgent_orders": "urgent orders",
  "workorders.search_work_orders": "Search work orders...",
  "workorders.all_status": "All Status",
  "workorders.all_contracts": "All Contracts",
  "workorders.all_products": "All Products",
  "workorders.work_orders": "Work Orders",
  "workorders.found_work_orders": "found work orders",
  "workorders.contract_work_order": "Contract / Work Order",
  "workorders.product": "Product",
  "workorders.quantity": "Quantity",
  "workorders.due_date": "Due Date",
  "workorders.priority": "Priority",
  "workorders.notes": "Notes",
  "workorders.actions": "Actions",
  "workorders.status": "Status",
  "workorders.progress": "Progress",
  "workorders.work_order": "work order",
  "workorders.work_orders_plural": "work orders",
  "workorders.not_set": "Not set",
  "workorders.no_notes": "No notes",
  "workorders.normal": "Normal",
  "workorders.high": "High",
  "workorders.urgent": "Urgent",
  "workorders.low": "Low",

  // Work Order Detail View
  "workorders.failed_to_load": "Failed to load",
  "workorders.production_completed": "Production completed successfully",
  "workorders.back_to_work_orders": "Back to Work Orders",
  "workorders.work_order_details": "Work order details",
  "workorders.complete_production": "Complete Production",
  "workorders.edit": "Edit",
  "workorders.work_order_overview": "Work Order Overview",
  "workorders.created": "Created",
  "workorders.sales_contract": "Sales Contract",
  "workorders.contract": "Contract",
  "workorders.customer": "Customer",
  "workorders.contract_date": "Contract Date",
  "workorders.no_sales_contract_linked": "No sales contract linked",
  "workorders.product_details": "Product Details",
  "workorders.sku": "SKU",
  "workorders.name": "Name",
  "workorders.unit": "Unit",
  "workorders.category": "Category",
  "workorders.quality_inspections": "Quality Inspections",
  "workorders.no_quality_inspections": "No quality inspections yet",
  "workorders.material_requirements": "Material Requirements",
  "workorders.raw_materials_needed": "Raw materials needed for production",
  "workorders.total_materials_required": "Total materials required",
  "workorders.different_materials": "different materials",
  "workorders.no_material_requirements": "No material requirements found",

  // Work Order Edit Page
  "workorders.back_to_view": "Back to View",
  "workorders.edit_work_order": "Edit Work Order",
  "workorders.cancel": "Cancel",
  "workorders.save_changes": "Save Changes",
  "workorders.work_order_information": "Work Order Information",
  "workorders.edit_work_order_details": "Edit Work Order Details",
  "workorders.add_production_notes": "Add production notes...",
  "workorders.work_order_updated": "Work order updated successfully",
  "workorders.failed_to_update": "Failed to update work order",
  "workorders.work_order_number": "Work Order Number",
  "workorders.sales_contract_number": "Sales Contract",
  "workorders.product_name": "Product",
  "workorders.customer_name": "Customer",
  "workorders.quantity_required": "Quantity Required",
  "workorders.due_date_label": "Due Date",
  "workorders.priority_label": "Priority",
  "workorders.status_label": "Status",
  "workorders.production_notes": "Production Notes",

  // Quality Control
  "quality.quality_control": "Quality Control",
  "quality.comprehensive_quality_management": "Comprehensive quality management system",
  "quality.title": "Quality Control",
  "quality.subtitle": "Manage quality inspections and certificates",
  "quality.track_quality_inspections": "Track quality inspections across all contracts",
  "quality.new_inspection": "New Inspection",
  "quality.total_inspections": "Total Inspections",
  "quality.across_contracts": "Across {count} contracts",
  "quality.pending_review": "Pending Review",
  "quality.awaiting_inspection_start": "awaiting inspection start",
  "quality.in_progress": "In Progress",
  "quality.currently_being_inspected": "currently being inspected",
  "quality.passed": "Passed",
  "quality.passed_quality_standards": "passed quality standards",
  "quality.failed": "Failed",
  "quality.require_corrective_action": "require corrective action",
  "quality.archived": "Archived",
  "quality.archived_inspections": "archived inspections",
  "quality.quality_workflow": "Quality Workflow",
  "quality.workflow_arrow": "→",
  "quality.workflow_or": "or",
  "quality.click_dashboard_cards": "Click dashboard cards to filter inspections",
  "quality.search_inspections": "Search inspections...",
  "quality.all_status": "All Status",
  "quality.quality_inspections": "Quality Inspections",
  "quality.found_inspections": "found inspections",
  "quality.contract_inspection": "Contract / Inspection",
  "quality.count_progress": "Count / Progress",
  "quality.customer_work_order": "Customer / Work Order",
  "quality.product": "Product",
  "quality.inspector": "Inspector",
  "quality.status": "Status",
  "quality.actions": "Actions",

  // Quality Analytics Dashboard
  "quality.error": "Error",
  "quality.failed_to_load_analytics": "Failed to load analytics data",
  "quality.quality_analytics": "Quality Analytics",
  "quality.advanced_quality_metrics": "Advanced quality metrics and performance insights",
  "quality.last_7_days": "Last 7 Days",
  "quality.last_30_days": "Last 30 Days",
  "quality.last_90_days": "Last 90 Days",
  "quality.last_year": "Last Year",
  "quality.refresh": "Refresh",
  "quality.pass_rate": "Pass Rate",
  "quality.excellent": "Excellent",
  "quality.good": "Good",
  "quality.fair": "Fair",
  "quality.poor": "Poor",
  "quality.defect_rate": "Defect Rate",
  "quality.target": "< 2% target",
  "quality.first_pass_yield": "First Pass Yield",
  "quality.industry_benchmark": "85% industry benchmark",
  "quality.quality_trends": "Quality Trends",
  "quality.pass_rate_percent": "Pass Rate %",
  "quality.defect_rate_percent": "Defect Rate %",
  "quality.no_trend_data": "No trend data available",
  "quality.defect_analysis": "Defect Analysis",
  "quality.no_defect_data": "No defect data available",
  "quality.last_updated": "Last updated: {timestamp}",

  // Inventory Management
  "inventory.title": "Inventory Management",
  "inventory.subtitle": "Manage stock levels, inbound and outbound operations",
  "inventory.overview.title": "Overview",
  "inventory.overview.last_updated": "Last updated: {time}",

  // KPI Cards
  "inventory.kpi.units_ready_to_ship": "units ready to ship",
  "inventory.kpi.products": "products",
  "inventory.kpi.low_stock": "low stock",
  "inventory.kpi.value": "Value: {amount}",
  "inventory.kpi.units_available_for_production": "units available for production",
  "inventory.kpi.lots": "lots",
  "inventory.kpi.expiring": "expiring",
  "inventory.kpi.combined_inventory_value": "combined inventory value",
  "inventory.kpi.locations": "locations",
  "inventory.kpi.transactions": "transactions",
  "inventory.kpi.quality_alerts": "quality alerts",

  // Navigation
  "inventory.raw_materials_nav.title": "Raw Materials",
  "inventory.raw_materials_nav.subtitle": "Manage raw material inventory",
  "inventory.raw_materials_nav.button": "View Raw Materials",

  // Quick Actions
  "inventory.quick_actions.title": "Quick Actions",
  "inventory.quick_actions.subtitle": "Common inventory operations",
  "inventory.quick_actions.receive": "Receive",
  "inventory.quick_actions.ship": "Ship",
  "inventory.quick_actions.transfer": "Transfer",
  "inventory.quick_actions.adjust": "Adjust",

  // Dialog Common
  "inventory.dialogs.product": "Product",
  "inventory.dialogs.quantity": "Quantity",
  "inventory.dialogs.location": "Location",
  "inventory.dialogs.location_optional": "Location (Optional)",
  "inventory.dialogs.from_location": "From Location",
  "inventory.dialogs.to_location": "To Location",
  "inventory.dialogs.reason": "Reason",
  "inventory.dialogs.reference": "Reference",
  "inventory.dialogs.notes": "Notes",
  "inventory.dialogs.notes_required": "Notes (Required)",
  "inventory.dialogs.cancel": "Cancel",
  "inventory.dialogs.processing": "Processing...",
  "inventory.dialogs.loading": "Loading...",
  "inventory.dialogs.select_product": "Select a product",
  "inventory.dialogs.select_product_with_stock": "Select a product with stock",
  "inventory.dialogs.any_location": "Any Location",
  "inventory.dialogs.available": "Available",
  "inventory.dialogs.max": "Max",
  "inventory.dialogs.po_number": "PO Number",
  "inventory.dialogs.order_number": "Order Number",
  "inventory.dialogs.additional_notes": "Additional notes",
  "inventory.dialogs.transfer_reason": "Transfer reason",
  "inventory.dialogs.adjustment_reason": "Adjustment reason",

  // Receive Dialog
  "inventory.dialogs.receive.title": "Receive Inventory",
  "inventory.dialogs.receive.description": "Add new stock to inventory",
  "inventory.dialogs.receive.button": "Receive Stock",

  // Ship Dialog
  "inventory.dialogs.ship.title": "Ship Inventory",
  "inventory.dialogs.ship.description": "Remove stock from inventory",
  "inventory.dialogs.ship.button": "Ship Stock",

  // Transfer Dialog
  "inventory.dialogs.transfer.title": "Transfer Inventory",
  "inventory.dialogs.transfer.description": "Move stock between locations",
  "inventory.dialogs.transfer.button": "Transfer Stock",

  // Adjust Dialog
  "inventory.dialogs.adjust.title": "Adjust Inventory",
  "inventory.dialogs.adjust.description": "Make inventory adjustments",
  "inventory.dialogs.adjust.button": "Adjust Stock",

  // Stock Inventory Section
  "inventory.stock_inventory.title": "Stock Inventory",
  "inventory.stock_inventory.subtitle": "items",
  "inventory.stock_inventory.search_placeholder": "Search products...",
  "inventory.stock_inventory.all_quality": "All Quality",
  "inventory.stock_inventory.all_locations": "All Locations",
  "inventory.stock_inventory.total_products": "Total Products",
  "inventory.stock_inventory.total_lots": "Total Lots",
  "inventory.stock_inventory.total_units": "Total Units",
  "inventory.stock_inventory.pending_quality": "Pending Quality",

  // Table Headers
  "inventory.stock_inventory.table.product": "Product",
  "inventory.stock_inventory.table.total_quantity": "Total Quantity",
  "inventory.stock_inventory.table.lots": "Lots",
  "inventory.stock_inventory.table.locations": "Locations",
  "inventory.stock_inventory.table.quality_status": "Quality Status",
  "inventory.stock_inventory.table.actions": "Actions",
  "inventory.stock_inventory.table.details": "Details",

  // Recent Activity
  "inventory.recent_activity.title": "Recent Activity",
  "inventory.recent_activity.subtitle": "Latest inventory transactions",

  // Location Fallbacks
  "inventory.unknown_location": "Unknown Location",

  // ✅ NEW: Analytics Tab Translations
  "inventory.analytics.title": "Comprehensive Analytics",
  "inventory.analytics.subtitle": "Advanced inventory insights and metrics",
  "inventory.analytics.view_analytics": "View Analytics",
  "inventory.analytics.loading": "Loading inventory analytics...",
  "inventory.analytics.stock_analytics": "Stock Analytics",
  "inventory.analytics.location_alerts": "Location Alerts",
  "inventory.analytics.location_utilization": "Location Utilization",
  "inventory.analytics.industrial_capacity": "Industrial capacity management",
  "inventory.analytics.flow_metrics_24h": "Flow Metrics (24h)",
  "inventory.analytics.workflow_analysis": "Manufacturing workflow analysis",
  "inventory.analytics.stock_distribution": "Stock Distribution",
  "inventory.analytics.stock_by_location": "Stock by Location",
  "inventory.analytics.quality_status": "Quality Status",
  "inventory.analytics.top_products_by_quantity": "Top Products by Quantity",
  "inventory.analytics.finished_goods_analytics": "Finished Goods Analytics",
  "inventory.analytics.finished_goods_metrics": "Real-time metrics for finished goods inventory",
  "inventory.analytics.finished_goods_only": "Finished Goods Only",
  "inventory.analytics.raw_materials_note": "Raw materials tracked separately",
  "inventory.analytics.refresh_data": "Refresh Data",
  "inventory.analytics.finished_goods_value": "Finished Goods Value",
  "inventory.analytics.fg_products": "FG Products",
  "inventory.analytics.finished_goods_units": "Finished Goods Units",
  "inventory.analytics.avg_unit": "Avg Unit",
  "inventory.analytics.unit": "unit",
  "inventory.analytics.low_stock_alerts": "Low Stock Alerts",
  "inventory.analytics.items_below": "Items below 10 units",
  "inventory.analytics.pending_inspections": "Pending Inspections",
  "inventory.analytics.quality_control_pending": "Quality control pending",
  "inventory.analytics.category_breakdown": "Category Breakdown",
  "inventory.analytics.inventory_by_location": "Inventory by Location",
  "inventory.analytics.total_value": "Total Value",
  "inventory.analytics.recent_activity": "Recent Activity",
  "inventory.analytics.raw_materials": "Raw Materials",
  "inventory.analytics.work_in_progress": "Work in Progress",
  "inventory.analytics.finished_goods": "Finished Goods",
  "inventory.analytics.no_location_data": "No location data available",
  "inventory.analytics.last_transactions": "Last {count} transactions",
  "inventory.analytics.unknown_product": "Unknown Product",
  "inventory.analytics.no_recent_transactions": "No recent transactions",
  "inventory.analytics.quality_status_distribution": "Quality Status Distribution",
  "inventory.analytics.last_updated": "Last updated",
  "inventory.analytics.failed_to_load": "Failed to load inventory data",
  "inventory.analytics.no_data_available": "No Inventory Data Available",
  "inventory.analytics.no_data_description": "No inventory data found for analytics. Please add some inventory items first.",
  "inventory.analytics.professional_costing": "Professional Costing",

  // ✅ NEW: Discrepancy Tab Translations
  "inventory.discrepancy.title": "Inventory Discrepancy Analysis",
  "inventory.discrepancy.subtitle": "Identify and resolve inventory inconsistencies",
  "inventory.discrepancy.run_analysis": "Run Analysis",
  "inventory.discrepancy.analyzing": "Analyzing...",
  "inventory.discrepancy.analysis_complete": "Analysis Complete",
  "inventory.discrepancy.analysis_results": "Found {totalIssues} issues: {highRiskCount} high risk, {mediumRiskCount} medium risk",
  "inventory.discrepancy.analysis_failed": "Analysis Failed",
  "inventory.discrepancy.unknown_product": "Unknown",
  "inventory.discrepancy.total_products": "Total Products",
  "inventory.discrepancy.high_risk": "High Risk",
  "inventory.discrepancy.shortages": "Shortages",
  "inventory.discrepancy.overages": "Overages",
  "inventory.discrepancy.table.product": "Product",
  "inventory.discrepancy.table.current_stock": "Current Stock",
  "inventory.discrepancy.table.expected_stock": "Expected Stock",
  "inventory.discrepancy.table.discrepancy": "Discrepancy",
  "inventory.discrepancy.table.risk_level": "Risk Level",
  "inventory.discrepancy.table.recommended_action": "Recommended Action",
  "inventory.discrepancy.table.actions": "Actions",
  "inventory.discrepancy.no_issues": "No Issues",
  "inventory.discrepancy.shortage": "Shortage",
  "inventory.discrepancy.overage": "Overage",
  "inventory.discrepancy.view_details": "View Details",
  "inventory.discrepancy.adjust": "Adjust",
  "inventory.discrepancy.click_run_analysis": "Click 'Run Analysis' to identify inventory discrepancies",

  // ✅ NEW: Shipping Audit Translations
  "inventory.discrepancy.shipping_audit_title": "Shipping Inventory Audit",
  "inventory.discrepancy.shipping_audit_subtitle": "Verify inventory reductions for shipped orders",
  "inventory.discrepancy.run_audit": "Run Audit",
  "inventory.discrepancy.click_run_audit": "Click 'Run Audit' to analyze shipped orders",
  "inventory.discrepancy.auditing": "Auditing...",
  "inventory.discrepancy.no_shipped_orders": "No Shipped Orders Found",
  "inventory.discrepancy.shipping_audit_complete": "Shipping Audit Complete",
  "inventory.discrepancy.audit_failed": "Audit Failed",
  "inventory.discrepancy.audit_summary": "Audit Summary:",
  "inventory.discrepancy.total_shipped_orders": "Total Shipped Orders:",
  "inventory.discrepancy.inventory_issues": "Inventory Issues:",
  "inventory.discrepancy.high_risk_orders": "High Risk:",
  "inventory.discrepancy.orders_need_attention": "orders need immediate attention",
  "inventory.discrepancy.table.shipment": "Shipment",
  "inventory.discrepancy.table.status": "Status",
  "inventory.discrepancy.table.total_qty": "Total Qty",
  "inventory.discrepancy.table.inventory_impact": "Inventory Impact",
  "inventory.discrepancy.table.recommended_action": "Recommended Action",
  "inventory.discrepancy.items": "items",
  "inventory.discrepancy.impact.correct": "Correct",
  "inventory.discrepancy.impact.partial": "Partial",
  "inventory.discrepancy.impact.missing": "Missing",
  "inventory.discrepancy.fix": "Fix",
  "inventory.discrepancy.unknown_product": "Unknown",

  "workorders.work_order_number": "Work Order Number",
  "workorders.sales_contract_number": "Sales Contract",
  "workorders.product_name": "Product",
  "workorders.customer_name": "Customer",
  "workorders.quantity_required": "Quantity Required",
  "workorders.due_date_label": "Due Date",
  "workorders.priority_label": "Priority",
  "workorders.status_label": "Status",
  "workorders.production_notes": "Production Notes",
}

const zh: Dict = {
  // App + Nav
  "app.name": "FC-CHINA",
  "nav.group.overview": "总览",
  "nav.group.master-data": "主数据",
  "nav.group.sales-purchasing": "销售采购", // ✅ LEGACY: Keep for backward compatibility
  "nav.group.sales-process": "销售流程", // ✅ NEW: Optimized grouping
  "nav.group.production": "生产管理", // ✅ LEGACY: Keep for backward compatibility
  "nav.group.production-planning": "生产计划", // ✅ NEW: Optimized grouping
  "nav.group.inventory-logistics": "库存物流", // ✅ LEGACY: Keep for backward compatibility
  "nav.group.quality-inventory": "质量库存", // ✅ NEW: Optimized grouping
  "nav.group.shipping-export": "运输出口", // ✅ NEW: Optimized grouping
  "nav.group.export-trade": "出口贸易", // ✅ LEGACY: Keep for backward compatibility
  "nav.group.finance-reporting": "财务报表",
  "nav.group.settings": "设置", // ✅ LEGACY: Keep for backward compatibility
  "nav.group.administration": "系统管理", // ✅ NEW: Optimized grouping
  "nav.item.dashboard": "仪表盘",
  "nav.item.customers": "客户管理",
  "nav.item.suppliers": "供应商",
  "nav.item.products": "产品管理",
  "nav.item.samples": "样品管理",
  "nav.item.sales-contracts": "销售合同",
  "nav.item.purchase-contracts": "采购合同",
  "nav.item.contract-templates": "合同模板",
  "nav.item.work-orders": "生产工单",
  "nav.item.bom-management": "物料清单",
  "nav.item.quality-control": "质量控制",
  "nav.item.mrp-planning": "MRP计划",
  "nav.item.inventory": "库存管理",
  "nav.item.raw-materials": "原材料",
  "nav.item.locations": "仓库位置",
  "nav.item.shipping": "物流运输",
  "nav.item.export-declarations": "出口报关",
  "nav.item.trade-compliance": "贸易合规",
  "nav.item.documentation": "文档管理",
  "nav.item.accounting": "财务会计",
  "nav.item.financial-dashboard": "财务仪表盘",
  "nav.item.accounts-receivable": "应收账款",
  "nav.item.accounts-payable": "应付账款",
  "nav.item.reports": "报表分析",
  "nav.item.company-profile": "公司资料",

  // Command
  "cmd.placeholder": "搜索或快速跳转...",

  // Dashboard
  "home.title": "外贸制造 ERP",
  "home.subtitle": "统一管理主数据、合同、生产、库存、出口报关与财务。",
  "home.quick.master": "添加主数据",
  "home.quick.contract": "创建合同",
  "home.quick.stock": "记录出入库",
  "home.quick.declaration": "新建报关单",
  "kpi.customers": "客户数",
  "kpi.products": "产品数",
  "kpi.suppliers": "供应商",
  "kpi.contracts": "合同",
  "kpi.suppliers.desc": "活跃供应商",
  "kpi.contracts.desc": "销售和采购合同",
  "kpi.onhand": "在库数量（合计）",
  "kpi.openWos": "未完成工单",

  // Dashboard Quick Actions
  "dashboard.quick_actions.title": "快速操作",
  "dashboard.quick_actions.subtitle": "常用任务帮助您快速开始",
  "dashboard.quick_actions.manage_customers": "管理客户",
  "dashboard.quick_actions.view_products": "查看产品",
  "dashboard.quick_actions.create_contract": "创建销售合同",
  "dashboard.quick_actions.update_profile": "更新公司资料",

  // Dashboard System Status
  "dashboard.system_status.title": "系统状态",
  "dashboard.system_status.subtitle": "您的ERP系统设置进度",
  "dashboard.system_status.company_profile": "公司资料",
  "dashboard.system_status.customer_database": "客户数据库",
  "dashboard.system_status.product_catalog": "产品目录",
  "dashboard.system_status.first_contract": "首个销售合同",
  "dashboard.system_status.inventory_setup": "库存设置",
  "dashboard.system_status.complete": "完成",
  "dashboard.system_status.active": "活跃",
  "dashboard.system_status.ready": "就绪",
  "dashboard.system_status.pending": "待处理",

  // Dashboard Getting Started
  "dashboard.getting_started.title": "🚀 快速开始",
  "dashboard.getting_started.subtitle": "完成这些步骤以充分利用您的制造业ERP系统",
  "dashboard.getting_started.step1.title": "1. 公司设置",
  "dashboard.getting_started.step1.desc": "您的公司资料已完成，可以开始业务。",
  "dashboard.getting_started.step2.title": "2. 创建您的第一个合同",
  "dashboard.getting_started.step2.desc": "通过创建您的第一个销售合同开始创收。",
  "dashboard.getting_started.step2.action": "创建合同",
  "dashboard.getting_started.step3.title": "3. 设置库存",
  "dashboard.getting_started.step3.desc": "跟踪您的原材料和成品库存。",
  "dashboard.getting_started.step3.action": "设置库存",
  "sample.title": "示例数据",
  "sample.desc": "此工作区内置了贴近真实的示例数据，便于理解各模块之间的关联。",
  "sample.reset": "重置示例数据",
  "sample.clear": "清空示例数据",
  "alert.db.title": "数据库未配置",
  "alert.db.desc":
    "请设置 DATABASE_URL（Neon Postgres）。应用将在首次加载时安全地自动迁移并种子最小数据。你也可以手动执行 scripts/sql/001_init.sql 和 002_seed.sql。",
  "alert.prep.title": "正在准备工作区",
  "alert.prep.desc": "正在初始化数据库结构，请稍后刷新。",

  // Common fields/actions
  "field.code": "编码",
  "field.name": "名称",
  "field.spec": "规格",
  "field.moq": "起订量",
  "field.inStock": "现货",
  "field.contact": "联系人",
  "field.incoterm": "贸易术语",
  "field.paymentTerm": "付款条款",
  "field.address": "地址",
  "field.sku": "SKU",
  "field.unit": "单位",
  "field.hsCode": "HS 编码",
  "field.origin": "原产地",
  "field.packaging": "包装",
  "field.currency": "币种",
  "field.number": "编号",
  "field.customer": "客户",
  "field.supplier": "供应商",
  "field.product": "产品",
  "field.qty": "数量",
  "field.price": "单价",
  "field.total": "合计",
  "field.woNumber": "工单号",
  "field.salesContract": "销售合同",
  "field.location": "库位",
  "field.note": "备注",
  "field.reference": "参考",
  "field.declarationNo": "报关单号",
  "field.amount": "金额",
  "field.received": "已收",
  "field.paid": "已付",
  "field.invoiceNo": "发票号",
  "table.actions": "操作",
  "table.noData": "暂无数据",
  "action.add": "添加",
  "action.addItem": "添加明细",
  "action.remove": "移除",
  "action.delete": "删除",
  "action.create": "创建",
  "action.createContract": "创建合同",
  "action.createPO": "创建采购单",
  "action.createWO": "创建工单",
  "action.addInbound": "添加入库",
  "action.addOutbound": "添加出库",
  "action.createDeclaration": "创建报关单",
  "action.submit": "提交",
  "action.createAR": "创建应收",
  "action.createAP": "创建应付",
  "cta.open": "进入",

  // Basic Info
  "basic.samples.title": "样品中心",
  "basic.samples.desc": "编码、名称、规格、起订量、是否现货。",
  "basic.customers.title": "客户",
  "basic.customers.desc": "客户档案与贸易条款。",
  "basic.suppliers.title": "供应商",
  "basic.suppliers.desc": "合格供应商与联系方式。",
  "basic.products.title": "产品/SKU",
  "basic.products.desc": "单位、HS 编码、原产地、包装。",

  // Contracts
  "contracts.sales.title": "销售合同",
  "contracts.sales.desc": "根据产品与客户创建销售合同。",
  "contracts.sales.empty": "暂无销售合同。",
  "contracts.purchase.title": "采购合同",
  "contracts.purchase.desc": "向供应商下达采购订单。",
  "contracts.purchase.empty": "暂无采购合同。",

  // Production
  "production.title": "生产工单",
  "production.desc": "跟踪工序进度与路由。",
  "production.empty": "暂无工单。",

  // Inventory
  "inventory.inbound.title": "入库（GRN）",
  "inventory.inbound.desc": "收货并入库。",
  "inventory.outbound.title": "出库（GDN）",
  "inventory.outbound.desc": "发货减少库存。",
  "inventory.stock.title": "库存",
  "inventory.stock.desc": "当前批次与库存汇总。",
  "inventory.stock.empty": "暂无库存批次。",
  "inventory.txns.title": "库存交易",
  "inventory.txns.desc": "出库时应用先进先出（FIFO）。",
  "inventory.txns.empty": "暂无交易记录。",

  // Export
  "export.title": "出口报关",
  "export.desc": "校验 HS 编码并跟踪报关状态。",
  "export.empty": "暂无报关单。",

  // Finance
  "finance.title": "财务会计",
  "finance.description": "管理发票、付款和财务报表",
  "finance.summary.totalAR": "应收总额",
  "finance.summary.outstandingAR": "未收应收",
  "finance.summary.totalAP": "应付总额",
  "finance.summary.netCashFlow": "净现金流",

  // Enhanced KPI Dashboard
  "finance.kpis.coreMetrics": "核心财务指标",
  "finance.kpis.totalRevenue": "总收入（年度）",
  "finance.kpis.totalExpenses": "总支出（年度）",
  "finance.kpis.profitLoss": "损益（年度）",
  "finance.kpis.netCashFlow": "净现金流",
  "finance.kpis.overdueIntelligence": "逾期智能分析",
  "finance.kpis.overdueAR": "逾期应收",
  "finance.kpis.overdueAP": "逾期应付",
  "finance.kpis.manufacturingIntelligence": "制造业智能分析",
  "finance.kpis.contractProfitability": "合同盈利能力",
  "finance.kpis.avgCollectionDays": "平均收款天数",
  "finance.kpis.manufacturingMargin": "制造业利润率",
  "finance.kpis.activeContracts": "活跃合同",

  // Additional Finance KPIs - Customer Payment Health (Chinese)
  "finance.kpis.customerPaymentHealth": "客户付款健康度",
  "finance.kpis.paymentPerformance": "付款表现",
  "finance.kpis.cashFlowPerformance": "现金流表现",
  "finance.kpis.cashReceived": "已收现金",
  "finance.kpis.mtd": "月度累计",
  "finance.kpis.actualCashCollected": "实际收到现金",
  "finance.kpis.pendingReceivables": "待收应收款",
  "finance.kpis.invoices": "发票",
  "finance.kpis.allInvoicesPaid": "所有发票已付清",
  "finance.kpis.collectionRate": "收款率",
  "finance.kpis.cashRevenue": "现金/收入",
  "finance.kpis.excellentCollectionEfficiency": "优秀的收款效率",
  "finance.kpis.overdueInvoices": "逾期发票",
  "finance.kpis.overdue": "逾期",
  "finance.kpis.excellentNothingOverdue": "优秀 - 无逾期",
  "finance.kpis.businessPerformance": "业务表现",
  "finance.kpis.totalContracts": "合同总数",

  // Enhanced AR/AP
  "finance.ar.title": "应收账款",
  "finance.ar.description": "跟踪应收发票和账龄，集成合同管理",
  "finance.ar.invoiceNumber": "发票号码",
  "finance.ar.customer": "客户",
  "finance.ar.salesContract": "销售合同",
  "finance.ar.amount": "金额",
  "finance.ar.received": "已收款",
  "finance.ar.currency": "货币",
  "finance.ar.status": "状态",
  "finance.ar.invoiceDate": "发票日期",
  "finance.ar.dueDate": "到期日期",
  "finance.ar.paymentTerms": "付款条件",
  "finance.ar.aging": "账龄",
  "finance.ar.contract": "合同",
  "finance.ar.createInvoice": "创建应收发票",
  "finance.ar.noInvoices": "未找到应收发票",

  // ✅ MISSING AR KEYS: Add missing Chinese translations
  "finance.ar.editInvoice": "编辑发票",
  "finance.ar.invoiceAmount": "发票金额",
  "finance.ar.totalInvoiceValue": "发票总价值",
  "finance.ar.amountReceived": "已收金额",
  "finance.ar.paymentsReceived": "已收付款",
  "finance.ar.outstandingBalance": "未收余额",
  "finance.ar.outstandingBalanceDesc": "仍欠金额",
  "finance.ar.sinceInvoiceDate": "自发票日期起",
  "finance.ar.invoiceInformation": "发票信息",
  "finance.ar.notes": "备注",
  "finance.ar.customerInformation": "客户信息",
  "finance.ar.customerName": "客户名称",
  "finance.ar.email": "邮箱",
  "finance.ar.phone": "电话",
  "finance.ar.address": "地址",
  "finance.ar.viewCustomerDetails": "查看客户详情",
  "finance.ar.customerNotAvailable": "客户信息不可用",
  "finance.ar.linkedSalesContract": "关联销售合同",
  "finance.ar.contractDescription": "此发票关联销售合同",
  "finance.ar.contractNumber": "合同号",
  "finance.ar.contractTitle": "合同标题",
  "finance.ar.contractValue": "合同价值",
  "finance.ar.contractItems": "合同项目",
  "finance.ar.product": "产品",
  "finance.ar.sku": "SKU",
  "finance.ar.quantity": "数量",
  "finance.ar.unitPrice": "单价",
  "finance.ar.lineTotal": "行总计",
  "finance.ar.unknownProduct": "未知产品",

  // AR Form Placeholders and Messages (Chinese)
  "finance.ar.searchPlaceholder": "搜索销售合同...",
  "finance.ar.customerPlaceholder": "选择客户...",
  "finance.ar.contractSelected": "合同已选择",
  "finance.ar.contractTotal": "合同总额",

  // AR Edit Page - Navigation and Actions (Chinese)
  "finance.ar.backToInvoice": "返回发票",
  "finance.ar.editTitle": "编辑应收发票",
  "finance.ar.editDescription": "更新发票详情和付款信息",
  "finance.ar.cancel": "取消",
  "finance.ar.saveChanges": "保存更改",

  // AR Edit Page - Form Sections (Chinese)
  "finance.ar.basicInvoiceDetails": "基本发票详情",
  "finance.ar.financialInformation": "财务信息",
  "finance.ar.invoiceAmountsAndCurrency": "发票金额和货币",
  "finance.ar.customerAndContract": "客户和合同",
  "finance.ar.selectCustomerAndContract": "选择客户和合同",
  "finance.ar.selectedContract": "已选合同",
  "finance.ar.additionalInformation": "附加信息",
  "finance.ar.optionalNotes": "可选备注",

  "finance.ap.title": "应付账款",
  "finance.ap.description": "跟踪应付发票和付款，集成合同管理",
  "finance.ap.invoiceNumber": "发票号码",
  "finance.ap.supplier": "供应商",
  "finance.ap.purchaseContract": "采购合同",
  "finance.ap.amount": "金额",
  "finance.ap.paid": "已付款",
  "finance.ap.currency": "货币",
  "finance.ap.status": "状态",
  "finance.ap.invoiceDate": "发票日期",
  "finance.ap.dueDate": "到期日期",
  "finance.ap.paymentTerms": "付款条件",
  "finance.ap.aging": "账龄",
  "finance.ap.contract": "合同",
  "finance.ap.createInvoice": "创建应付发票",
  "finance.ap.noInvoices": "未找到应付发票",

  // ✅ MISSING AP KEYS: Add missing Chinese translations
  "finance.ap.editInvoice": "编辑发票",
  "finance.ap.amountPaid": "已付金额",
  "finance.ap.invoiceAmount": "发票金额",
  "finance.ap.invoiceInformation": "发票信息",
  "finance.ap.supplierInformation": "供应商信息",
  "finance.ap.linkedPurchaseContract": "关联采购合同",
  "finance.ap.contractItems": "合同项目",
  "finance.ap.product": "产品",
  "finance.ap.sku": "SKU",
  "finance.ap.quantity": "数量",
  "finance.ap.unitPrice": "单价",
  "finance.ap.lineTotal": "行总计",
  "finance.ap.outstandingBalance": "未付余额",
  "finance.ap.paymentStatus": "付款状态",

  // AP Form Placeholders and Messages (Chinese)
  "finance.ap.searchPlaceholder": "搜索采购合同...",
  "finance.ap.supplierPlaceholder": "选择供应商...",

  // AP View Page - Action Buttons and Labels (Chinese)
  "finance.ap.backToFinance": "返回财务",
  "finance.ap.generatePDF": "生成PDF",
  "finance.ap.viewFullContract": "查看完整合同",
  "finance.ap.invoiceTitle": "应付发票",
  "finance.ap.invoiceDetailsSubtitle": "应付账款发票详情",
  "finance.ap.balanceDue": "应付余额",
  "finance.ap.outstandingBalanceDesc": "未付余额",
  "finance.ap.aging": "账龄",
  "finance.ap.sinceInvoiceDate": "自发票日期起",
  "finance.ap.supplierName": "供应商名称",
  "finance.ap.viewSupplierDetails": "查看供应商详情",

  // AP Edit Page - Navigation and Actions (Chinese)
  "finance.ap.backToInvoice": "返回发票",
  "finance.ap.editTitle": "编辑应付发票",
  "finance.ap.editDescription": "更新发票详情和付款信息",
  "finance.ap.cancel": "取消",
  "finance.ap.saveChanges": "保存更改",

  // AP Edit Page - Form Sections (Chinese)
  "finance.ap.basicInvoiceDetails": "基本发票详情",
  "finance.ap.financialInformation": "财务信息",
  "finance.ap.invoiceAmountsAndCurrency": "发票金额和货币",
  "finance.ap.supplierAndContract": "供应商和合同",
  "finance.ap.selectSupplierAndContract": "选择供应商和合同",
  "finance.ap.selectedContract": "已选合同",
  "finance.ap.additionalInformation": "附加信息",
  "finance.ap.optionalNotes": "可选备注",
  "finance.ap.enterNotes": "输入附加备注或评论...",

  // Additional AP Labels (Chinese)
  "finance.ap.invoiceInformationTitle": "发票信息",

  // AP View Page - Additional Labels (Chinese)
  "finance.ap.totalInvoiceValue": "发票总价值",
  "finance.ap.paymentsMade": "已付款项",
  "finance.ap.invoiceNumber": "发票号码",
  "finance.ap.status": "状态",
  "finance.ap.invoiceDate": "发票日期",
  "finance.ap.dueDate": "到期日期",
  "finance.ap.paymentTerms": "付款条件",
  "finance.ap.currency": "货币",
  "finance.ap.email": "邮箱",
  "finance.ap.address": "地址",
  "finance.ap.linkedPurchaseContract": "关联采购合同",
  "finance.ap.contractDescription": "此发票关联采购合同",
  "finance.ap.contractNumber": "合同号",
  "finance.ap.contractTitle": "合同标题",
  "finance.ap.contractValue": "合同价值",

  // Manufacturing Payment Terms
  "finance.paymentTerms.tt": "TT（电汇）",
  "finance.paymentTerms.dp": "DP（付款交单）",
  "finance.paymentTerms.lc": "LC（信用证）",
  "finance.paymentTerms.deposit": "定金（预付款）",
  "finance.paymentTerms.depositTT": "30%定金 + 70%电汇",
  "finance.paymentTerms.depositLC": "50%定金 + 50%信用证",

  // Manufacturing Invoice Statuses
  "finance.status.depositReceived": "已收定金",
  "finance.status.partialPaid": "部分付款",
  "finance.status.depositPaid": "已付定金",
  "finance.status.paid": "已付款",

  // Additional AR Labels (Chinese)
  "finance.ar.invoiceInformationTitle": "发票信息",

  // AR View Page - Action Buttons (Chinese)
  "finance.ar.backToFinance": "返回财务",
  "finance.ar.generatePDF": "生成PDF",
  "finance.ar.viewFullContract": "查看完整合同",
  "finance.ar.invoiceTitle": "应收发票",
  "finance.ar.title": "应收账款",
  "finance.ar.desc": "跟踪应收与账龄。",
  "finance.ar.empty": "暂无应收发票。",
  "finance.ap.title": "应付账款",
  "finance.ap.desc": "跟踪应付与付款。",
  "finance.ap.empty": "暂无应付发票。",

  // Docs
  "docs.plan.title": "参考脑图",
  "docs.plan.desc": "用于指导实施的中文原稿与英文译稿。",

  // 模块卡片
  "module.master.title": "主数据",
  "module.master.desc": "样品、客户、供应商、产品及贸易条款。",
  "module.contracts.title": "合同",
  "module.contracts.desc": "基于 SKU 创建销售合同与采购订单。",
  "module.production.title": "生产",
  "module.production.desc": "生成工单并跟踪工序与质检。",
  "module.inventory.title": "库存",
  "module.inventory.desc": "入库/出库、先进先出及当前库位。",
  "module.export.title": "出口",
  "module.export.desc": "创建报关单并校验 HS 编码。",
  "module.export.declarations": "报关单",
  "module.finance.title": "财务",
  "module.finance.desc": "跟踪应收与应付及基础账龄。",

  // Landing Page
  "landing.login": "登录",
  "landing.getStarted": "开始使用",
  "landing.learnMore": "了解更多",
  "landing.badge": "500+ 出口制造商信赖之选",
  "landing.hero.title": "一体化纺织制造与出口 ERP",
  "landing.hero.subtitle": "从生产到出口，简化您的纺织制造业务流程。在一个平台上管理客户、产品、质量控制和国际贸易合规。",
  "landing.features.noCredit": "无需信用卡",
  "landing.features.freeTrial": "30天免费试用",
  "landing.features.quickSetup": "几分钟完成设置",
  "landing.features.title": "出口制造所需的一切功能",
  "landing.features.subtitle": "从原材料到国际运输，管理您的整个制造工作流程",
  "landing.features.crm.title": "客户与供应商管理",
  "landing.features.crm.description": "全面的客户关系管理系统，管理国际客户和供应商的联系信息、付款条款和贸易历史。",
  "landing.features.inventory.title": "产品目录与库存",
  "landing.features.inventory.description": "详细的产品管理，包括SKU、规格、质量标准和实时库存跟踪。",
  "landing.features.production.title": "生产管理",
  "landing.features.production.description": "工单管理、生产调度，从裁剪到包装的实时跟踪。",
  "landing.features.quality.title": "质量控制",
  "landing.features.quality.description": "集成质量检验、缺陷跟踪和出口标准合规管理。",
  "landing.features.export.title": "出口文档",
  "landing.features.export.description": "自动化出口报关、运输文件和国际贸易合规管理。",
  "landing.features.analytics.title": "分析与报告",
  "landing.features.analytics.description": "实时仪表板、生产分析和全面的业务洞察报告。",
  "landing.benefits.title": "为什么选择我们的制造 ERP？",
  "landing.benefits.subtitle": "专为出口导向的纺织制造商打造",
  "landing.benefits.speed.title": "提升 50% 运营效率",
  "landing.benefits.speed.description": "简化的工作流程减少手工作业，加速生产周期",
  "landing.benefits.compliance.title": "100% 合规保障",
  "landing.benefits.compliance.description": "内置出口合规功能，确保满足所有国际贸易要求",
  "landing.benefits.global.title": "全球化就绪",
  "landing.benefits.global.description": "多币种、多语言支持，满足国际业务运营需求",

  // Landing Page (Hero mock labels)
  "landing.hero.mock.last30days": "近30天",
  "landing.hero.mock.onTimeShipments": "准时发货率",

  "landing.cta.title": "准备好转型您的制造业务了吗？",
  "landing.cta.subtitle": "加入数百家已通过我们的 ERP 解决方案简化运营的制造商",
  "landing.cta.button": "立即开始免费试用",
  "landing.cta.features": "无需信用卡 • 30天免费试用 • 几分钟完成设置",
  "landing.footer.copyright": "© 2024 FC-CHINA. 为全球出口制造商而建。",

  // Dashboard Page
  "dashboard.loading": "正在加载仪表板...",
  "dashboard.error.title": "仪表板错误",
  "dashboard.error.description": "这可能表示您需要完成公司资料设置。",
  "dashboard.error.retry": "重试",
  "dashboard.welcome": "欢迎回来",
  "dashboard.subtitle": "这是您今天制造业务的最新情况。",
  "dashboard.stats.customers.title": "客户总数",
  "dashboard.stats.customers.description": "活跃客户账户",
  "dashboard.stats.products.title": "产品",
  "dashboard.stats.products.description": "目录中的产品",
  "dashboard.stats.suppliers.title": "供应商",
  "dashboard.stats.suppliers.description": "活跃供应商",
  "dashboard.stats.contracts.title": "活跃合同",
  "dashboard.stats.contracts.description": "销售和采购合同",

  // Common UI Elements
  "common.loading": "加载中...",
  "common.error": "错误",
  "common.success": "成功",
  "common.cancel": "取消",
  "common.save": "保存",
  "common.delete": "删除",
  "common.edit": "编辑",
  "common.view": "查看",
  "common.add": "添加",
  "common.create": "创建",
  "common.update": "更新",
  "common.search": "搜索",
  "common.filter": "筛选",
  "common.actions": "操作",
  "common.status": "状态",
  "common.name": "名称",
  "common.backTo": "返回",
  "common.description": "描述",
  "common.date": "日期",
  "common.created": "创建时间",
  "common.updated": "更新时间",
  "common.active": "活跃",
  "common.inactive": "非活跃",
  "common.yes": "是",
  "common.no": "否",
  "common.confirm": "确认",
  "common.close": "关闭",
  "common.retry": "重试",
  "common.optional": "可选",
  "common.of": "共",
  "common.na": "不适用",

  // ✅ DASHBOARD TRANSLATIONS: Missing keys for Dashboard page (Chinese)
  "common.welcome_back": "欢迎回来",
  "common.ready_to_manage_textile_export_operations": "准备管理您的纺织品出口业务",
  "common.company": "公司",
  "common.financial_performance": "财务表现",
  "common.total_revenue": "总收入",
  "common.export_sales_revenue": "出口销售收入",
  "common.profit_margin": "利润率",
  "common.manufacturing_margin": "制造利润率",
  "common.pending_receivables": "待收账款",
  "common.outstanding_invoices": "未结发票",
  "common.active_customers": "活跃客户",
  "common.business_relationships": "业务关系",
  "common.production_operations": "生产运营",
  "common.active_work_orders": "活跃工单",
  "common.in_production": "生产中",
  "common.production_efficiency": "生产效率",
  "common.completion_rate": "完成率",
  "common.active_shipments": "活跃发货",
  "common.in_transit": "运输中",
  "common.ontime_delivery": "准时交付",
  "common.delivery_performance": "交付表现",
  "common.quality_control": "质量控制",
  "common.manufacturing_quality_metrics": "制造质量指标",
  "common.quality_pass_rate": "质量通过率",
  "common.pending_inspections": "待检验",
  "common.quality_score": "质量评分",
  "common.export_operations": "出口业务",
  "common.international_trade_metrics": "国际贸易指标",
  "common.export_declarations": "出口报关单",
  "common.active_sales_contracts": "活跃销售合同",
  "common.product_catalog": "产品目录",
  "common.quick_access": "快速访问",
  "common.navigate_to_key_manufacturing_modules": "导航到关键制造模块",
  "common.reports": "报表",
  "common.production": "生产",
  "common.quality": "质量",
  "common.shipping": "发货",

  // Status Values
  "status.active": "活跃",
  "status.inactive": "非活跃",
  "status.draft": "草稿",
  "status.approved": "已审批",
  "status.pending": "待处理",
  "status.completed": "已完成",
  "status.cancelled": "已取消",
  "status.done": "完成",

  // Table Headers
  "header.wo": "工单",
  "header.operations": "工序",
  "header.type": "类型",
  "header.time": "时间",
  "header.lot": "批次",

  // Loading States
  "loading.inventory": "正在加载库存...",
  "loading.try_again": "重试",

  // Form Fields
  "field.name": "名称",
  "field.email": "邮箱",
  "field.phone": "电话",
  "field.address": "地址",
  "field.company": "公司",
  "field.contact": "联系人",
  "field.qty": "数量",
  "field.price": "价格",
  "field.total": "总计",
  "field.currency": "货币",
  "field.status": "状态",
  "field.type": "类型",
  "field.date": "日期",
  "field.notes": "备注",
  "field.product": "产品",
  "field.customer": "客户",
  "field.supplier": "供应商",
  "field.contract": "合同",
  "field.template": "模板",

  // Customers Page
  "customers.title": "客户管理",
  "customers.subtitle": "管理您的客户数据库和关系",
  "customers.add": "添加客户",
  "customers.add.title": "添加新客户",
  "customers.add.description": "为您的业务创建新的客户记录。",
  "customers.edit.title": "编辑客户",
  "customers.edit.description": "更新客户信息。",
  "customers.delete.title": "删除客户",
  "customers.delete.description": "您确定要删除此客户吗？此操作无法撤销。",
  "customers.form.company_name": "公司名称",
  "customers.form.contact_name": "联系人",
  "customers.form.contact_phone": "电话号码",
  "customers.form.contact_email": "邮箱地址",
  "customers.form.address": "地址",
  "customers.form.tax_id": "税号",
  "customers.form.bank": "银行信息",
  "customers.form.incoterm": "贸易条款",
  "customers.form.payment_term": "付款条件",
  "customers.form.status": "状态",
  "customers.table.company_name": "公司名称",
  "customers.table.contact_person": "联系人",
  "customers.table.phone": "电话",
  "customers.table.email": "邮箱",
  "customers.table.address": "地址",
  "customers.table.incoterm": "贸易条款",
  "customers.table.payment_terms": "付款条件",
  "customers.table.status": "状态",
  "customers.table.actions": "操作",
  "customers.success.created": "客户创建成功！",
  "customers.success.updated": "客户更新成功！",
  "customers.success.deleted": "客户删除成功！",
  "customers.error.create": "创建客户失败",
  "customers.error.update": "更新客户失败",
  "customers.error.delete": "删除客户失败",
  "customers.empty": "未找到客户",
  "customers.empty.description": "添加您的第一个客户开始使用。",

  // ✅ CUSTOMER VIEW & EDIT TRANSLATIONS: Missing keys for view and edit pages
  "customers.view.back_to_customers": "返回客户列表",
  "customers.view.edit_customer": "编辑客户",
  "customers.view.contact_information": "联系信息",
  "customers.view.details": "详细信息",
  "customers.view.address": "地址",
  "customers.view.incoterm": "贸易条款",
  "customers.view.payment_term": "付款条件",
  "customers.view.status": "状态",
  "customers.view.related_samples": "相关样品",
  "customers.view.related_samples_description": "与此客户相关的样品（包括入库和出库）",
  "customers.view.search_samples": "按名称、代码、方向、状态搜索样品...",
  "customers.view.no_samples_found": "未找到样品",
  "customers.view.no_samples_found_description": "没有样品符合您的搜索条件。请尝试调整搜索词。",
  "customers.view.no_related_samples": "无相关样品",
  "customers.view.no_related_samples_description": "尚未有样品与此客户关联。",
  "customers.view.sales_contracts": "销售合同",
  "customers.view.sales_contracts_description": "此客户的销售合同。",
  "customers.view.contract": "合同",
  "customers.view.date": "日期",
  "customers.view.items": "项目",
  "customers.view.actions": "操作",
  "customers.view.no_sales_contracts": "无销售合同",
  "customers.view.no_sales_contracts_description": "尚未为此客户创建销售合同。",
  "customers.view.view_contract": "查看合同",
  "customers.view.view": "查看",
  "customers.view.search_contracts": "按合同号、状态、日期搜索...",
  "customers.view.no_contracts_found": "未找到合同",
  "customers.view.no_contracts_found_description": "没有合同符合您的搜索条件。请尝试调整搜索词。",
  "customers.edit.page_title": "编辑客户",
  "customers.edit.page_description": "更新 {customerName} 的详细信息。",
  "customers.form.customer_name": "客户名称",
  "customers.form.contact_name": "联系人",
  "customers.form.contact_email": "联系邮箱",
  "customers.form.contact_phone": "联系电话",
  "customers.form.address": "地址",
  "customers.form.incoterm": "贸易条款",
  "customers.form.payment_term": "付款条件",
  "customers.success.updated_toast": "客户更新成功。",
  "customers.error.update_toast": "客户更新失败。",

  // Products Page
  "products.title": "产品管理",
  "products.subtitle": "管理您的产品目录和库存",
  "products.add": "添加产品",
  "products.add.title": "添加新产品",
  "products.add.description": "创建包含规格和定价信息的新产品记录。",
  "products.edit.title": "编辑产品",
  "products.edit.description": "更新产品信息和规格。",
  "products.delete.title": "删除产品",
  "products.delete.description": "您确定要删除此产品吗？此操作无法撤销。",
  "products.form.name": "产品名称",
  "products.form.sku": "SKU",
  "products.form.unit": "单位",
  "products.form.hs_code": "HS编码",
  "products.form.origin": "原产地",
  "products.form.package": "包装",
  "products.form.status": "状态",

  // ✅ PRICING FIELDS: Enhanced product pricing system
  "products.form.pricing_information": "价格信息",
  "products.form.base_price": "基础价格",
  "products.form.cost_price": "成本价格",
  "products.form.margin_percentage": "利润率 %",
  "products.form.currency": "货币",
  "products.table.name": "产品名称",
  "products.table.sku": "SKU",
  "products.table.unit": "单位",
  "products.table.hs_code": "HS编码",
  "products.table.origin": "原产地",
  "products.table.package": "包装",
  "products.table.price": "价格",
  "products.table.status": "状态",
  "products.table.actions": "操作",
  "products.success.created": "产品创建成功！",
  "products.success.updated": "产品更新成功！",
  "products.success.deleted": "产品删除成功！",
  "products.error.create": "创建产品失败",
  "products.error.update": "更新产品失败",
  "products.error.delete": "删除产品失败",
  "products.empty": "未找到产品",
  "products.empty.description": "添加您的第一个产品开始使用。",

  // Product View Page
  "products.product": "产品",
  "products.details": "详细信息",
  "products.name": "名称",
  "products.sku": "SKU",
  "products.unit": "单位",
  "products.pricing": "定价",
  "products.basePrice": "基础价格",
  "products.costPrice": "成本价格",
  "products.margin": "利润率",
  "products.specifications": "规格",
  "products.hsCode": "HS编码",
  "products.origin": "原产地",
  "products.package": "包装",
  "products.billOfMaterials": "物料清单",

  // BOM Management
  "bom.title": "物料清单",
  "bom.description": "生产所需材料",
  "bom.addMaterial": "添加材料",
  "bom.addMaterialDialog.title": "添加材料到BOM",
  "bom.addMaterialDialog.description": "选择原材料并指定生产一个单位所需的数量",
  "bom.editMaterialDialog.title": "编辑BOM项目",
  "bom.editMaterialDialog.description": "更新材料需求",
  "bom.table.material": "材料",
  "bom.table.sku": "SKU",
  "bom.table.qtyRequired": "所需数量",
  "bom.table.unit": "单位",
  "bom.table.wasteFactor": "损耗系数",
  "bom.table.cost": "成本",
  "bom.table.status": "状态",
  "bom.table.actions": "操作",
  "bom.form.rawMaterial": "原材料",
  "bom.form.qtyRequired": "所需数量",
  "bom.form.unit": "单位",
  "bom.form.wasteFactor": "损耗系数",
  "bom.form.wasteFactorDescription": "材料损耗百分比（例如：0.05 = 5%）",
  "bom.empty": "BOM中还没有材料。点击\"添加材料\"开始。",
  "bom.noSupplier": "无供应商",
  "bom.unknown": "未知",
  "bom.selectMaterial": "选择原材料",
  "bom.noMaterialsAvailable": "无可用原材料",
  "bom.adding": "添加中...",
  "bom.updating": "更新中...",
  "bom.updateMaterial": "更新材料",
  "products.view.title": "产品详情",
  "products.view.description": "查看产品信息（只读）。",

  // Sales Contracts Page
  "sales_contracts.title": "销售合同",
  "sales_contracts.subtitle": "管理您公司的销售合同。",
  "sales_contracts.add": "添加合同",
  "sales_contracts.add.title": "创建销售合同",
  "sales_contracts.add.description": "为您的客户创建新的销售合同。",
  "sales_contracts.edit.title": "编辑销售合同",
  "sales_contracts.edit.description": "更新销售合同信息。",
  "sales_contracts.delete.title": "您确定吗？",
  "sales_contracts.delete.description": "这将永久删除该合同。此操作无法撤销。",
  "sales_contracts.form.number": "合同编号",
  "sales_contracts.form.customer": "客户",
  "sales_contracts.form.template": "模板",
  "sales_contracts.form.currency": "货币",
  "sales_contracts.form.items": "明细",
  "sales_contracts.table.contract_number": "合同编号",
  "sales_contracts.table.number": "合同编号",
  "sales_contracts.table.customer": "客户",
  "sales_contracts.table.date": "日期",
  "sales_contracts.table.currency": "货币",
  "sales_contracts.table.items": "明细",
  "sales_contracts.table.total": "总计",
  "sales_contracts.table.status": "状态",
  "sales_contracts.table.created": "创建时间",
  "sales_contracts.table.actions": "操作",
  "sales_contracts.search_placeholder": "搜索合同...",
  "sales_contracts.success.created": "销售合同创建成功！",
  "sales_contracts.success.updated": "销售合同更新成功！",
  "sales_contracts.success.deleted": "合同删除成功。",
  "sales_contracts.error.create": "创建销售合同失败",
  "sales_contracts.error.update": "更新销售合同失败",
  "sales_contracts.error.delete": "删除合同失败。",
  "sales_contracts.empty": "未找到销售合同",
  "sales_contracts.empty.description": "创建您的第一个销售合同开始使用。",

  // Purchase Contracts Page
  "purchase_contracts.title": "采购合同",
  "purchase_contracts.subtitle": "管理您公司的采购合同。",
  "purchase_contracts.add": "添加合同",
  "purchase_contracts.add.title": "创建采购合同",
  "purchase_contracts.add.description": "与您的供应商创建新的采购合同。",
  "purchase_contracts.edit.title": "编辑采购合同",
  "purchase_contracts.edit.description": "更新采购合同信息。",
  "purchase_contracts.delete.title": "您确定吗？",
  "purchase_contracts.delete.description": "这将永久删除该合同。此操作无法撤销。",
  "purchase_contracts.form.number": "合同编号",
  "purchase_contracts.form.supplier": "供应商",
  "purchase_contracts.form.template": "模板",
  "purchase_contracts.form.currency": "货币",
  "purchase_contracts.form.items": "明细",
  "purchase_contracts.table.contract_number": "合同编号",
  "purchase_contracts.table.number": "合同编号",
  "purchase_contracts.table.supplier": "供应商",
  "purchase_contracts.table.date": "日期",
  "purchase_contracts.table.currency": "货币",
  "purchase_contracts.table.items": "明细",
  "purchase_contracts.table.total": "总计",
  "purchase_contracts.table.status": "状态",
  "purchase_contracts.table.created": "创建时间",
  "purchase_contracts.table.actions": "操作",
  "purchase_contracts.search_placeholder": "搜索合同...",
  "purchase_contracts.success.created": "采购合同创建成功！",
  "purchase_contracts.success.updated": "采购合同更新成功！",
  "purchase_contracts.success.deleted": "合同删除成功。",
  "purchase_contracts.error.create": "创建采购合同失败",
  "purchase_contracts.error.update": "更新采购合同失败",
  "purchase_contracts.error.delete": "删除合同失败。",
  "purchase_contracts.empty": "未找到采购合同",
  "purchase_contracts.empty.description": "创建您的第一个采购合同开始使用。",

  // Suppliers Page
  "suppliers.title": "供应商管理",
  "suppliers.subtitle": "管理您的供应商数据库和关系",
  "suppliers.add": "添加供应商",
  "suppliers.add.title": "添加新供应商",
  "suppliers.add.description": "创建包含联系信息和详细信息的新供应商记录。",
  "suppliers.edit.title": "编辑供应商",
  "suppliers.edit.description": "更新供应商信息。",
  "suppliers.delete.title": "删除供应商",
  "suppliers.delete.description": "您确定要删除此供应商吗？此操作无法撤销。",
  "suppliers.delete.confirmation": "这将永久删除供应商\"{name}\"并从我们的服务器中删除其数据。",
  "suppliers.form.name": "供应商名称",
  "suppliers.form.contact_name": "联系人",
  "suppliers.form.contact_phone": "电话号码",
  "suppliers.form.contact_email": "邮箱地址",
  "suppliers.form.address": "地址",
  "suppliers.form.bank": "银行详情",
  "suppliers.form.tax_id": "税务编号",
  "suppliers.form.status": "状态",
  "suppliers.table.company_name": "公司名称",
  "suppliers.table.name": "供应商名称",
  "suppliers.table.contact_person": "联系人",
  "suppliers.table.phone": "电话",
  "suppliers.table.email": "邮箱",
  "suppliers.table.address": "地址",
  "suppliers.table.bank": "银行详情",
  "suppliers.table.status": "状态",
  "suppliers.table.actions": "操作",
  "suppliers.success.created": "供应商创建成功！",
  "suppliers.success.updated": "供应商更新成功！",
  "suppliers.success.deleted": "供应商删除成功！",
  "suppliers.error.create": "创建供应商失败",
  "suppliers.error.update": "更新供应商失败",
  "suppliers.error.delete": "删除供应商失败",
  "suppliers.empty": "未找到供应商",
  "suppliers.empty.description": "添加您的第一个供应商开始使用。",
  "suppliers.view.subtitle": "查看供应商详情和相关采购合同",
  "suppliers.view.supplier_info": "供应商信息",
  "suppliers.view.supplier_info_desc": "基本供应商详情和联系信息",
  "suppliers.view.purchase_contracts": "采购合同",
  "suppliers.view.purchase_contracts_desc": "与此供应商相关的采购合同",
  "suppliers.view.no_contracts": "无采购合同",
  "suppliers.view.no_contracts_desc": "此供应商尚未有任何采购合同。",
  "suppliers.view.create_contract": "创建采购合同",
  "suppliers.view.view_all_contracts": "查看所有合同",

  // Samples Page - Main
  "samples.title": "样品管理",
  "samples.subtitle": "管理产品样品和审批流程",
  "samples.add": "新建样品",
  "samples.refresh": "刷新",
  "samples.loading": "正在加载样品...",
  "samples.found": "找到 {count} 个样品",

  // Dashboard Cards
  "samples.cards.outbound.title": "📤 出库样品",
  "samples.cards.outbound.description": "我们发送给客户",
  "samples.cards.inbound.title": "📥 入库样品",
  "samples.cards.inbound.description": "来自客户和供应商",
  "samples.cards.internal.title": "🏭 内部样品",
  "samples.cards.internal.description": "研发和测试",
  "samples.cards.quality.title": "🧪 质量流水线",
  "samples.cards.quality.description": "等待质检审批",

  // Table Headers
  "samples.table.sample": "样品",
  "samples.table.direction": "方向",
  "samples.table.purpose": "用途",
  "samples.table.relationship": "业务关系",
  "samples.table.product": "产品",
  "samples.table.status": "状态",
  "samples.table.priority": "优先级",
  "samples.table.created": "创建时间",
  "samples.table.actions": "操作",
  "samples.table.empty": "未找到样品。创建您的第一个样品开始使用。",

  // Actions
  "samples.actions.view": "查看",
  "samples.actions.edit": "编辑",
  "samples.actions.delete": "删除",
  "samples.actions.approve": "审批",
  "samples.actions.reject": "拒绝",

  // Sample Filters
  "samples.filters.search": "按名称、编码或备注搜索样品...",
  "samples.filters.status.all": "所有状态",
  "samples.filters.status.pending": "待审批",
  "samples.filters.status.approved": "已审批",
  "samples.filters.status.rejected": "已拒绝",
  "samples.filters.type.all": "所有类型",
  "samples.filters.type.development": "开发",
  "samples.filters.type.production": "生产",
  "samples.filters.type.quality": "质量",
  "samples.filters.type.prototype": "原型",
  "samples.filters.direction.all": "所有方向",
  "samples.filters.direction.outbound": "出库",
  "samples.filters.direction.inbound": "入库",
  "samples.filters.direction.internal": "内部",
  "samples.filters.advanced": "高级",
  "samples.filters.clear": "清除筛选",

  // Sample Delete
  "samples.delete.success.title": "样品已删除",
  "samples.delete.success.description": "样品 '{name}' 已成功删除",
  "samples.delete.error.title": "删除失败",
  "samples.delete.error.description": "删除样品失败，请重试。",
  "samples.delete.dialog.title": "删除样品",
  "samples.delete.dialog.description": "您确定要删除此样品吗？此操作无法撤销。",
  "samples.delete.dialog.warning": "此操作是永久性的，无法撤销。",
  "samples.delete.dialog.confirm": "删除样品",
  "samples.delete.dialog.deleting": "删除中...",

  // Sample Fields
  "samples.fields.code": "样品编码",
  "samples.fields.name": "样品名称",
  "samples.fields.date": "日期",
  "samples.fields.status": "状态",
  "samples.fields.priority": "优先级",
  "samples.fields.type": "样品类型",
  "samples.fields.direction": "方向",
  "samples.fields.purpose": "用途",
  "samples.fields.customer": "客户",
  "samples.fields.supplier": "供应商",
  "samples.fields.product": "产品",
  "samples.fields.quantity": "数量",
  "samples.fields.unit": "单位",
  "samples.fields.cost": "成本",
  "samples.fields.currency": "货币",
  "samples.fields.delivery_date": "交付日期",
  "samples.fields.specifications": "技术规格",
  "samples.fields.quality_requirements": "质量要求",
  "samples.fields.notes": "备注",

  // Sample Create
  "samples.create.title": "创建新样品",
  "samples.create.description": "添加新样品以跟踪客户请求和审批",
  "samples.create.backToSamples": "返回样品",

  // Card 1: Sample Classification
  "samples.create.classification.title": "样品分类",
  "samples.create.classification.description": "样品方向和基本信息",
  "samples.create.sampleDirection": "样品方向",
  "samples.create.sampleDirectionRequired": "样品方向 *",
  "samples.create.outbound": "📤 出库 - 我们发送给客户",
  "samples.create.inbound": "📥 入库 - 客户/供应商发送给我们",
  "samples.create.internal": "🏭 内部 - 内部测试/研发",
  "samples.create.sampleCode": "样品编码",
  "samples.create.sampleCodeRequired": "样品编码 *",
  "samples.create.sampleDate": "样品日期",
  "samples.create.sampleDateRequired": "样品日期 *",
  "samples.create.receivedDate": "接收日期",
  "samples.create.receivedDateRequired": "接收日期 *",
  "samples.create.sampleName": "样品名称",
  "samples.create.sampleNameRequired": "样品名称 *",
  "samples.create.sampleType": "样品类型",
  "samples.create.priority": "优先级",

  // Card 2: Product Information
  "samples.create.productInfo.title": "产品信息",
  "samples.create.productInfo.description": "产品详情和规格",
  "samples.create.product": "产品",
  "samples.create.productRequired": "产品 *",
  "samples.create.quantity": "数量",
  "samples.create.unit": "单位",
  "samples.create.technicalSpecs": "技术规格",
  "samples.create.qualityRequirements": "质量要求",

  // Card 3: Business Relations (Dynamic)
  "samples.create.customerDelivery": "客户交付",
  "samples.create.sampleSource": "样品来源",
  "samples.create.internalRelations": "内部关系",
  "samples.create.customerReceiving": "接收样品的客户",
  "samples.create.whoSentSample": "谁发送了这个样品给我们",
  "samples.create.internalDepartment": "内部部门/项目",
  "samples.create.customerRequired": "客户（接收样品）*",
  "samples.create.samplePurpose": "样品用途",
  "samples.create.deliveryInstructions": "交付说明",
  "samples.create.senderType": "样品发送方类型",
  "samples.create.senderTypeRequired": "样品发送方类型 *",
  "samples.create.customerSender": "客户（发送方）",
  "samples.create.customerSenderRequired": "客户（发送方）*",
  "samples.create.supplierSender": "供应商（发送方）",
  "samples.create.supplierSenderRequired": "供应商（发送方）*",
  "samples.create.sampleCondition": "样品状况和要求",
  "samples.create.internalPurpose": "内部用途",
  "samples.create.internalRequirements": "内部要求",

  // Card 4: Commercial Details
  "samples.create.commercialDetails.title": "商业详情",
  "samples.create.commercialDetails.description": "定价和交付信息",
  "samples.create.deliveryDate": "交付日期",
  "samples.create.sampleCost": "样品成本",
  "samples.create.currency": "货币",
  "samples.create.testingStatus": "测试状态",
  "samples.create.testingResults": "测试结果",
  "samples.create.quoteRequested": "已请求报价",
  "samples.create.quoteProvided": "已提供报价",
  "samples.create.targetCompletion": "目标完成日期",

  // Form Actions
  "samples.create.cancel": "取消",
  "samples.create.save": "创建样品",
  "samples.create.saving": "创建中...",

  // Dropdown Options
  "samples.create.options.development": "开发",
  "samples.create.options.production": "生产",
  "samples.create.options.quality": "质量",
  "samples.create.options.prototype": "原型",
  "samples.create.options.low": "低",
  "samples.create.options.normal": "正常",
  "samples.create.options.high": "高",
  "samples.create.options.urgent": "紧急",
  "samples.create.options.customer": "客户",
  "samples.create.options.supplier": "供应商",
  "samples.create.options.customerEvaluation": "客户评估",
  "samples.create.options.marketingDemo": "营销演示",
  "samples.create.options.tradeShow": "贸易展览",
  "samples.create.options.salesPresentation": "销售展示",
  "samples.create.options.manufacturingQuote": "制造报价请求",
  "samples.create.options.materialTesting": "材料测试",
  "samples.create.options.reverseEngineering": "逆向工程",
  "samples.create.options.qualityComparison": "质量对比",
  "samples.create.options.qualityControl": "质量控制",
  "samples.create.options.rAndD": "研发测试",
  "samples.create.options.processImprovement": "工艺改进",
  "samples.create.options.productDevelopment": "产品开发",
  "samples.create.options.notStarted": "未开始",
  "samples.create.options.inProgress": "进行中",
  "samples.create.options.completed": "已完成",
  "samples.create.options.failed": "失败",

  // Placeholders
  "samples.create.placeholders.inboundCode": "例如：IN-2024-001",
  "samples.create.placeholders.outboundCode": "例如：OUT-2024-001",
  "samples.create.placeholders.customerFabric": "例如：客户面料样品",
  "samples.create.placeholders.cottonFabric": "例如：棉质面料样品",
  "samples.create.placeholders.quantity": "例如：100",
  "samples.create.placeholders.unit": "例如：米、件",
  "samples.create.placeholders.techSpecs": "技术规格...",
  "samples.create.placeholders.qualityStandards": "质量标准...",
  "samples.create.placeholders.searchProducts": "搜索产品（出库必需）...",
  "samples.create.placeholders.searchProductsOptional": "搜索产品（可选）...",
  "samples.create.placeholders.searchCustomers": "搜索要发送样品的客户...",
  "samples.create.placeholders.searchCustomerSender": "搜索发送样品的客户...",
  "samples.create.placeholders.searchSupplierSender": "搜索发送样品的供应商...",
  "samples.create.placeholders.whySending": "我们为什么发送这个样品？",
  "samples.create.placeholders.whySent": "他们为什么发送这个样品？",
  "samples.create.placeholders.whoSent": "谁发送了这个样品给我们？",
  "samples.create.placeholders.internalPurpose": "内部测试目的",
  "samples.create.placeholders.deliveryInstructions": "特殊交付说明，客户要求...",
  "samples.create.placeholders.sampleCondition": "接收时样品状况，分析要求，客户规格...",
  "samples.create.placeholders.internalRequirements": "内部测试要求，项目详情，部门规格...",
  "samples.create.placeholders.sampleCost": "例如：150.00",
  "samples.create.placeholders.testingResults": "质量分析，材料属性，测试结果...",

  // Help Text
  "samples.create.help.outboundProduct": "📤 出库样品需要选择产品（采样我们自己的产品）",
  "samples.create.help.inboundProduct": "📥 产品选择是可选的（样品可能还不存在于我们的目录中）",

  // Success/Error Messages
  "samples.create.success.title": "样品已创建",
  "samples.create.success.description": "样品已成功创建",
  "samples.create.error.title": "创建失败",
  "samples.create.error.description": "创建样品失败，请重试。",

  // Sample View
  "samples.view.back": "返回样品",
  "samples.view.pending_request": "待处理请求",
  "samples.view.approved": "已审批",
  "samples.view.rejected": "已拒绝",
  "samples.view.edit": "编辑",
  "samples.view.sample_info": "样品信息",
  "samples.view.sample_type": "样品类型",
  "samples.view.priority": "优先级",
  "samples.view.sample_date": "样品日期",
  "samples.view.quantity": "数量",
  "samples.view.delivery_date": "交付日期",
  "samples.view.cost": "成本",
  "samples.view.relationships": "业务关系",
  "samples.view.customer": "客户",
  "samples.view.contact": "联系人",
  "samples.view.email": "邮箱",
  "samples.view.product": "产品",
  "samples.view.sku": "SKU",
  "samples.view.supplier": "供应商",
  "samples.view.specifications": "规格和备注",
  "samples.view.technical_specs": "技术规格",
  "samples.view.quality_requirements": "质量要求",
  "samples.view.notes": "备注",
  "samples.view.approval_history": "审批历史",
  "samples.view.created": "已创建",
  "samples.view.revised": "已修订",
  "samples.view.pending": "待审批",
  "samples.view.revision_required": "需要修订",
  "samples.view.by_system": "由系统",
  "samples.view.by_current_user": "由当前用户",
  "samples.view.sample_created": "样品已创建并提交审批",
  "samples.view.sample_processed": "样品已处理",
  "samples.view.metadata": "元数据",
  "samples.view.created_date": "创建时间",
  "samples.view.approved_by": "审批人",
  "samples.view.approved_date": "审批日期",

  // Approval History Status Labels
  "samples.view.status.created": "已创建",
  "samples.view.status.pending": "待审批",
  "samples.view.status.approved": "已审批",
  "samples.view.status.rejected": "已拒绝",
  "samples.view.status.revised": "已修订",
  "samples.view.status.revision_required": "需要修订",

  // Approval History Actions
  "samples.view.actions.sample_created": "样品已创建并提交审批",
  "samples.view.actions.sample_processed": "样品已处理",
  "samples.view.actions.sample_approved": "样品已审批",
  "samples.view.actions.sample_rejected": "样品已拒绝",
  "samples.view.actions.revision_requested": "要求修订",

  // User References
  "samples.view.by_system_on": "由系统于",
  "samples.view.by_current_user_on": "由当前用户于",
  "samples.view.by_user_on": "由 {user} 于",

  // Sample Edit
  "samples.edit.title": "编辑样品",
  "samples.edit.description": "更新样品信息和规格",
  "samples.edit.loading": "正在加载样品数据...",
  "samples.edit.success.title": "样品已更新",
  "samples.edit.success.description": "样品已成功更新",
  "samples.edit.error.title": "更新失败",
  "samples.edit.error.description": "更新样品失败，请重试。",
  "samples.edit.error.load": "加载样品数据失败，请重试。",
  "samples.edit.back": "返回样品",
  "samples.edit.cancel": "取消",
  "samples.edit.save": "更新样品",
  "samples.edit.saving": "更新中...",
  "samples.edit.validation.name": "样品名称为必填项",
  "samples.edit.code.readonly": "样品编码无法更改",
  "samples.edit.basic_info": "基本信息",
  "samples.edit.basic_info_desc": "更新基本样品信息",
  "samples.edit.sample_code": "样品编码",
  "samples.edit.sample_name": "样品名称",
  "samples.edit.sample_name_placeholder": "输入样品名称",
  "samples.edit.sample_type": "样品类型",
  "samples.edit.priority": "优先级",
  "samples.edit.relationships": "业务关系",
  "samples.edit.relationships_desc": "将此样品与客户、产品和供应商关联",
  "samples.edit.customer": "客户",
  "samples.edit.customer_placeholder": "搜索客户...",
  "samples.edit.product": "产品",
  "samples.edit.product_placeholder": "搜索产品...",
  "samples.edit.supplier": "供应商",
  "samples.edit.supplier_placeholder": "搜索供应商...",
  "samples.edit.specifications": "规格和详情",
  "samples.edit.specifications_desc": "添加技术规格和其他详情",
  "samples.edit.quantity": "数量",
  "samples.edit.unit": "单位",
  "samples.edit.cost": "成本",
  "samples.edit.currency": "货币",
  "samples.edit.delivery_date": "交付日期",
  "samples.edit.technical_specs": "技术规格",
  "samples.edit.technical_specs_placeholder": "输入技术规格...",
  "samples.edit.quality_requirements": "质量要求",
  "samples.edit.quality_requirements_placeholder": "输入质量要求...",
  "samples.edit.notes": "备注",
  "samples.edit.notes_placeholder": "输入其他备注...",

  // Search Dropdowns
  "samples.edit.search.no_results": "未找到结果",
  "samples.edit.search.add_new_customer": "添加新客户",
  "samples.edit.search.add_new_product": "添加新产品",
  "samples.edit.search.add_new_supplier": "添加新供应商",
  "samples.edit.search.loading": "加载中...",
  "samples.edit.search.type_to_search": "输入以搜索...",

  // Inventory Page
  "inventory.title": "库存管理",
  "inventory.subtitle": "管理库存水平、入库和出库操作",
  "inventory.tabs.inbound": "入库",
  "inventory.tabs.outbound": "出库",
  "inventory.tabs.stock": "库存",
  "inventory.tabs.transactions": "交易记录",
  "inventory.inbound.form.product": "产品",
  "inventory.inbound.form.qty": "数量",
  "inventory.inbound.form.location": "位置",
  "inventory.inbound.form.ref": "参考",
  "inventory.inbound.button": "收货",
  "inventory.outbound.form.product": "产品",
  "inventory.outbound.form.qty": "数量",
  "inventory.outbound.form.location": "位置",
  "inventory.outbound.form.ref": "参考",
  "inventory.outbound.button": "发货",
  "inventory.stock.loading": "正在加载库存...",
  "inventory.stock.error": "加载库存数据失败",
  "inventory.stock.retry": "重试",
  "inventory.stock.table.lot": "批次",
  "inventory.stock.table.location": "位置",

  // Additional inventory keys
  "inventory.inbound.title": "入库",
  "inventory.inbound.desc": "接收库存",
  "inventory.outbound.title": "出库",
  "inventory.outbound.desc": "发货库存",
  "inventory.stock.title": "库存",
  "inventory.stock.desc": "当前库存水平",
  "inventory.transactions.title": "交易记录",
  "inventory.transactions.desc": "库存移动历史",
  "field.location": "位置",
  "field.note": "备注",
  "field.reference": "参考",
  "action.addInbound": "收货",
  "action.addOutbound": "发货",

  // ✅ NEW: Comprehensive inventory transaction translations (Chinese)
  "inventory.transaction_forms": "交易表单",
  "inventory.transaction_history": "交易历史",
  "inventory.transaction_success": "交易成功",
  "inventory.transaction_error": "交易失败",
  "inventory.inbound": "入库",
  "inventory.outbound": "出库",
  "inventory.transfer": "调拨",
  "inventory.adjustment": "调整",
  "inventory.product": "产品",
  "inventory.quantity": "数量",
  "inventory.location": "位置",
  "inventory.source_location": "源位置",
  "inventory.destination_location": "目标位置",
  "inventory.adjustment_quantity": "调整数量",
  "inventory.reason_code": "原因代码",
  "inventory.notes": "备注",
  "inventory.reference": "参考",
  "inventory.status": "状态",
  "inventory.date": "日期",
  "inventory.type": "类型",
  "inventory.select_product": "选择产品",
  "inventory.select_location": "选择位置",
  "inventory.reference_placeholder": "采购单/销售单号、收货单号等",
  "inventory.notes_placeholder": "附加备注或说明",
  "inventory.transfer_notes_placeholder": "调拨原因",
  "inventory.adjustment_notes_placeholder": "说明调整原因",
  "inventory.positive_negative_allowed": "允许正负值",
  "inventory.process_inbound": "处理入库",
  "inventory.process_outbound": "处理出库",
  "inventory.process_transfer": "处理调拨",
  "inventory.process_adjustment": "处理调整",
  "inventory.adjustment_warning": "警告",
  "inventory.adjustment_warning_text": "调整会直接修改库存数量。请确保有适当的授权和文档记录。",
  "inventory.search_transactions": "搜索交易记录...",
  "inventory.filter_by_type": "按类型筛选",
  "inventory.filter_by_location": "按位置筛选",
  "inventory.all_types": "所有类型",
  "inventory.all_locations": "所有位置",
  "inventory.no_transactions": "未找到交易记录",
  "inventory.showing_transactions": "显示 {count} 条，共 {total} 条交易记录",
  "inventory.fetch_error": "数据加载失败",
  "inventory.adjustment_notes": "调整备注",
  "inventory.reason_receipt": "收货",
  "inventory.reason_shipment": "发货",
  "inventory.reason_transfer": "调拨",
  "inventory.reason_cycle_count": "盘点",
  "inventory.reason_damage": "损坏",
  "inventory.reason_obsolete": "报废",
  "inventory.reason_adjustment": "调整",
  "inventory.reason_return": "退货",
  "inventory.reason_sample": "样品",
  "inventory.status_pending": "待处理",
  "inventory.status_approved": "已批准",
  "inventory.status_rejected": "已拒绝",

  // ✅ NEW: Enhanced Inventory KPI Cards (Chinese)
  "inventory.finishedGoods": "成品库存",
  "inventory.rawMaterials": "原材料库存",
  "inventory.totalValue": "总价值",

  // ✅ MISSING INVENTORY KEYS: Add missing Chinese translations
  "inventory.management.title": "库存管理",
  "inventory.management.subtitle": "管理库存水平、入库和出库操作",
  "inventory.tabs.finished_goods": "成品库存",
  "inventory.tabs.analytics": "分析报告",
  "inventory.tabs.discrepancy": "差异分析",
  "inventory.quick_actions.title": "快速操作",
  "inventory.quick_actions.subtitle": "常用库存操作",
  "inventory.quick_actions.receive": "收货",
  "inventory.quick_actions.ship": "发货",
  "inventory.dialogs.quantity": "数量",
  "inventory.dialogs.location": "位置",

  // Company Profile
  "company.profile.title": "公司资料",
  "company.profile.subtitle": "管理您的公司信息和设置",
  "company.profile.not_found": "未找到公司资料",
  "company.profile.not_found_desc": "看起来您还没有完成公司资料设置。",
  "company.profile.complete_setup": "完成公司设置",
  "company.profile.complete": "完整",
  "company.profile.incomplete": "不完整",
  "company.profile.edit": "编辑资料",
  "company.profile.save": "保存更改",
  "company.profile.cancel": "取消",
  "company.profile.tabs.basic": "基本信息",
  "company.profile.tabs.business": "业务详情",
  "company.profile.tabs.banking": "银行信息",
  "company.profile.tabs.export": "出口贸易",
  "company.profile.basic.description": "您公司的基本联系方式和地址信息",
  "company.profile.business.description": "业务注册和运营信息",
  "company.profile.banking.description": "银行和金融账户详情",
  "company.profile.export.description": "出口许可和贸易合规信息",
  "company.profile.success.updated": "公司资料更新成功！",
  "company.profile.error.update": "更新资料失败",

  // Company Profile Form Fields
  "company.field.name": "公司名称",
  "company.field.legal_name": "法定公司名称",
  "company.field.email": "邮箱地址",
  "company.field.phone": "电话号码",
  "company.field.website": "网站",
  "company.field.country": "国家",
  "company.field.address_line1": "街道地址",
  "company.field.address_line2": "地址第二行",
  "company.field.city": "城市",
  "company.field.state_province": "省/州",
  "company.field.postal_code": "邮政编码",
  "company.field.industry": "行业",
  "company.field.business_type": "业务类型",
  "company.field.employee_count": "员工数量",
  "company.field.annual_revenue": "年收入",
  "company.field.registration_number": "注册号",
  "company.field.tax_id": "税务编号",
  "company.field.vat_number": "增值税号",
  "company.field.bank_name": "银行名称",
  "company.field.bank_account": "账户号码",
  "company.field.bank_swift": "SWIFT/BIC代码",
  "company.field.bank_address": "银行地址",
  "company.field.export_license": "出口许可证",
  "company.field.customs_code": "海关代码",
  "company.field.preferred_incoterms": "首选贸易条款",
  "company.field.preferred_payment_terms": "首选付款条件",

  // Quality Control
  "quality.title": "质量控制",
  "quality.subtitle": "管理质量检验、缺陷跟踪和合规报告",
  "quality.standards": "质量标准",
  "quality.metrics.title": "质量指标",
  "quality.metrics.pass_rate": "合格率",
  "quality.metrics.total_inspections": "总检验数",
  "quality.metrics.pending": "待检验数",
  "quality.metrics.defect_rate": "缺陷率",
  "quality.inspections.title": "最近检验",
  "quality.inspections.subtitle": "最新质量检验结果",
  "quality.defects.title": "缺陷跟踪",
  "quality.defects.subtitle": "跟踪和管理质量缺陷",

  // Contract Templates
  "contract_templates.title": "合同模板",
  "contract_templates.subtitle": "管理销售和采购协议的可重用合同模板",
  "contract_templates.add": "添加模板",
  "contract_templates.table.name": "模板名称",
  "contract_templates.table.type": "类型",
  "contract_templates.table.language": "语言",
  "contract_templates.table.version": "版本",
  "contract_templates.table.status": "状态",
  "contract_templates.table.actions": "操作",

  // Contract Forms (Add/Edit)
  "contracts.form.number": "合同编号",
  "contracts.form.customer": "客户",
  "contracts.form.supplier": "供应商",
  "contracts.form.currency": "货币",
  "contracts.form.template": "模板",
  "contracts.form.items": "合同项目",
  "contracts.form.product": "产品",
  "contracts.form.quantity": "数量",
  "contracts.form.price": "价格",
  "contracts.form.total": "总计",
  "contracts.form.add_item": "添加项目",
  "contracts.form.remove_item": "移除",
  "contracts.form.contract_info": "合同信息",
  "contracts.form.contract_info_desc": "基本合同详情和客户信息",
  "contracts.form.items_section": "合同项目",
  "contracts.form.items_section_desc": "此合同的产品和数量",
  "contracts.form.template_section": "合同模板",
  "contracts.form.template_section_desc": "选择合同文档生成模板",

  // Contract View
  "contracts.view.back": "返回合同",
  "contracts.view.back_sales": "返回销售合同",
  "contracts.view.back_purchase": "返回采购合同",
  "contracts.view.edit_contract": "编辑合同",
  "contracts.view.export_pdf": "导出PDF",
  "contracts.view.loading": "正在加载合同文档...",
  "contracts.view.no_document": "无可用合同文档",
  "contracts.view.contract_summary": "合同摘要",
  "contracts.view.contract_document": "合同文档",
  "contracts.view.customer": "客户",
  "contracts.view.supplier": "供应商",
  "contracts.view.contract_date": "合同日期",
  "contracts.view.total_value": "合同总额",
  "contracts.view.template": "模板",
  "contracts.view.no_email": "无邮箱",
  "contracts.view.items_count": "{count} 项",
  "contracts.view.sales_template": "销售模板",
  "contracts.view.purchase_template": "采购模板",

  // Contract Edit
  "contracts.edit.title_sales": "编辑销售合同",
  "contracts.edit.title_purchase": "编辑采购合同",
  "contracts.edit.subtitle": "更新合同 {number} 的详细信息",
  "contracts.edit.back": "返回合同",
  "contracts.edit.template_optional": "合同模板（可选）",
  "contracts.edit.template_desc": "销售合同模板",
  "contracts.edit.preview": "预览",
  "contracts.edit.items_title": "合同项目",
  "contracts.edit.add_item": "添加项目",
  "contracts.edit.product": "产品",
  "contracts.edit.quantity": "数量",
  "contracts.edit.unit_price": "单价",
  "contracts.edit.sku_label": "SKU: {sku}",
  "contracts.edit.unit_label": "单位: {unit}",

  // Contract Creation
  "contracts.create.title_sales": "创建销售合同",
  "contracts.create.title_purchase": "创建采购合同",
  "contracts.create.subtitle_sales": "输入新销售合同的详细信息",
  "contracts.create.subtitle_purchase": "输入新采购合同的详细信息",
  "contracts.create.back_sales": "销售合同",
  "contracts.create.back_purchase": "采购合同",
  "contracts.create.add_new": "添加新合同",
  "contracts.create.contract_info": "合同信息",
  "contracts.create.contract_info_desc": "基本合同详情和客户信息",
  "contracts.create.contract_info_desc_purchase": "基本合同详情和供应商信息",
  "contracts.create.number": "合同编号",
  "contracts.create.number_placeholder": "例如：PC-2025-001",
  "contracts.create.supplier": "供应商",
  "contracts.create.supplier_placeholder": "选择供应商...",
  "contracts.create.customer": "客户",
  "contracts.create.customer_placeholder": "选择客户...",
  "contracts.create.currency": "货币",
  "contracts.create.currency_placeholder": "例如：USD",
  "contracts.create.template": "合同模板（可选）",
  "contracts.create.template_placeholder": "选择模板...",
  "contracts.create.items": "合同项目",
  "contracts.create.items_desc": "为此合同添加产品和数量",
  "contracts.create.add_item": "添加项目",
  "contracts.create.remove_item": "删除项目",
  "contracts.create.product": "产品",
  "contracts.create.product_placeholder": "选择产品...",
  "contracts.create.quantity": "数量",
  "contracts.create.quantity_placeholder": "0",
  "contracts.create.unit_price": "单价",
  "contracts.create.unit_price_placeholder": "0.00",
  "contracts.create.total": "总计",
  "contracts.create.cancel": "取消",
  "contracts.create.create": "创建合同",
  "contracts.create.creating": "创建中...",
  "contracts.create.success": "合同创建成功！",
  "contracts.create.error": "创建合同失败",
  "contracts.create.summary": "合同摘要",
  "contracts.create.summary_desc": "查看合同总价值和详细信息",
  "contracts.create.items_count": "项目数量:",
  "contracts.create.currency_label": "货币:",
  "contracts.create.total_value": "合同总价值:",

  // Contract Templates
  "contracts.templates.page_title": "合同模板",
  "contracts.templates.page_desc": "管理销售和采购协议的合同模板",
  "contracts.templates.sales_section": "销售合同模板",
  "contracts.templates.sales_desc": "创建和管理销售合同模板",
  "contracts.templates.purchase_section": "采购合同模板",
  "contracts.templates.purchase_desc": "创建和管理采购合同模板",
  "contracts.templates.template_name": "模板名称",
  "contracts.templates.currency": "货币",
  "contracts.templates.payment_terms": "付款条款",
  "contracts.templates.delivery_terms": "交付条款",
  "contracts.templates.template_content": "模板内容",
  "contracts.templates.content_placeholder": "输入合同模板内容，使用占位符如 {{customer_name}}、{{product_name}} 等",
  "contracts.templates.content_placeholder_purchase": "输入合同模板内容，使用占位符如 {{supplier_name}}、{{material_name}} 等",
  "contracts.templates.create_template": "创建模板",
  "contracts.templates.existing_templates": "现有模板",
  "contracts.templates.name": "名称",
  "contracts.templates.actions": "操作",
  "contracts.templates.payment_30_days": "30天",
  "contracts.templates.payment_60_days": "60天",
  "contracts.templates.delivery_fob": "FOB",
  "contracts.templates.delivery_cif": "CIF",
  "contracts.templates.delivery_exw": "EXW",

  // Contract Templates Cards
  "contract_templates.sales.title": "销售合同模板",
  "contract_templates.sales.description": "创建和管理销售合同模板",
  "contract_templates.sales.sample": "示例模板",
  "contract_templates.sales.sample_title": "专业销售合同模板",
  "contract_templates.sales.sample_desc": "复制此专业模板并粘贴到下面的模板内容字段中。",
  "contract_templates.purchase.title": "采购合同模板",
  "contract_templates.purchase.description": "创建和管理采购合同模板",
  "contract_templates.purchase.sample": "示例模板",
  "contract_templates.purchase.sample_title": "专业采购合同模板",
  "contract_templates.purchase.sample_desc": "复制此专业模板并粘贴到下面的模板内容字段中。",

  // Quality Control
  "quality.title": "质量控制",
  "quality.subtitle": "管理质量检验和证书",
  "quality.attachments.documents.title": "文档附件",
  "quality.attachments.documents.upload": "上传文档",
  "quality.attachments.documents.formats": "支持格式：PDF、DOC、DOCX、XLS、XLSX、TXT",
  "quality.attachments.documents.none": "无文档附件",
  "quality.attachments.photos.title": "照片附件",
  "quality.attachments.photos.upload": "上传照片",
  "quality.attachments.photos.formats": "支持格式：JPG、PNG、GIF、WebP",
  "quality.attachments.photos.none": "无照片附件",
  "quality.attachments.preview": "预览",
  "quality.attachments.download": "下载",
  "quality.attachments.remove": "删除",
  "quality.attachments.uploading": "正在上传文件...",
  "quality.attachments.upload_success": "上传成功",
  "quality.attachments.upload_success_desc": "个文件上传成功",
  "quality.attachments.download_success": "下载完成",
  "quality.attachments.download_success_desc": "下载成功",
  "quality.attachments.download_failed": "下载失败",
  "quality.attachments.download_failed_desc": "下载文件失败，请重试。",
  "quality.attachments.remove_success": "文件已删除",
  "quality.attachments.remove_success_desc": "文件删除成功",
  "quality.attachments.remove_failed": "删除失败",
  "quality.attachments.remove_failed_desc": "删除文件失败，请重试。",

  // Quality Status
  "quality.status": "质量状态",
  "quality.status.pending": "待检验",
  "quality.status.approved": "已通过",
  "quality.status.quarantined": "已隔离",
  "quality.status.rejected": "已拒绝",

  // Work Orders
  "workOrders.title": "生产工单",
  "workOrders.forProduct": "此产品的生产工单",
  "workOrder.title": "生产工单",
  "workOrder.number": "工单编号",
  "workOrder.status.completed": "已完成",
  "workOrder.status.pending": "待开始",
  "workOrder.status.in-progress": "进行中",

  // Product Quality Requirements
  "products.form.quality_requirements": "质量要求",
  "products.form.inspection_required": "需要质量检验",
  "products.form.inspection_required_desc": "如果产品需要质量检验才能通过，请启用此选项",
  "products.form.quality_tolerance": "质量公差",
  "products.form.quality_notes": "质量备注",
  "products.form.quality_notes_placeholder": "输入具体的质量要求、标准或检验标准...",
  "products.quality.not_required": "无需检验",
  "products.success.updated": "产品已更新",
  "products.success.updated_desc": "产品质量要求已成功更新。",
  "products.success.created_desc": "已成功创建具有质量要求的产品。",
  "products.error.update": "更新失败",

  // Work Orders Quality Gate Modal
  "work_orders.quality_gate.title": "需要质量审批",
  "work_orders.quality_gate.description": "此工单需要完成所有必需的质量检验审批后才能完成。",
  "work_orders.quality_gate.work_order_info": "工单信息",
  "work_orders.quality_gate.inspections_status": "质量检验状态",
  "work_orders.quality_gate.no_inspections": "未找到质量检验记录。可能需要创建检验。",
  "work_orders.quality_gate.completion_status": "完成状态",
  "work_orders.quality_gate.can_complete": "所有质量要求已满足。工单可以完成。",
  "work_orders.quality_gate.cannot_complete": "完成前需要质量审批。",
  "work_orders.quality_gate.pending_inspections": "待处理检验",
  "work_orders.quality_gate.complete_inspections_first": "请先完成所有待处理的质量检验。",
  "work_orders.quality_gate.go_to_quality_control": "前往质量控制",
  "work_orders.quality_gate.complete_work_order": "完成工单",

  // Additional translations for quality gate modal
  "quality.inspector": "检验员",
  "quality.inspection_types.final": "最终检验",
  "quality.inspection_types.incoming": "来料检验",
  "quality.inspection_types.in_process": "过程检验",
  "quality.status.pending": "待处理",
  "quality.status.passed": "通过",
  "quality.status.failed": "失败",
  "common.close": "关闭",
  "common.processing": "处理中...",

  // ✅ MISSING QUALITY KEYS: Add missing Chinese translations
  "quality.quality_inspections": "质量检验",
  "quality.analytics_dashboard": "分析仪表板",
  "quality.loading_quality_inspections": "正在加载质量检验...",
  "quality.loading_quality_analytics": "正在加载质量分析...",
  "quality.loading_quality_inspection": "正在加载质量检验...",
  "quality.loading_quality_inspection_editor": "正在加载质量检验编辑器...",
  "quality.total_inspections": "总检验数",
  "quality.across_contracts": "跨 {count} 个合同",

  // ✅ MISSING SHIPPING KEYS: Add missing Chinese translations
  "shipping.back_to_shipping": "返回发货管理",
  "shipping.shipment_to": "发货至",

  // ✅ RAW MATERIALS TRANSLATIONS: Complete raw materials page localization (Chinese)
  "raw_materials.title": "原材料",
  "raw_materials.subtitle": "管理您的原材料库存和供应商关系",
  "raw_materials.new_material": "新建材料",
  "raw_materials.total_materials": "材料总数",
  "raw_materials.active_materials": "活跃材料",
  "raw_materials.with_stock": "有库存",
  "raw_materials.discontinued": "已停产",
  "raw_materials.search_placeholder": "按名称、SKU或描述搜索材料...",
  "raw_materials.filter_by_category": "按类别筛选",
  "raw_materials.filter_by_status": "按状态筛选",
  "raw_materials.filters.all_categories": "所有类别",
  "raw_materials.filters.all_status": "所有状态",
  "raw_materials.filters.active": "活跃",
  "raw_materials.filters.inactive": "非活跃",
  "raw_materials.filters.discontinued": "已停产",
  "raw_materials.filters.yarn": "纱线",
  "raw_materials.filters.fabric": "面料",
  "raw_materials.filters.dyes": "染料",
  "raw_materials.filters.chemicals": "化学品",
  "raw_materials.filters.accessories": "辅料",
  "raw_materials.filters.other": "其他",
  "raw_materials.more_filters": "更多筛选",
  "raw_materials.inventory_title": "原材料库存",
  "raw_materials.showing_results": "显示 {count} / {total} 个材料",
  "raw_materials.table.sku": "SKU",
  "raw_materials.table.material_name": "材料名称",
  "raw_materials.table.category": "类别",
  "raw_materials.table.primary_supplier": "主要供应商",
  "raw_materials.table.unit": "单位",
  "raw_materials.table.standard_cost": "标准成本",
  "raw_materials.table.stock_status": "库存状态",
  "raw_materials.table.status": "状态",
  "raw_materials.table.actions": "操作",
  "raw_materials.no_materials_found": "未找到原材料。创建您的第一个材料以开始使用。",
  "raw_materials.no_materials_match_filters": "没有材料符合您当前的筛选条件。请尝试调整搜索或筛选条件。",
  "raw_materials.no_supplier": "无供应商",
  "raw_materials.no_stock": "无库存",
  "raw_materials.low_stock": "库存不足",
  "raw_materials.in_stock": "有库存",
  "raw_materials.raw_materials_storage": "原材料仓库",
  "raw_materials.main_finished_goods_warehouse": "主要成品仓库",
  "raw_materials.note": "原材料是您制造过程的基础。保持准确的库存水平和供应商关系，以实现最佳的生产计划。",

  // ✅ RAW MATERIALS VIEW PAGE TRANSLATIONS (Chinese)
  "raw_materials.view.back_to_materials": "返回材料",
  "raw_materials.view.add_lot": "添加批次",
  "raw_materials.view.edit": "编辑",
  "raw_materials.view.total_lots": "总批次",
  "raw_materials.view.available_qty": "可用数量",
  "raw_materials.view.total_value": "总价值",
  "raw_materials.view.standard_cost": "标准成本",
  "raw_materials.view.material_information": "材料信息",
  "raw_materials.view.primary_supplier": "主要供应商",
  "raw_materials.view.composition": "成分",
  "raw_materials.view.quality_grade": "质量等级",
  "raw_materials.view.lead_time": "交货时间",
  "raw_materials.view.days": "天",
  "raw_materials.view.inventory_settings": "库存设置",
  "raw_materials.view.reorder_point": "再订购点",
  "raw_materials.view.max_stock_level": "最大库存水平",
  "raw_materials.view.inspection_required": "需要检验",
  "raw_materials.view.yes": "是",
  "raw_materials.view.no": "否",
  "raw_materials.view.specifications": "规格",
  "raw_materials.view.not_specified": "未指定",
  "raw_materials.view.no_supplier_assigned": "未分配供应商",
  "raw_materials.view.tabs.inventory_lots": "库存批次",
  "raw_materials.view.tabs.bom_usage": "BOM使用",
  "raw_materials.view.tabs.consumption_history": "消耗历史",
  "raw_materials.view.lots.lot_number": "批次号",
  "raw_materials.view.lots.supplier": "供应商",
  "raw_materials.view.lots.quantity": "数量",
  "raw_materials.view.lots.unit_cost": "单位成本",
  "raw_materials.view.lots.received_date": "接收日期",
  "raw_materials.view.lots.quality_status": "质量状态",
  "raw_materials.view.lots.status": "状态",
  "raw_materials.view.lots.actions": "操作",
  "raw_materials.view.lots.no_lots_found": "未找到此材料的批次。",
  "raw_materials.view.lots.unknown_supplier": "未知供应商",

  // ✅ RAW MATERIALS EDIT PAGE TRANSLATIONS (Chinese)
  "raw_materials.edit.back_to_material": "返回材料",
  "raw_materials.edit.title": "编辑原材料",
  "raw_materials.edit.update_material": "更新 {name} ({sku})",
  "raw_materials.edit.material_information": "材料信息",
  "raw_materials.edit.sku_label": "SKU",
  "raw_materials.edit.sku_placeholder": "输入材料SKU",
  "raw_materials.edit.name_label": "材料名称",
  "raw_materials.edit.name_placeholder": "输入材料名称",
  "raw_materials.edit.category_label": "类别",
  "raw_materials.edit.unit_label": "单位",
  "raw_materials.edit.unit_placeholder": "例如：公斤、米、升",
  "raw_materials.edit.supplier_label": "主要供应商",
  "raw_materials.edit.supplier_placeholder": "选择主要供应商",
  "raw_materials.edit.composition_label": "成分",
  "raw_materials.edit.composition_placeholder": "例如：100%棉",
  "raw_materials.edit.quality_grade_label": "质量等级",
  "raw_materials.edit.quality_grade_placeholder": "例如：优质、标准",
  "raw_materials.edit.specifications_label": "规格",
  "raw_materials.edit.specifications_placeholder": "输入详细规格和要求...",
  "raw_materials.edit.cost_inventory_settings": "成本和库存设置",
  "raw_materials.edit.standard_cost_label": "标准成本",
  "raw_materials.edit.reorder_point_label": "再订购点",
  "raw_materials.edit.lead_time_label": "交货时间（天）",
  "raw_materials.edit.status_quality_control": "状态和质量控制",
  "raw_materials.edit.status_label": "状态",
  "raw_materials.edit.inspection_required_label": "需要检验",
  "raw_materials.edit.quality_notes_label": "质量备注",
  "raw_materials.edit.quality_notes_placeholder": "输入质量控制备注和要求...",
  "raw_materials.edit.update_button": "更新材料",
  "raw_materials.edit.updating": "更新中...",
  "raw_materials.edit.cancel": "取消",
  "raw_materials.edit.note_title": "注意：",
  "raw_materials.edit.note": "更改原材料规格可能会影响现有的BOM和生产计划。进行更改后请检查所有相关产品。",
  "raw_materials.edit.category_placeholder": "选择类别",
  "raw_materials.edit.success_message": "原材料更新成功",
  "raw_materials.edit.error_message": "更新原材料失败",

  // ✅ LOCATIONS PAGE TRANSLATIONS (Chinese)
  "locations.title": "位置管理",
  "locations.subtitle": "管理仓库位置、容量和设施利用率",
  "locations.add_location": "添加位置",
  "locations.kpi.total_locations": "总位置数",
  "locations.kpi.total_capacity": "总容量",
  "locations.kpi.active_locations": "活跃位置",
  "locations.kpi.average_utilization": "平均利用率",
  "locations.table.location_directory": "位置目录",
  "locations.table.location_directory_desc": "所有设施位置的综合概览，包含容量和利用率指标",
  "locations.table.name": "名称",
  "locations.table.type": "类型",
  "locations.table.capacity": "容量",
  "locations.table.utilization": "利用率",
  "locations.table.details": "详情",
  "locations.table.status": "状态",
  "locations.table.actions": "操作",
  "locations.table.security": "安全级别",
  "locations.table.available": "可用",
  "locations.table.loading": "加载中...",
  "locations.table.no_data": "无数据",
  "locations.status.active": "活跃",
  "locations.status.inactive": "非活跃",

  // Location Types (Chinese)
  "locations.type.warehouse": "仓库",
  "locations.type.raw_materials": "原材料",
  "locations.type.finished_goods": "成品",
  "locations.type.work_in_progress": "在制品",
  "locations.type.quality_control": "质量控制",
  "locations.type.shipping": "发货",
  "locations.type.receiving": "收货",
  "locations.type.quarantine": "隔离",
  "locations.type.returns": "退货",

  // Security Levels (Chinese)
  "locations.security.low": "低",
  "locations.security.medium": "中",
  "locations.security.high": "高",

  // Dialog Titles (Chinese) (Edit only - Add dialog removed)
  "locations.dialog.edit_title": "编辑位置",

  // Form Labels and Placeholders (Chinese)
  "locations.form.name_label": "位置名称",
  "locations.form.name_placeholder": "输入位置名称",
  "locations.form.type_label": "位置类型",
  "locations.form.type_placeholder": "选择位置类型",
  "locations.form.description_label": "描述",
  "locations.form.description_placeholder": "输入位置描述",
  "locations.form.capacity_label": "容量",
  "locations.form.capacity_placeholder": "输入容量",
  "locations.form.code_label": "位置代码",
  "locations.form.code_placeholder": "输入位置代码",
  "locations.form.zone_label": "区域",
  "locations.form.zone_placeholder": "输入区域",
  "locations.form.security_level_label": "安全级别",
  "locations.form.security_level_placeholder": "选择安全级别",
  "locations.form.temperature_controlled_label": "温控",
  "locations.form.allows_mixed_products_label": "允许混合产品",
  "locations.form.cancel_button": "取消",
  "locations.form.create_button": "创建位置",
  "locations.form.update_button": "更新位置",

  // Loading and Messages (Chinese)
  "locations.loading": "加载位置中...",
  "locations.create.title": "创建新位置",
  "locations.create.description": "向您的设施管理系统添加新位置",
  "locations.create.location_name_id": "位置名称（ID）",
  "locations.create.location_code": "位置代码",
  "locations.create.description_brief": "位置用途的简要描述",
  "locations.create.type": "类型",
  "locations.create.capacity": "容量",
  "locations.create.zone": "区域",
  "locations.create.cancel": "取消",
  "locations.create.create_location": "创建位置",

  // Toast Messages (Chinese)
  "locations.toast.load_error": "加载位置失败",
  "locations.toast.validation_error": "请填写所有必填字段",
  "locations.toast.create_success": "位置创建成功",
  "locations.toast.create_error": "创建位置失败",
  "locations.toast.update_success": "位置更新成功",
  "locations.toast.update_error": "更新位置失败",
  "locations.toast.delete_success": "位置删除成功",
  "locations.toast.delete_error": "删除位置失败",
  "locations.toast.delete_confirm": "您确定要删除此位置吗？",

  // ✅ SHIPPING PAGE TRANSLATIONS (Chinese)
  "shipping.title": "运输管理",
  "shipping.subtitle": "管理货运、跟踪交付和优化物流运营",
  "shipping.new_shipment": "新建货运",
  "shipping.search_placeholder": "按编号、客户或跟踪号搜索货运...",
  "shipping.filter.all_statuses": "所有状态",
  "shipping.filter.status_placeholder": "按状态筛选",

  // Overview Section (Chinese)
  "shipping.overview.title": "运输概览",
  "shipping.overview.subtitle": "实时运输指标和性能指标",
  "shipping.overview.last_updated": "最后更新",

  // Statistics Cards (Chinese)
  "shipping.stats.total": "总计",
  "shipping.stats.total_desc": "所有货运",
  "shipping.stats.preparing": "准备中",
  "shipping.stats.preparing_desc": "正在准备",
  "shipping.stats.shipped": "已发货",
  "shipping.stats.shipped_desc": "运输中",
  "shipping.stats.delivered": "已交付",
  "shipping.stats.delivered_desc": "成功交付",
  "shipping.stats.success_rate": "成功率",
  "shipping.stats.success_rate_desc": "交付成功率",

  // Pipeline Section (Chinese)
  "shipping.pipeline.title": "运输流程",
  "shipping.pipeline.show_details": "显示详情",
  "shipping.pipeline.hide_details": "隐藏详情",
  "shipping.pipeline.ready": "就绪",
  "shipping.pipeline.transit": "运输中",
  "shipping.pipeline.active_progress": "活跃进度",
  "shipping.pipeline.overall_progress": "总体进度",
  "shipping.pipeline.delivered_progress": "已交付",
  "shipping.pipeline.status_breakdown": "状态分解",
  "shipping.pipeline.active_rate": "活跃率",
  "shipping.pipeline.active_rate_desc": "当前活跃",
  "shipping.pipeline.ontime_rate": "准时率",
  "shipping.pipeline.ontime_rate_desc": "准时交付",

  // Table Headers (Chinese)
  "shipping.table.shipment": "货运",
  "shipping.table.customer": "客户",
  "shipping.table.method": "方式",
  "shipping.table.status": "状态",
  "shipping.table.items_qty": "项目/数量",
  "shipping.table.financial_impact": "财务影响",
  "shipping.table.ship_date": "发货日期",
  "shipping.table.tracking": "跟踪",
  "shipping.table.actions": "操作",

  // Table Content (Chinese)
  "shipping.table.no_shipments": "未找到货运",
  "shipping.table.no_shipments_desc": "创建您的第一个货运开始",
  "shipping.table.create_shipment": "创建货运",
  "shipping.table.not_scheduled": "未安排",
  "shipping.table.no_tracking": "无跟踪",
  "shipping.table.items": "项目",
  "shipping.table.units": "单位",
  "shipping.table.eta": "预计到达",

  // Actions Menu (Chinese)
  "shipping.actions.open_menu": "打开菜单",
  "shipping.actions.view_details": "查看详情",
  "shipping.actions.edit_shipment": "编辑货运",
  "shipping.actions.track_shipment": "跟踪货运",
  "shipping.actions.cancel_shipment": "取消货运",

  // Status Labels (Chinese)
  "shipping.status.preparing": "准备中",
  "shipping.status.ready": "就绪",
  "shipping.status.shipped": "已发货",
  "shipping.status.in_transit": "运输中",
  "shipping.status.out_for_delivery": "派送中",
  "shipping.status.delivered": "已交付",
  "shipping.status.cancelled": "已取消",
  "shipping.status.exception": "异常",

  // Shipping Methods (Chinese)
  "shipping.method.air_freight": "空运",
  "shipping.method.sea_freight": "海运",
  "shipping.method.express": "快递",
  "shipping.method.truck": "卡车",

  // Quick Actions (Chinese)
  "shipping.quick_actions.air_freight": "空运",
  "shipping.quick_actions.air_freight_desc": "紧急交付的快速国际运输",
  "shipping.quick_actions.create_air": "创建空运",
  "shipping.quick_actions.sea_freight": "海运",
  "shipping.quick_actions.sea_freight_desc": "大批量的经济海运",
  "shipping.quick_actions.create_sea": "创建海运",
  "shipping.quick_actions.express_delivery": "快递配送",
  "shipping.quick_actions.express_delivery_desc": "当日或次日配送服务",
  "shipping.quick_actions.create_express": "创建快递",

  // ✅ SHIPPING VIEW PAGE TRANSLATIONS (Chinese)
  "shipping.view.customer_info": "客户信息",
  "shipping.view.company": "公司",
  "shipping.view.contact_person": "联系人",
  "shipping.view.email": "邮箱",
  "shipping.view.phone": "电话",
  "shipping.view.address": "地址",
  "shipping.view.shipment_items": "货运项目",
  "shipping.view.items_total_qty": "项目 • 总数量：",
  "shipping.view.product": "产品",
  "shipping.view.sku": "SKU",
  "shipping.view.quantity": "数量",
  "shipping.view.unit_price": "单价",
  "shipping.view.total": "总计",
  "shipping.view.unit": "单位：",
  "shipping.view.no_items": "此货运中没有项目",
  "shipping.view.total_shipment_value": "货运总价值：",
  "shipping.view.additional_info": "附加信息",
  "shipping.view.notes": "备注",
  "shipping.view.special_instructions": "特殊说明",
  "shipping.view.shipping_details": "运输详情",
  "shipping.view.shipment_number": "货运编号",
  "shipping.view.tracking_number": "跟踪号",
  "shipping.view.carrier": "承运商",
  "shipping.view.ship_date": "发货日期",
  "shipping.view.estimated_delivery": "预计交付",
  "shipping.view.created": "创建时间",
  "shipping.view.last_updated": "最后更新",
  "shipping.view.related_contract": "相关合同",
  "shipping.view.contract_number": "合同编号",
  "shipping.view.contract_status": "合同状态",
  "shipping.view.view_contract": "查看合同",
  "shipping.view.cost_breakdown": "费用明细",
  "shipping.view.shipping_cost": "运输费用",
  "shipping.view.insurance_cost": "保险费用",
  "shipping.view.total_shipping_cost": "总运输费用",

  // ✅ SHIPPING EDIT PAGE TRANSLATIONS (Chinese)
  "shipping.edit.current_status": "当前货运状态",
  "shipping.edit.created": "创建时间",
  "shipping.edit.current_status_label": "当前状态：",
  "shipping.edit.related_contract": "相关合同：",
  "shipping.edit.customer_info": "客户信息",
  "shipping.edit.customer": "客户",
  "shipping.edit.select_customer": "选择客户",
  "shipping.edit.shipping_details": "运输详情",
  "shipping.edit.shipping_method": "运输方式",
  "shipping.edit.status": "状态",
  "shipping.edit.sea_freight": "海运",
  "shipping.edit.air_freight": "空运",
  "shipping.edit.express": "快递",
  "shipping.edit.truck": "卡车",
  "shipping.edit.carrier": "承运商",
  "shipping.edit.service_type": "服务类型",
  "shipping.edit.standard": "标准",
  "shipping.edit.economy": "经济",
  "shipping.edit.dates_tracking": "日期和跟踪",
  "shipping.edit.ship_date": "发货日期",
  "shipping.edit.estimated_delivery": "预计交付",
  "shipping.edit.tracking_number": "跟踪号",
  "shipping.edit.costs": "费用",
  "shipping.edit.shipping_cost": "运输费用",
  "shipping.edit.insurance_cost": "保险费用",
  "shipping.edit.additional_info": "附加信息",
  "shipping.edit.notes": "备注",
  "shipping.edit.special_instructions": "特殊说明",
  "shipping.edit.cancel": "取消",
  "shipping.edit.update_shipment": "更新货运",
  "shipping.edit.updating": "更新中...",

  // Missing edit page keys (Chinese)
  "shipping.edit.title": "编辑货运",
  "shipping.edit.back_to_shipment": "返回货运",
  "shipping.view.shipment": "货运",

  // Toast Messages (Chinese)
  "shipping.toast.updated": "货运已更新",
  "shipping.toast.updated_desc": "已成功更新。",
  "shipping.toast.error": "错误",
  "shipping.toast.update_failed": "更新货运失败",

  // ✅ EXPORT DECLARATION KEYS: Complete Chinese localization
  // Main page (Chinese)
  "export.main.title": "出口报关单",
  "export.main.description": "管理出口贸易文档和海关报关单",
  "export.main.new_declaration": "新建报关单",

  // Statistics (Chinese)
  "export.stats.total_declarations": "总报关单",
  "export.stats.success_rate": "成功率",
  "export.stats.draft": "草稿",
  "export.stats.draft_desc": "待完成",
  "export.stats.submitted": "已提交",
  "export.stats.submitted_desc": "审核中",
  "export.stats.approved": "已批准",
  "export.stats.approved_desc": "准备出口",
  "export.stats.cleared": "已清关",
  "export.stats.cleared_desc": "成功出口",
  "export.stats.attention_required": "需要关注",
  "export.stats.rejected_declarations": "被拒绝的报关单",
  "export.stats.review_issues": "审查问题",
  "export.stats.clear_filter": "清除筛选",

  // Table (Chinese)
  "export.table.title": "出口报关单",
  "export.table.description": "报关单总计",
  "export.table.search_placeholder": "搜索报关单...",
  "export.table.loading": "加载报关单中...",
  "export.table.declaration_no": "报关单号",
  "export.table.status": "状态",
  "export.table.contract": "合同",
  "export.table.items": "项目",
  "export.table.documents": "文档",
  "export.table.created": "创建时间",
  "export.table.no_match": "没有匹配的报关单",
  "export.empty": "未找到出口报关单",

  // Delete dialog (Chinese)
  "export.delete.title": "删除报关单",
  "export.delete.description": "您确定要删除此出口报关单吗？此操作无法撤销。",
  "export.delete.cancel": "取消",
  "export.delete.confirm": "删除",
  "export.delete.success": "报关单删除成功",
  "export.delete.error": "删除报关单失败",

  // View page (Chinese)
  "export.loading_declaration": "加载报关单中...",
  "export.back_to_declarations": "返回报关单",
  "export.declaration_details": "报关单详情",
  "export.edit_declaration": "编辑报关单",
  "export.declaration_information": "报关单信息",
  "export.declaration_number": "报关单号",
  "export.status": "状态",
  "export.created_date": "创建日期",
  "export.last_updated": "最后更新",
  "export.summary": "摘要",
  "export.total_items": "总项目数",
  "export.declaration_id": "报关单ID",
  "export.declaration_items": "报关单项目",
  "export.product": "产品",
  "export.sku": "SKU",
  "export.quantity": "数量",
  "export.hs_code": "HS编码",
  "export.linked_shipments": "关联货运",
  "export.linked_shipments_description": "与此报关单关联的货运",
  "export.shipment_number": "货运单号",
  "export.customer": "客户",
  "export.ship_date": "发货日期",
  "export.items": "项目",
  "export.no_shipments_linked": "此报关单未关联任何货运",

  // Documents (Chinese)
  "export.documents.title": "出口文档",
  "export.documents.view_description": "查看和下载报关单文档",
  "export.documents.no_documents": "此报关单未上传任何文档",
  "export.documents.download_success": "下载完成",
  "export.documents.download_success_desc": "下载成功",
  "export.documents.download_failed": "下载失败",
  "export.documents.download_failed_desc": "无法下载文件",

  // Additional view page keys (Chinese)
  "export.no_items_found": "此报关单未找到任何项目",
  "export.unknown_product": "未知产品",
  "export.unknown_customer": "未知客户",
  "export.items_count": "项目",
  "export.unknown_type": "未知类型",
  "export.manual_entry": "手动录入",
  "export.files": "文件",

  // Edit page (Chinese)
  "export.edit.title": "编辑报关单",
  "export.edit.back_to_declarations": "返回报关单",
  "export.edit.declaration_details": "报关单详情",
  "export.edit.declaration_items": "报关单项目",
  "export.edit.manage_products": "管理此出口报关单的产品",
  "export.edit.cancel": "取消",
  "export.edit.save_changes": "保存更改",
  "export.edit.contract_label": "合同",
  "export.edit.items_label": "项目",
  "export.edit.shipments_label": "货运",
  "export.edit.documents_label": "文档",

  // Products section (Chinese)
  "export.products.inherit_button": "从合同继承",
  "export.products.add_manual": "手动添加产品",
  "export.products.table.product": "产品",
  "export.products.table.quantity": "数量",
  "export.products.table.hs_code": "HS编码",
  "export.products.table.quality": "质量",
  "export.products.table.actions": "操作",
  "export.products.inherited": "继承自",

  // Shipments section (Chinese)
  "export.shipments.integration_title": "货运集成",
  "export.shipments.integration_description": "将货运链接到此出口报关单",
  "export.shipments.selected_count": "已选择",
  "export.shipments.shipment_count": "货运",

  // Documents section (Chinese)
  "export.documents.management_title": "文档管理",
  "export.documents.management_description": "上传和管理出口文档",
  "export.documents.existing_document": "现有文档",
  "export.documents.upload_new_title": "上传新文档",
  "export.documents.upload_new_description": "为此报关单添加额外文档",
  "export.documents.choose_files": "选择文件",

  // Additional edit form fields (Chinese)
  "export.edit.declaration_number": "报关单号",
  "export.edit.status": "状态",
  "export.contract.title": "合同信息",
  "export.documents.current_title": "当前文档",
  "export.form.declaration_items": "报关单项目",
  "export.shipments.select_label": "选择货运",

  // ✅ NEW: MRP Planning Report - Chinese
  "mrpPlanning.title": "MRP规划仪表板",
  "mrpPlanning.subtitle": "具有经过验证数据关系的综合物料需求规划",
  "mrpPlanning.backToReports": "返回报告",
  "mrpPlanning.realTimeData": "实时数据",
  "mrpPlanning.fullMRPDashboard": "完整MRP仪表板",
  "mrpPlanning.activeForecasts": "活跃预测",
  "mrpPlanning.demandForecastingPipeline": "需求预测管道",
  "mrpPlanning.procurementPlans": "采购计划",
  "mrpPlanning.materialProcurementPlanning": "物料采购规划",
  "mrpPlanning.averageMargin": "平均利润率",
  "mrpPlanning.bomProfitabilityAnalysis": "BOM盈利能力分析",
  "mrpPlanning.supplierPartners": "供应商合作伙伴",
  "mrpPlanning.leadTimeOptimization": "交货期优化",
  "mrpPlanning.provenMRPIntegration": "经过验证的MRP集成",
  "mrpPlanning.integrationDescription": "此报告与位于/planning的综合MRP规划仪表板集成，该仪表板已通过50多个测试场景的广泛测试，具有经过验证的数据关系。",
  "mrpPlanning.provenDataFlow": "经过验证的数据流",
  "mrpPlanning.dataFlow1": "产品 → 物料清单 → 原材料",
  "mrpPlanning.dataFlow2": "需求预测 → 采购计划",
  "mrpPlanning.dataFlow3": "供应商交货期 → 集装箱优化",
  "mrpPlanning.enterpriseFeatures": "企业功能",
  "mrpPlanning.feature1": "BOM盈利能力分析",
  "mrpPlanning.feature2": "多供应商采购规划",
  "mrpPlanning.feature3": "集装箱装载优化",
  "mrpPlanning.accessFullDashboard": "访问完整MRP规划仪表板",
  "mrpPlanning.demandForecasting": "需求预测",
  "mrpPlanning.demandForecastingDesc": "创建和管理具有BOM盈利能力分析的需求预测",
  "mrpPlanning.viewForecasts": "查看预测",
  "mrpPlanning.procurementPlanningTitle": "采购规划",
  "mrpPlanning.procurementPlanningDesc": "具有供应商优化的物料需求规划",
  "mrpPlanning.viewPlans": "查看计划",
  "mrpPlanning.containerOptimization": "集装箱优化",
  "mrpPlanning.containerOptimizationDesc": "优化集装箱装载和运输效率",
  "mrpPlanning.optimize": "优化",
  "mrpPlanning.supplierPerformance": "供应商绩效",
  "mrpPlanning.supplierPerformanceDesc": "跟踪供应商交货期和绩效指标",
  "mrpPlanning.viewSuppliers": "查看供应商",

  // ✅ NEW: MRP Planning Main Page - Chinese
  "planning.title": "MRP规划",
  "planning.subtitle": "物料需求规划和需求预测",
  "planning.refreshPriorities": "刷新优先级",
  "planning.newForecast": "新建预测",

  // Status Cards
  "planning.activeForecasts": "活跃预测",
  "planning.draft": "草稿",
  "planning.pending": "待处理",
  "planning.approved": "已批准",
  "planning.rejected": "已拒绝",
  "planning.procurementStatus": "采购状态",
  "planning.pendingApproval": "待批准",
  "planning.ordered": "已订购",
  "planning.readyToOrder": "准备订购",
  "planning.procurementPlans": "采购计划",
  "planning.estimated": "预估",
  "planning.allApproved": "全部批准",
  "planning.actionRequired": "需要操作",
  "planning.urgentHighPriority": "需要批准的紧急/高优先级计划",
  "planning.overdue": "逾期",
  "planning.supplierNetwork": "供应商网络",
  "planning.excellentRated": "优秀评级",
  "planning.avgLeadTime": "平均交货期",
  "planning.daysAverage": "天平均交付",
  "planning.approvedPlansReady": "已批准计划准备订购",
  "planning.actionAvailable": "可操作",
  "planning.forecastProfitability": "预测盈利能力",
  "planning.averageMargin": "平均利润率",
  "planning.totalRevenue": "总收入",
  "planning.totalProfit": "总利润",
  "planning.profitabilityDistribution": "盈利能力分布",
  "planning.excellent": "优秀",
  "planning.good": "良好",
  "planning.fair": "一般",
  "planning.poor": "较差",

  // Tabs
  "planning.tabs.overview": "概览",
  "planning.tabs.forecasting": "预测",
  "planning.tabs.procurement": "采购",
  "planning.tabs.analytics": "分析",

  // Overview Tab
  "planning.demandForecastsProduction": "需求预测和生产计划",
  "planning.activeForecastsDescription": "驱动物料需求的活跃预测",
  "planning.pending": "待处理",
  "planning.units": "单位",
  "planning.margin": "利润率",
  "planning.generatingPlans": "生成采购计划",
  "planning.materials": "材料",
  "planning.viewAllForecasts": "查看所有预测",

  // Material Procurement
  "planning.materialProcurementStatus": "物料采购状态",
  "planning.materialsNeeded": "已批准预测所需的材料",
  "planning.requireImmediateAction": "需要立即行动",
  "planning.target": "目标",
  "planning.estimatedCost": "预估成本",
  "planning.viewAllPlans": "查看所有计划",

  // Supplier Network
  "planning.supplierNetworkSummary": "供应商网络摘要",
  "planning.keySuppliersSupporting": "支持您活跃采购计划的关键供应商",
  "planning.activeSuppliers": "活跃供应商",
  "planning.totalProcurementValue": "总采购价值",
  "planning.priorityLevels": "优先级别",
  "planning.critical": "关键",
  "planning.immediate": "立即",
  "planning.high": "高",
  "planning.soon": "尽快",
  "planning.normal": "正常",
  "planning.standard": "标准",
  "planning.low": "低",
  "planning.future": "未来",

  // Procurement Tab
  "planning.purchaseRecommendations": "采购建议",
  "planning.material": "材料",
  "planning.quantity": "数量",
  "planning.supplier": "供应商",
  "planning.targetDate": "目标日期",
  "planning.cost": "成本",
  "planning.urgency": "紧急程度",

  // Analytics Tab
  "planning.mrpSystemAnalytics": "MRP系统分析",
  "planning.realTimeAnalytics": "来自您MRP操作的实时分析和绩效洞察",
  "planning.performanceOverview": "绩效概览",
  "planning.totalForecasts": "总预测",
  "planning.totalPlans": "总计划",
  "planning.totalValue": "总价值",
  "planning.avgMargin": "平均利润率",

  // ✅ RESTORED: Missing Secondary KPI Keys - Chinese
  "planning.supplierNetwork": "供应商网络",
  "planning.excellentRated": "优秀评级",
  "planning.daysAverage": "天平均交付",
  "planning.costEfficiency": "成本效率",
  "planning.containerUtilization": "集装箱利用率",
  "planning.optimized": "已优化",
  "planning.systemHealth": "系统健康",
  "planning.operationalEfficiency": "运营效率",
  "planning.forecastsAnalyzed": "预测已分析",

  // ✅ RESTORED: Quick Actions & Implementation Status - Chinese
  "planning.quickActions": "快速操作",
  "planning.commonMRPTasks": "常见MRP规划任务和工作流程",
  "planning.createForecast": "创建预测",
  "planning.generateDemandForecast": "生成需求预测",
  "planning.procurementPlanning": "采购规划",
  "planning.manageProcurementPlans": "管理采购计划",
  "planning.containerOptimization": "集装箱优化",
  "planning.optimizeShipping": "优化运输",
  "planning.performanceReport": "性能报告",
  "planning.viewAnalytics": "查看分析",
  "planning.mrpImplementationStatus": "MRP实施状态",
  "planning.databaseSchema": "数据库架构",
  "planning.complete": "完成",
  "planning.demandForecastingService": "需求预测服务",
  "planning.procurementPlanningService": "采购规划服务",
  "planning.supplierLeadTimeManagement": "供应商交付时间管理",
  "planning.containerOptimizationEngine": "集装箱优化引擎",
  "planning.apiEndpoints": "API端点",
  "planning.mrpDashboard": "MRP仪表板",
  "planning.workflowIntegration": "工作流集成",
  "planning.inProgress": "进行中",
  "planning.advancedAnalytics": "高级分析",
  "planning.nextPhase": "下一阶段",

  // ✅ FIXED: Data Display Fields - Chinese
  "planning.period": "周期",
  "planning.confidence": "置信度",
  "planning.status": "状态",

  // ✅ NEW: Procurement Page - Chinese
  "procurement.title": "采购规划",
  "procurement.subtitle": "物料需求规划和供应商管理",
  "procurement.searchPlaceholder": "搜索采购计划...",
  "procurement.newPlan": "新建计划",
  "procurement.viewPlan": "查看计划",
  "procurement.editPlan": "编辑计划",
  "procurement.deletePlan": "删除计划",
  "procurement.backToPlanning": "返回规划",
  "procurement.planDetails": "计划详情",
  "procurement.materialInfo": "材料信息",
  "procurement.supplierInfo": "供应商信息",
  "procurement.planningInfo": "规划信息",
  "procurement.actions": "操作",
  "procurement.save": "保存",
  "procurement.cancel": "取消",
  "procurement.delete": "删除",
  "procurement.confirmDelete": "确定要删除此采购计划吗？",
  "procurement.deleteSuccess": "采购计划删除成功",
  "procurement.saveSuccess": "采购计划保存成功",
  "procurement.loadError": "加载采购计划失败",
  "procurement.saveError": "保存采购计划失败",
  "procurement.deleteError": "删除采购计划失败",

  // ✅ NEW: Main Reports Page - Chinese
  "reports.title": "报告与分析",
  "reports.subtitle": "综合商业智能和绩效分析",
  "reports.kpiOverview": "KPI概览",
  "reports.totalRevenue": "总收入",
  "reports.activeCustomers": "活跃客户",
  "reports.completedOrders": "已完成订单",
  "reports.qualityScore": "质量评分",
  "reports.essentialReports": "核心报告",
  "reports.essentialReportsDesc": "具有真实数据集成的核心商业智能报告",
  "reports.viewReport": "查看报告",
  "reports.mrpPlanning": "MRP规划仪表板",
  "reports.mrpPlanningDesc": "需求预测、BOM盈利能力、采购规划和供应商绩效",
  "reports.financialPerformance": "财务绩效",
  "reports.financialPerformanceDesc": "使用真实发票数据进行损益分析、应收应付账龄和现金流预测",
  "reports.productionAnalytics": "生产分析",
  "reports.productionAnalyticsDesc": "工单效率、产能分析和质量集成洞察",
  "reports.qualityMetrics": "质量指标",
  "reports.qualityMetricsDesc": "检验状态、缺陷分析和质量趋势监控",
  "reports.inventoryIntelligence": "库存智能",
  "reports.inventoryIntelligenceDesc": "库存构成、补货建议和周转分析",
  "reports.businessIntelligence": "商业智能",
  "reports.businessIntelligenceDesc": "高管KPI、客户分析、合同绩效和战略洞察",
  "reports.migrationNotice": "迁移通知",
  "reports.migrationNoticeDesc": "报告系统已从14个报告优化为6个与经过验证的MRP工作流程保持一致的核心报告。导航从3级简化为2级，提供更好的用户体验。所有报告使用来自经过测试的数据库关系的真实数据，具有与MRP规划仪表板质量标准匹配的一致专业UI/UX。",
  "reports.simplifiedArchitecture": "简化架构",
  "reports.essentialReportsBadge": "6个核心报告",
  "reports.from": "来自",
  "reports.arInvoices": "张应收发票",
  "reports.productionEfficiency": "生产效率",
  "reports.of": "，共",
  "reports.ordersCompleted": "个订单已完成",
  "reports.qualityPassRate": "质量通过率",
  "reports.inspectionsPassed": "次检验通过",
  "reports.inventoryValue": "库存价值",
  "reports.across": "跨",
  "reports.stockItems": "个库存项目",
  "reports.optimized": "优化",
  "reports.optimizedDescription": "从14个报告减少到6个与经过验证的MRP工作流程保持一致的核心报告",
  "reports.simplified": "简化",
  "reports.simplifiedDescription": "将导航从3级扁平化为2级，提供更好的用户体验",
  "reports.integrated": "集成",
  "reports.integratedDescription": "所有报告使用来自经过测试的数据库关系的真实数据",
  "reports.professional": "专业",
  "reports.professionalDescription": "与MRP规划仪表板质量标准匹配的一致UI/UX",

  // ✅ NEW: Report Status Labels - Chinese
  "reports.status.active": "活跃",
  "reports.status.beta": "测试版",
  "reports.status.coming_soon": "即将推出",

  // ✅ NEW: MRP Planning Report Card - Chinese
  "reports.mrp-planning.title": "MRP规划仪表板",
  "reports.mrp-planning.description": "需求预测、BOM盈利能力、采购计划和供应商绩效",
  "reports.mrp-planning.metrics.0.label": "活跃预测",
  "reports.mrp-planning.metrics.1.label": "采购计划",
  "reports.mrp-planning.metrics.2.label": "集成",

  // ✅ NEW: Financial Performance Report Card - Chinese
  "reports.financial-performance.title": "财务绩效",
  "reports.financial-performance.description": "损益分析、应收应付账龄和现金流预测，基于真实发票数据",
  "reports.financial-performance.metrics.0.label": "当前收入",
  "reports.financial-performance.metrics.1.label": "当前支出",
  "reports.financial-performance.metrics.2.label": "活跃客户",

  // ✅ NEW: Production Analytics Report Card - Chinese
  "reports.production-analytics.title": "生产分析",
  "reports.production-analytics.description": "工单效率、产能分析和质量集成洞察",
  "reports.production-analytics.metrics.0.label": "总工单数",
  "reports.production-analytics.metrics.1.label": "已完成订单",
  "reports.production-analytics.metrics.2.label": "效率率",

  // ✅ NEW: Quality Metrics Report Card - Chinese
  "reports.quality-metrics.title": "质量指标",
  "reports.quality-metrics.description": "检验状态、缺陷分析和质量趋势监控",
  "reports.quality-metrics.metrics.0.label": "总检验数",
  "reports.quality-metrics.metrics.1.label": "通过检验",
  "reports.quality-metrics.metrics.2.label": "通过率",

  // ✅ NEW: Inventory Intelligence Report Card - Chinese
  "reports.inventory-intelligence.title": "库存智能",
  "reports.inventory-intelligence.description": "库存构成、补货建议和周转分析",
  "reports.inventory-intelligence.metrics.0.label": "库存批次",
  "reports.inventory-intelligence.metrics.1.label": "总价值",
  "reports.inventory-intelligence.metrics.2.label": "健康状态",

  // ✅ NEW: Business Intelligence Report Card - Chinese
  "reports.business-intelligence.title": "商业智能",
  "reports.business-intelligence.description": "执行KPI、客户分析、合同绩效和战略洞察",
  "reports.business-intelligence.metrics.0.label": "当前收入",
  "reports.business-intelligence.metrics.1.label": "活跃客户",
  "reports.business-intelligence.metrics.2.label": "合同绩效",

  // ✅ NEW: Financial Performance Report - Chinese
  "financialPerformance.title": "财务绩效",
  "financialPerformance.subtitle": "使用真实发票数据进行综合损益、应收应付账龄和现金流分析",
  "financialPerformance.backToReports": "返回报告",
  "financialPerformance.realData": "真实数据",
  "financialPerformance.totalRevenue": "总收入",
  "financialPerformance.netProfit": "净利润",
  "financialPerformance.profitMargin": "利润率",
  "financialPerformance.outstandingAR": "应收账款余额",
  "financialPerformance.accountsReceivableAging": "应收账款账龄",
  "financialPerformance.growthFromLastMonth": "较上月{growth}%",
  "financialPerformance.currentPeriod": "当前期间",
  "financialPerformance.overview": "概览",
  "financialPerformance.arAging": "应收账龄",
  "financialPerformance.apAging": "应付账龄",
  "financialPerformance.cashFlow": "现金流",
  "financialPerformance.profitLossSummary": "损益汇总",
  "financialPerformance.currentVsPrevious": "当月与上月对比",
  "financialPerformance.revenue": "收入",
  "financialPerformance.expenses": "支出",
  "financialPerformance.recentArInvoices": "最近应收发票",
  "financialPerformance.latestReceivableTransactions": "最新应收交易",
  "financialPerformance.unknownCustomer": "未知客户",
  "financialPerformance.status.paid": "已付款",
  "financialPerformance.status.pending": "待处理",
  "financialPerformance.status.overdue": "逾期",
  "financialPerformance.status.draft": "草稿",
  "financialPerformance.outstandingCustomerPayments": "按账龄分类的未收客户款项",
  "financialPerformance.current": "当前",
  "financialPerformance.days1to30": "1-30天",
  "financialPerformance.days31to60": "31-60天",
  "financialPerformance.days60Plus": "60天以上",
  "financialPerformance.currentPercentage": "当前 ({percentage}%)",
  "financialPerformance.accountsPayableAging": "应付账款账龄",
  "financialPerformance.outstandingSupplierPayments": "按账龄分类的未付供应商款项",
  "financialPerformance.cashFlowAnalysis": "现金流分析",
  "financialPerformance.projectedCashFlow": "基于应收应付账龄的现金流预测",
  "financialPerformance.expectedInflows": "预期流入（应收）",
  "financialPerformance.expectedOutflows": "预期流出（应付）",
  "financialPerformance.thisMonth": "本月",
  "financialPerformance.next30Days": "未来30天",
  "financialPerformance.netCashFlowProjection": "净现金流预测",
  "financialPerformance.realFinancialDataIntegration": "真实财务数据集成",
  "financialPerformance.dataSourceDescription": "此财务绩效报告基于您的真实制造ERP数据生成：应收发票（{arCount}张当前），应付发票（{apCount}张当前），以及销售/采购合同。所有计算使用具有适当多租户安全性的实际交易数据。",
  "financialPerformance.fromLastMonth": "较上月",

  // ✅ NEW: Production Analytics Report - Chinese
  "productionAnalytics.title": "生产分析",
  "productionAnalytics.subtitle": "工单效率、产能分析和质量集成洞察",
  "productionAnalytics.backToReports": "返回报告",
  "productionAnalytics.realTimeData": "实时数据",
  "productionAnalytics.totalWorkOrders": "总工单数",
  "productionAnalytics.completedOrders": "已完成订单",
  "productionAnalytics.averageEfficiency": "平均效率",
  "productionAnalytics.qualityIntegration": "质量集成",
  "productionAnalytics.productionStatusBreakdown": "生产状态分解",
  "productionAnalytics.workOrdersByStatus": "按当前状态分类的工单",
  "productionAnalytics.status.pending": "待处理",
  "productionAnalytics.status.in_progress": "进行中",
  "productionAnalytics.status.completed": "已完成",
  "productionAnalytics.status.cancelled": "已取消",
  "productionAnalytics.overview": "概览",
  "productionAnalytics.efficiency": "效率",
  "productionAnalytics.quality": "质量",
  "productionAnalytics.trends": "趋势",
  "productionAnalytics.productionEfficiencyMetrics": "生产效率指标",
  "productionAnalytics.workOrderCompletionRates": "工单完成率和绩效分析",
  "productionAnalytics.completionRate": "完成率",
  "productionAnalytics.averageLeadTime": "平均交货期",
  "productionAnalytics.days": "天",
  "productionAnalytics.qualityIntegrationMetrics": "质量集成指标",
  "productionAnalytics.qualityInspectionIntegration": "质量检验与生产工作流程集成",
  "productionAnalytics.inspectionsCompleted": "已完成检验",
  "productionAnalytics.qualityPassRate": "质量通过率",
  "productionAnalytics.recentWorkOrders": "最近工单",
  "productionAnalytics.latestProductionActivity": "最新生产活动和进度跟踪",
  "productionAnalytics.workOrder": "工单",
  "productionAnalytics.product": "产品",
  "productionAnalytics.status": "状态",
  "productionAnalytics.progress": "进度",
  "productionAnalytics.unknownProduct": "未知产品",
  "productionAnalytics.noWorkOrders": "暂无工单",
  "productionAnalytics.workOrdersWillAppear": "随着生产活动的创建，工单将会显示",
  "productionAnalytics.realProductionDataIntegration": "真实生产数据集成",
  "productionAnalytics.dataSourceDescription": "此生产分析报告与您的综合工单管理系统集成，包括生产操作、质量检验和库存集成。所有指标均基于具有经过验证工作流程关系的真实生产数据计算。",

  // ✅ NEW: Missing Production Analytics Keys - Chinese
  "productionAnalytics.workOrdersCount": "{count}个工单",
  "productionAnalytics.completionRate": "完成率",
  "productionAnalytics.ordersCompleted": "个订单已完成",
  "productionAnalytics.averageEfficiency": "平均效率",
  "productionAnalytics.productionEfficiencyMetric": "生产效率指标",
  "productionAnalytics.qualityPassRate": "质量通过率",
  "productionAnalytics.inspectionsPassed": "次检验通过",
  "productionAnalytics.inProgress": "进行中",
  "productionAnalytics.activeWorkOrders": "活跃工单",
  "productionAnalytics.productionStatusBreakdown": "生产状态分解",
  "productionAnalytics.workOrdersByStatus": "按当前状态分类的工单",
  "productionAnalytics.ordersCount": "{count}个订单",
  "productionAnalytics.productionOverview": "生产概览",
  "productionAnalytics.comprehensiveMetrics": "综合生产指标",
  "productionAnalytics.totalWorkOrders": "总工单数",
  "productionAnalytics.completedOrders": "已完成订单",
  "productionAnalytics.inProgressOrders": "进行中订单",
  "productionAnalytics.overview": "概览",
  "productionAnalytics.efficiencyAnalysis": "效率分析",
  "productionAnalytics.qualityIntegration": "质量集成",
  "productionAnalytics.recentOrders": "最近订单",
  "productionAnalytics.comprehensiveMetricsInsights": "综合生产指标和洞察",
  "productionAnalytics.efficiencyAnalysisByProduct": "按产品的效率分析",
  "productionAnalytics.productionEfficiencyMetricsProduct": "按产品的生产效率指标",
  "productionAnalytics.product": "产品",
  "productionAnalytics.avgEfficiency": "平均效率",
  "productionAnalytics.totalOrders": "总订单数",
  "productionAnalytics.completed": "已完成",
  "productionAnalytics.customer": "客户",
  "productionAnalytics.qualityInspectionStatus": "质量检验状态",
  "productionAnalytics.passedInspections": "通过检验",
  "productionAnalytics.failedInspections": "失败检验",
  "productionAnalytics.pendingInspections": "待处理检验",
  "productionAnalytics.qualityIntegrationRate": "质量集成率",
  "productionAnalytics.overallPassRate": "总体通过率",
  "productionAnalytics.recentWorkOrders": "最近工单",
  "productionAnalytics.latestProductionOrders": "最新生产订单和进度",
  "productionAnalytics.workOrder": "工单",
  "productionAnalytics.quality": "质量",
  "productionAnalytics.stock": "库存",
  "productionAnalytics.productionDataIntegration": "生产数据集成",
  "productionAnalytics.workOrders": "个工单",
  "productionAnalytics.orders": "个订单",

  // ✅ NEW: Quality Metrics Report - Chinese
  "qualityMetrics.title": "质量指标",
  "qualityMetrics.subtitle": "检验状态、缺陷分析和质量趋势监控",
  "qualityMetrics.backToReports": "返回报告",
  "qualityMetrics.realTimeData": "实时数据",
  "qualityMetrics.totalInspections": "总检验数",
  "qualityMetrics.passRate": "通过率",
  "qualityMetrics.activeDefects": "活跃缺陷",
  "qualityMetrics.certificatesIssued": "已签发证书",
  "qualityMetrics.qualityStatusOverview": "质量状态概览",
  "qualityMetrics.inspectionsByStatus": "按当前状态分类的检验",
  "qualityMetrics.status.pending": "待处理",
  "qualityMetrics.status.passed": "通过",
  "qualityMetrics.status.failed": "失败",
  "qualityMetrics.status.quarantined": "隔离",
  "qualityMetrics.overview": "概览",
  "qualityMetrics.defects": "缺陷",
  "qualityMetrics.inspections": "检验",
  "qualityMetrics.trends": "趋势",
  "qualityMetrics.defectSeverityAnalysis": "缺陷严重性分析",
  "qualityMetrics.defectsByImpactLevel": "按影响级别和严重性分类的缺陷",
  "qualityMetrics.critical": "严重",
  "qualityMetrics.major": "重要",
  "qualityMetrics.minor": "轻微",
  "qualityMetrics.inspectionTypeBreakdown": "检验类型分解",
  "qualityMetrics.inspectionsByType": "按类型和频率分类的质量检验",
  "qualityMetrics.incoming": "来料检验",
  "qualityMetrics.inProcess": "过程检验",
  "qualityMetrics.final": "最终检验",
  "qualityMetrics.productQualityMetrics": "产品质量指标",
  "qualityMetrics.qualityPerformanceByProduct": "按产品类别的质量绩效分析",
  "qualityMetrics.product": "产品",
  "qualityMetrics.inspections": "检验",
  "qualityMetrics.passRate": "通过率",
  "qualityMetrics.defectRate": "缺陷率",
  "qualityMetrics.unknownProduct": "未知产品",
  "qualityMetrics.recentInspections": "最近检验",
  "qualityMetrics.latestQualityActivity": "最新质量控制活动和检验结果",
  "qualityMetrics.inspection": "检验",
  "qualityMetrics.type": "类型",
  "qualityMetrics.status": "状态",
  "qualityMetrics.date": "日期",
  "qualityMetrics.noInspections": "暂无检验",
  "qualityMetrics.inspectionsWillAppear": "随着质量控制活动的执行，质量检验将会显示",
  "qualityMetrics.qualityTrendsAnalysis": "质量趋势分析",
  "qualityMetrics.historicalQualityPerformance": "历史质量绩效和改进跟踪",
  "qualityMetrics.noTrendData": "暂无趋势数据",
  "qualityMetrics.trendsWillAppear": "随着检验数据的积累，质量趋势将会显示",
  "qualityMetrics.realQualityDataIntegration": "真实质量数据集成",
  "qualityMetrics.dataSourceDescription": "此质量指标报告与您的综合质量控制系统集成，包括检验、缺陷跟踪、证书和生产工作流程集成。所有指标均基于符合法规合规标准的真实质量数据计算。",

  // ✅ NEW: Missing Quality Metrics Keys - Chinese
  "qualityMetrics.liveQualityData": "实时质量数据",
  "qualityMetrics.inspectionsCount": "{count}次检验",
  "qualityMetrics.overallPassRate": "总体通过率",
  "qualityMetrics.inspectionsPassed": "次检验通过",
  "qualityMetrics.defectRate": "缺陷率",
  "qualityMetrics.defectsIdentified": "个缺陷已识别",
  "qualityMetrics.pendingInspections": "待处理检验",
  "qualityMetrics.awaitingQualityReview": "等待质量审查",
  "qualityMetrics.certificateValidity": "证书有效性",
  "qualityMetrics.certificatesValid": "张证书有效",
  "qualityMetrics.inspectionStatusBreakdown": "检验状态分解",
  "qualityMetrics.qualityInspectionResultsDistribution": "质量检验结果分布",
  "qualityMetrics.passed": "通过",
  "qualityMetrics.failed": "失败",
  "qualityMetrics.pending": "待处理",
  "qualityMetrics.quarantined": "隔离",
  "qualityMetrics.defectSeverityAnalysis": "缺陷严重性分析",
  "qualityMetrics.breakdownDefectsBySeverity": "按严重程度分类的缺陷分解",
  "qualityMetrics.inspectionTypes": "检验类型",
  "qualityMetrics.productQuality": "产品质量",
  "qualityMetrics.qualityTrends": "质量趋势",
  "qualityMetrics.recentInspections": "最近检验",
  "qualityMetrics.qualityInspectionsByType": "按类型分类的质量检验",
  "qualityMetrics.performanceMetricsDifferentInspectionTypes": "不同检验类型的绩效指标",
  "qualityMetrics.inspectionType": "检验类型",
  "qualityMetrics.totalInspections": "总检验数",
  "qualityMetrics.performance": "绩效",
  "qualityMetrics.inspections": "次检验",

  // ✅ NEW: Inventory Intelligence Report - Chinese
  "inventoryIntelligence.title": "库存智能",
  "inventoryIntelligence.subtitle": "库存构成、补货建议和周转分析",
  "inventoryIntelligence.backToReports": "返回报告",
  "inventoryIntelligence.realTimeData": "实时数据",
  "inventoryIntelligence.totalStockLots": "总库存批次",
  "inventoryIntelligence.totalValue": "总价值",
  "inventoryIntelligence.lowStockItems": "低库存项目",
  "inventoryIntelligence.inventoryHealth": "库存健康度",
  "inventoryIntelligence.inventoryComposition": "库存构成",
  "inventoryIntelligence.stockByCategory": "按类别和类型的库存分布",
  "inventoryIntelligence.finishedGoods": "成品",
  "inventoryIntelligence.rawMaterials": "原材料",
  "inventoryIntelligence.overview": "概览",
  "inventoryIntelligence.analysis": "分析",
  "inventoryIntelligence.recommendations": "建议",
  "inventoryIntelligence.locations": "位置",
  "inventoryIntelligence.costDistribution": "成本分布",
  "inventoryIntelligence.inventoryValueByRange": "按成本范围的库存价值分布",
  "inventoryIntelligence.highValue": "高价值",
  "inventoryIntelligence.mediumValue": "中等价值",
  "inventoryIntelligence.lowValue": "低价值",
  "inventoryIntelligence.averageUnitCost": "平均单位成本",
  "inventoryIntelligence.stockLevelsAnalysis": "库存水平分析",
  "inventoryIntelligence.currentStockStatus": "当前库存状态和可用性分析",
  "inventoryIntelligence.item": "项目",
  "inventoryIntelligence.currentStock": "当前库存",
  "inventoryIntelligence.reorderPoint": "补货点",
  "inventoryIntelligence.status": "状态",
  "inventoryIntelligence.status.healthy": "健康",
  "inventoryIntelligence.status.low": "偏低",
  "inventoryIntelligence.status.critical": "严重",
  "inventoryIntelligence.unknownItem": "未知项目",
  "inventoryIntelligence.reorderRecommendations": "补货建议",
  "inventoryIntelligence.intelligentRestockingSuggestions": "基于使用模式的智能补货建议",
  "inventoryIntelligence.suggestedQuantity": "建议数量",
  "inventoryIntelligence.priority": "优先级",
  "inventoryIntelligence.priority.high": "高",
  "inventoryIntelligence.priority.medium": "中",
  "inventoryIntelligence.priority.low": "低",
  "inventoryIntelligence.noRecommendations": "目前无补货建议",
  "inventoryIntelligence.recommendationsWillAppear": "补货建议将基于库存水平和使用模式显示",
  "inventoryIntelligence.locationDistribution": "位置分布",
  "inventoryIntelligence.stockByLocation": "跨仓库位置的库存分布",
  "inventoryIntelligence.location": "位置",
  "inventoryIntelligence.items": "项目",
  "inventoryIntelligence.utilization": "利用率",
  "inventoryIntelligence.unknownLocation": "未知位置",
  "inventoryIntelligence.turnoverAnalysis": "周转分析",
  "inventoryIntelligence.inventoryTurnoverPerformance": "库存周转绩效和效率指标",
  "inventoryIntelligence.fastMoving": "快速流动",
  "inventoryIntelligence.slowMoving": "缓慢流动",
  "inventoryIntelligence.noTurnoverData": "暂无周转数据",
  "inventoryIntelligence.turnoverWillAppear": "随着库存流动数据的积累，周转分析将会显示",
  "inventoryIntelligence.realInventoryDataIntegration": "真实库存数据集成",
  "inventoryIntelligence.dataSourceDescription": "此库存智能报告与您的综合库存管理系统集成，包括库存批次、原材料、需求预测和采购计划。所有数据均来源于具有高级分析功能的真实库存交易，用于优化和决策支持。",

  // ✅ NEW: Missing Inventory Intelligence Keys - Chinese
  "inventoryIntelligence.liveInventoryData": "实时库存数据",
  "inventoryIntelligence.includesRawMaterials": "包含原材料",
  "inventoryIntelligence.units": "单位",
  "inventoryIntelligence.capacity": "容量",
  "inventoryIntelligence.totalInventoryValue": "总库存价值",
  "inventoryIntelligence.stockHealthScore": "库存健康评分",
  "inventoryIntelligence.activeLots": "个活跃批次",
  "inventoryIntelligence.lowStockAlerts": "低库存警报",
  "inventoryIntelligence.itemsBelowReorderPoint": "个项目低于补货点",
  "inventoryIntelligence.storageUtilization": "存储利用率",
  "inventoryIntelligence.inventoryComposition": "库存构成",
  "inventoryIntelligence.breakdownInventoryByTypeValue": "按类型和价值分类的库存分解",
  "inventoryIntelligence.finishedGoods": "成品",
  "inventoryIntelligence.rawMaterials": "原材料",
  "inventoryIntelligence.finishedGoodsValue": "成品价值",
  "inventoryIntelligence.rawMaterialsValue": "原材料价值",
  "inventoryIntelligence.costDistributionAnalysis": "成本分布分析",
  "inventoryIntelligence.inventoryValueDistributionCostTiers": "按成本层级的库存价值分布",
  "inventoryIntelligence.highValue": "高价值",
  "inventoryIntelligence.mediumValue": "中等价值",
  "inventoryIntelligence.lowValue": "低价值",
  "inventoryIntelligence.averageUnitCost": "平均单位成本",
  "inventoryIntelligence.stockLevels": "库存水平",
  "inventoryIntelligence.reorderAnalysis": "补货分析",
  "inventoryIntelligence.locationDistribution": "位置分布",
  "inventoryIntelligence.turnoverAnalysis": "周转分析",
  "inventoryIntelligence.stockLevelsByProduct": "按产品的库存水平",
  "inventoryIntelligence.currentInventoryLevelsValuesByProduct": "按产品的当前库存水平和价值",
  "inventoryIntelligence.product": "产品",
  "inventoryIntelligence.sku": "SKU",
  "inventoryIntelligence.quantity": "数量",
  "inventoryIntelligence.avgCost": "平均成本",
  "inventoryIntelligence.stockLots": "个库存批次",
  "inventoryIntelligence.locations": "个位置",
  "inventoryIntelligence.lots": "批次",
  "inventoryIntelligence.totalValue": "总价值",
  "inventoryIntelligence.intelligentReorderSuggestionsBasedStockDemand": "基于库存和需求模式的智能补货建议",
  "inventoryIntelligence.currentStock": "当前库存",
  "inventoryIntelligence.forecastDemand": "预测需求",
  "inventoryIntelligence.recommendation": "建议",
  "inventoryIntelligence.noReorderRecommendations": "目前无补货建议",
  "inventoryIntelligence.reorderRecommendationsWillAppear": "补货建议将基于库存水平和需求模式显示",
  "inventoryIntelligence.inventoryByLocation": "按位置的库存",
  "inventoryIntelligence.distributionInventoryAcrossStorageLocations": "跨存储位置的库存分布",
  "inventoryIntelligence.itemCount": "项目数量",
  "inventoryIntelligence.totalQuantity": "总数量",
  "inventoryIntelligence.utilization": "利用率",
  "inventoryIntelligence.unknownLocation": "未知位置",
  "inventoryIntelligence.noLocationData": "暂无位置数据",
  "inventoryIntelligence.locationDataWillAppear": "随着库存在存储位置间分布，位置数据将会显示",
  "inventoryIntelligence.inventoryTurnoverAnalysis": "库存周转分析",
  "inventoryIntelligence.productMovementTurnoverPerformance": "产品流动和周转绩效分析",
  "inventoryIntelligence.movementStatus": "流动状态",
  "inventoryIntelligence.good": "良好",
  "inventoryIntelligence.review": "需审查",
  "inventoryIntelligence.turnoverDataWillAppear": "随着库存流动模式的发展，周转数据将会显示",
  "inventoryIntelligence.inventoryIntelligenceIntegration": "库存智能集成",
  "inventoryIntelligence.costTrackingNotImplemented": "成本跟踪未实施 - 仅显示数量",
  "inventoryIntelligence.notAvailable": "不可用",
  "inventoryIntelligence.noTurnoverData": "暂无周转数据",

  // ✅ NEW: Business Intelligence Report - Chinese
  "businessIntelligence.title": "商业智能",
  "businessIntelligence.subtitle": "综合商业分析和战略洞察的执行仪表板",
  "businessIntelligence.backToReports": "返回报告",
  "businessIntelligence.liveBusinessData": "实时业务数据",
  "businessIntelligence.customersCount": "{count}个客户",
  "businessIntelligence.totalRevenue": "总收入",
  "businessIntelligence.activeContracts": "活跃合同",
  "businessIntelligence.customerBase": "客户群",
  "businessIntelligence.operationalEfficiency": "运营效率",
  "businessIntelligence.profitMargin": "利润率",
  "businessIntelligence.completionRate": "完成率",
  "businessIntelligence.suppliers": "个供应商",
  "businessIntelligence.of": "，共",
  "businessIntelligence.orders": "个订单",
  "businessIntelligence.financialPerformance": "财务绩效",
  "businessIntelligence.revenueExpensesProfitabilityAnalysis": "收入、支出和盈利能力分析",
  "businessIntelligence.revenue": "收入",
  "businessIntelligence.expenses": "支出",
  "businessIntelligence.netProfitMargin": "净利润率",
  "businessIntelligence.qualityOperations": "质量与运营",
  "businessIntelligence.qualityMetricsOperationalPerformance": "质量指标和运营绩效",
  "businessIntelligence.qualityScore": "质量评分",
  "businessIntelligence.efficiency": "效率",
  "businessIntelligence.qualityInspections": "质量检验",
  "businessIntelligence.passed": "次通过",
  "businessIntelligence.workOrders": "工单",
  "businessIntelligence.completed": "已完成",
  "businessIntelligence.failed": "失败",
  "businessIntelligence.inProgress": "进行中",
  "businessIntelligence.customerAnalytics": "客户分析",
  "businessIntelligence.contractPerformance": "合同绩效",
  "businessIntelligence.revenueTrends": "收入趋势",
  "businessIntelligence.operations": "运营",
  "businessIntelligence.topCustomersByRevenue": "按收入排名的顶级客户",
  "businessIntelligence.customerRelationshipRevenueAnalysis": "客户关系和收入分析",
  "businessIntelligence.customer": "客户",
  "businessIntelligence.contracts": "合同",
  "businessIntelligence.lastActivity": "最后活动",
  "businessIntelligence.status": "状态",
  "businessIntelligence.unknownCustomer": "未知客户",
  "businessIntelligence.active": "活跃",
  "businessIntelligence.noCustomerData": "暂无客户数据",
  "businessIntelligence.customerDataWillAppear": "随着业务关系的发展，客户分析将会显示",
  "businessIntelligence.contractPerformanceMetrics": "合同绩效指标",
  "businessIntelligence.salesPurchaseContractAnalytics": "销售和采购合同分析",
  "businessIntelligence.totalSalesContracts": "总销售合同",
  "businessIntelligence.completedContracts": "已完成合同",
  "businessIntelligence.avgContractValue": "平均合同价值",
  "businessIntelligence.contractCompletionRate": "合同完成率",
  "businessIntelligence.revenueTrendsLast6Months": "收入趋势（最近6个月）",
  "businessIntelligence.monthlyRevenuePerformanceGrowthAnalysis": "月度收入绩效和增长分析",
  "businessIntelligence.invoices": "张发票",
  "businessIntelligence.noRevenueTrendData": "暂无收入趋势数据",
  "businessIntelligence.revenueTrendsWillAppear": "随着发票数据的积累，收入趋势将会显示",
  "businessIntelligence.operationalPerformance": "运营绩效",
  "businessIntelligence.productionQualityLogisticsMetrics": "生产、质量和物流指标",
  "businessIntelligence.production": "生产",
  "businessIntelligence.totalWorkOrders": "总工单数",
  "businessIntelligence.quality": "质量",
  "businessIntelligence.inspections": "次检验",
  "businessIntelligence.inventory": "库存",
  "businessIntelligence.stockLots": "库存批次",
  "businessIntelligence.shipments": "次发货",
  "businessIntelligence.businessIntelligenceIntegration": "商业智能集成",
  "businessIntelligence.dataSourceDescription": "此商业智能报告集成了所有制造ERP模块的数据，包括销售/采购合同、财务管理、生产、质量控制、库存和发货。所有指标均基于真实业务数据计算，具有全面的跨模块分析功能，用于战略决策制定。",

  // Forecasting
  "forecasting.demandForecasting": "需求预测",
  "forecasting.manageForecastsScenarios": "管理需求预测和计划方案",
  "forecasting.backToPlanning": "返回计划",
  "forecasting.newForecast": "新建预测",
  "forecasting.filtersSearch": "筛选和搜索",
  "forecasting.filterSearchForecasts": "筛选和搜索需求预测",
  "forecasting.search": "搜索",
  "forecasting.searchForecasts": "搜索预测...",
  "forecasting.status": "状态",
  "forecasting.allStatuses": "所有状态",
  "forecasting.allStatusesSelect": "所有状态",
  "forecasting.method": "方法",
  "forecasting.allMethods": "所有方法",
  "forecasting.allMethodsSelect": "所有方法",
  "forecasting.pipelineAnalysis": "管道分析",
  "forecasting.manualEntry": "手动输入",
  "forecasting.historicalData": "历史数据",
  "forecasting.hybridMethod": "混合方法",
  "forecasting.confidence": "置信度",
  "forecasting.allLevels": "所有级别",
  "forecasting.allLevelsSelect": "所有级别",
  "forecasting.low": "低",
  "forecasting.medium": "中",
  "forecasting.high": "高",
  "forecasting.demandForecastsCount": "需求预测",
  "forecasting.allForecastsCompany": "您公司的所有需求预测",
  "forecasting.product": "产品",
  "forecasting.period": "期间",
  "forecasting.demand": "需求",
  "forecasting.profitMargin": "利润率",
  "forecasting.supplierPrefs": "供应商偏好",
  "forecasting.created": "创建时间",
  "forecasting.actions": "操作",
  "forecasting.units": "单位",
  "forecasting.noBomPrice": "无BOM/价格",
  "forecasting.auto": "自动",
  "forecasting.unknownSupplier": "未知供应商",
  "forecasting.noForecastsCreated": "未创建预测",
  "forecasting.startCreatingFirst": "开始创建您的第一个需求预测以启用物料计划",
  "forecasting.createFirstForecast": "创建第一个预测",

  // Forecasting Detail/View Page
  "forecasting.backToForecasts": "返回预测",
  "forecasting.edit": "编辑",
  "forecasting.forecastSummary": "预测摘要",
  "forecasting.quantity": "数量",
  "forecasting.notes": "备注",
  "forecasting.forecastProfitability": "预测盈利能力",
  "forecasting.profitMarginAnalysis": "利润率分析",
  "forecasting.profitMarginUnavailable": "利润率分析不可用",
  "forecasting.needsBomPrice": "产品需要配置BOM和销售价格",
  "forecasting.statusActions": "状态和操作",
  "forecasting.currentStatus": "当前状态",
  "forecasting.lastUpdated": "最后更新",
  "forecasting.createdBy": "创建者",
  "forecasting.procurementPlans": "采购计划",
  "forecasting.generatedPlans": "生成的计划",
  "forecasting.noProcurementPlans": "尚未生成采购计划",
  "forecasting.generatePlansDescription": "当此预测获得批准时，将自动生成采购计划",

  // Forecasting Edit Page
  "forecasting.backToForecast": "返回预测",
  "forecasting.editForecast": "编辑预测",
  "forecasting.forecastDetails": "预测详情",

  // Forecasting Create Page
  "forecasting.createDemandForecast": "创建需求预测",
  "forecasting.newDemandForecast": "新需求预测",

  // User Display
  "common.system": "系统",
  "common.user": "用户",

  // Demand Forecast Form
  "demandForecast.createTitle": "创建需求预测",
  "demandForecast.editTitle": "编辑需求预测",
  "demandForecast.description": "基于销售管道分析和历史数据生成需求预测",
  "demandForecast.productSelection": "产品选择",
  "demandForecast.selectProduct": "选择用于需求预测的产品",
  "demandForecast.product": "产品",
  "demandForecast.searchSelectProduct": "搜索并选择产品...",
  "demandForecast.selected": "已选择",
  "demandForecast.supplierOptional": "供应商（可选）",
  "demandForecast.autoSelectSupplier": "自动选择最佳供应商",
  "demandForecast.preferredSupplier": "首选供应商",
  "demandForecast.chooseSupplier": "选择特定供应商或让系统自动选择最佳选项",
  "demandForecast.forecastConfiguration": "预测配置",
  "demandForecast.configurePeriod": "配置预测期间和需求参数",
  "demandForecast.forecastPeriod": "预测期间",
  "demandForecast.selectPeriod": "选择期间",
  "demandForecast.forecastedDemand": "预测需求",
  "demandForecast.enterQuantity": "输入数量",
  "demandForecast.expectedDemand": "所选期间的预期需求数量",
  "demandForecast.confidenceLevel": "置信度",
  "demandForecast.lowConfidence": "60-70% 置信度",
  "demandForecast.mediumConfidence": "70-85% 置信度",
  "demandForecast.highConfidence": "85-95% 置信度",
  "demandForecast.forecastMethod": "预测方法",
  "demandForecast.pipelineAnalysis": "管道分析",
  "demandForecast.historicalData": "历史数据",
  "demandForecast.manualEntry": "手动输入",
  "demandForecast.hybridMethod": "混合方法",
  "demandForecast.advancedSettings": "高级设置",
  "demandForecast.seasonalityAdjustment": "季节性调整",
  "demandForecast.noAdjustment": "无调整",
  "demandForecast.applySeasonality": "应用季节性",
  "demandForecast.trendFactor": "趋势因子",
  "demandForecast.growthMultiplier": "增长倍数（1.0 = 无变化，1.2 = 20% 增长）",
  "demandForecast.additionalNotes": "附加备注",
  "demandForecast.addNotes": "添加任何附加备注或假设...",
  "demandForecast.cancel": "取消",
  "demandForecast.changesUpdate": "更改将更新现有预测",
  "demandForecast.saving": "保存中...",
  "demandForecast.createForecast": "创建预测",
  "demandForecast.updateForecast": "更新预测",
  "demandForecast.success": "成功",
  "demandForecast.createdSuccessfully": "创建成功",
  "demandForecast.updatedSuccessfully": "更新成功",
  "demandForecast.error": "错误",
  "demandForecast.failedToSave": "保存需求预测失败",

  // BOM (Bill of Materials)
  "bom.title": "物料清单",
  "bom.subtitle": "管理产品BOM和物料需求",
  "bom.manage_products": "管理产品",
  "bom.total_products": "总产品数",
  "bom.avg_materials_per_product": "平均每产品物料数",
  "bom.with_bom": "有BOM",
  "bom.total_materials": "总物料数",
  "bom.without_bom": "无BOM",
  "bom.need_bom_configuration": "需要BOM配置",
  "bom.total_value": "总价值",
  "bom.incomplete_boms": "不完整BOM",
  "bom.overview": "概览",
  "bom.view_and_manage_description": "查看和管理所有产品BOM",
  "bom.refresh": "刷新",
  "bom.search_products": "搜索产品...",
  "bom.all_products": "所有产品",
  "bom.add_product": "添加产品",
  "bom.product": "产品",
  "bom.bom_status": "BOM状态",
  "bom.materials": "物料",
  "bom.categories": "类别",
  "bom.material_cost": "物料成本",
  "bom.selling_price": "销售价格",
  "bom.profit": "利润",
  "bom.margin_percent": "利润率 %",
  "bom.last_updated": "最后更新",
  "bom.actions": "操作",
  "bom.complete": "完整",
  "bom.incomplete": "不完整",
  "bom.no_bom": "无BOM",
  "bom.complete_boms": "完整BOM",
  "bom.incomplete_boms_filter": "不完整BOM",
  "bom.view": "查看",
  "bom.edit": "编辑",
  "bom.delete": "删除",
  "bom.no_products_found": "未找到产品",
  "bom.no_products_message": "没有产品符合您的搜索条件",
  "bom.loading": "加载中...",
  "bom.error": "错误",
  "bom.failed_to_load": "加载BOM数据失败",

  // Work Orders
  "workorders.title": "工作订单",
  "workorders.subtitle": "跟踪工艺路线和操作进度。",
  "workorders.refresh": "刷新",
  "workorders.new_work_order": "新工作订单",
  "workorders.total_orders": "总订单数",
  "workorders.all_work_orders": "所有工作订单",
  "workorders.pending": "待处理",
  "workorders.awaiting_start": "等待开始",
  "workorders.in_progress": "进行中",
  "workorders.currently_active": "当前活跃",
  "workorders.completed": "已完成",
  "workorders.finished_orders": "已完成订单",
  "workorders.overdue": "逾期",
  "workorders.past_due_date": "超过截止日期",
  "workorders.high_priority": "高优先级",
  "workorders.urgent_orders": "紧急订单",
  "workorders.search_work_orders": "搜索工作订单...",
  "workorders.all_status": "所有状态",
  "workorders.all_contracts": "所有合同",
  "workorders.all_products": "所有产品",
  "workorders.work_orders": "工作订单",
  "workorders.found_work_orders": "找到的工作订单",
  "workorders.contract_work_order": "合同 / 工作订单",
  "workorders.product": "产品",
  "workorders.quantity": "数量",
  "workorders.due_date": "截止日期",
  "workorders.priority": "优先级",
  "workorders.notes": "备注",
  "workorders.actions": "操作",
  "workorders.status": "状态",
  "workorders.progress": "进度",
  "workorders.work_order": "工作订单",
  "workorders.work_orders_plural": "工作订单",
  "workorders.not_set": "未设置",
  "workorders.no_notes": "无备注",
  "workorders.normal": "普通",
  "workorders.high": "高",
  "workorders.urgent": "紧急",
  "workorders.low": "低",

  // Work Order Detail View
  "workorders.failed_to_load": "加载失败",
  "workorders.production_completed": "生产完成成功",
  "workorders.back_to_work_orders": "返回工作订单",
  "workorders.work_order_details": "工作订单详情",
  "workorders.complete_production": "完成生产",
  "workorders.edit": "编辑",
  "workorders.work_order_overview": "工作订单概览",
  "workorders.created": "创建时间",
  "workorders.sales_contract": "销售合同",
  "workorders.contract": "合同",
  "workorders.customer": "客户",
  "workorders.contract_date": "合同日期",
  "workorders.no_sales_contract_linked": "未关联销售合同",
  "workorders.product_details": "产品详情",
  "workorders.sku": "SKU",
  "workorders.name": "名称",
  "workorders.unit": "单位",
  "workorders.category": "类别",
  "workorders.quality_inspections": "质量检验",
  "workorders.no_quality_inspections": "暂无质量检验",
  "workorders.material_requirements": "物料需求",
  "workorders.raw_materials_needed": "生产所需原材料",
  "workorders.total_materials_required": "所需物料总数",
  "workorders.different_materials": "种不同物料",
  "workorders.no_material_requirements": "未找到物料需求",

  // Work Order Edit Page
  "workorders.back_to_view": "返回查看",
  "workorders.edit_work_order": "编辑工作订单",
  "workorders.cancel": "取消",
  "workorders.save_changes": "保存更改",
  "workorders.work_order_information": "工作订单信息",
  "workorders.edit_work_order_details": "编辑工作订单详情",
  "workorders.add_production_notes": "添加生产备注...",
  "workorders.work_order_updated": "工作订单更新成功",
  "workorders.failed_to_update": "更新工作订单失败",
  "workorders.work_order_number": "工作订单号",
  "workorders.sales_contract_number": "销售合同",
  "workorders.product_name": "产品",
  "workorders.customer_name": "客户",
  "workorders.quantity_required": "所需数量",
  "workorders.due_date_label": "截止日期",
  "workorders.priority_label": "优先级",
  "workorders.status_label": "状态",
  "workorders.production_notes": "生产备注",

  // Quality Control
  "quality.quality_control": "质量控制",
  "quality.comprehensive_quality_management": "综合质量管理系统",
  "quality.title": "质量控制",
  "quality.subtitle": "管理质量检验和证书",
  "quality.track_quality_inspections": "跟踪所有合同的质量检验",
  "quality.new_inspection": "新检验",
  "quality.total_inspections": "总检验数",
  "quality.across_contracts": "涉及 {count} 个合同",
  "quality.pending_review": "待审核",
  "quality.awaiting_inspection_start": "等待检验开始",
  "quality.in_progress": "进行中",
  "quality.currently_being_inspected": "正在检验中",
  "quality.passed": "通过",
  "quality.passed_quality_standards": "通过质量标准",
  "quality.failed": "失败",
  "quality.require_corrective_action": "需要纠正措施",
  "quality.archived": "已归档",
  "quality.archived_inspections": "已归档检验",
  "quality.quality_workflow": "质量工作流程",
  "quality.workflow_arrow": "→",
  "quality.workflow_or": "或",
  "quality.click_dashboard_cards": "点击仪表板卡片筛选检验",
  "quality.search_inspections": "搜索检验...",
  "quality.all_status": "所有状态",
  "quality.quality_inspections": "质量检验",
  "quality.found_inspections": "找到的检验",
  "quality.contract_inspection": "合同 / 检验",
  "quality.count_progress": "数量 / 进度",
  "quality.customer_work_order": "客户 / 工作订单",
  "quality.product": "产品",
  "quality.inspector": "检验员",
  "quality.status": "状态",
  "quality.actions": "操作",

  // Quality Analytics Dashboard
  "quality.error": "错误",
  "quality.failed_to_load_analytics": "加载分析数据失败",
  "quality.quality_analytics": "质量分析",
  "quality.advanced_quality_metrics": "高级质量指标和性能洞察",
  "quality.last_7_days": "最近7天",
  "quality.last_30_days": "最近30天",
  "quality.last_90_days": "最近90天",
  "quality.last_year": "最近一年",
  "quality.refresh": "刷新",
  "quality.pass_rate": "通过率",
  "quality.excellent": "优秀",
  "quality.good": "良好",
  "quality.fair": "一般",
  "quality.poor": "较差",
  "quality.defect_rate": "缺陷率",
  "quality.target": "< 2% 目标",
  "quality.first_pass_yield": "一次通过率",
  "quality.industry_benchmark": "85% 行业基准",
  "quality.quality_trends": "质量趋势",
  "quality.pass_rate_percent": "通过率 %",
  "quality.defect_rate_percent": "缺陷率 %",
  "quality.no_trend_data": "无趋势数据",
  "quality.defect_analysis": "缺陷分析",
  "quality.no_defect_data": "无缺陷数据",
  "quality.last_updated": "最后更新: {timestamp}",

  // Inventory Management
  "inventory.title": "库存管理",
  "inventory.subtitle": "管理库存水平、入库和出库操作",
  "inventory.overview.title": "概览",
  "inventory.overview.last_updated": "最后更新: {time}",

  // KPI Cards
  "inventory.kpi.units_ready_to_ship": "单位准备发货",
  "inventory.kpi.products": "产品",
  "inventory.kpi.low_stock": "低库存",
  "inventory.kpi.value": "价值: {amount}",
  "inventory.kpi.units_available_for_production": "单位可用于生产",
  "inventory.kpi.lots": "批次",
  "inventory.kpi.expiring": "即将过期",
  "inventory.kpi.combined_inventory_value": "综合库存价值",
  "inventory.kpi.locations": "位置",
  "inventory.kpi.transactions": "交易",
  "inventory.kpi.quality_alerts": "质量警报",

  // Navigation
  "inventory.raw_materials_nav.title": "原材料",
  "inventory.raw_materials_nav.subtitle": "管理原材料库存",
  "inventory.raw_materials_nav.button": "查看原材料",

  // Quick Actions
  "inventory.quick_actions.title": "快速操作",
  "inventory.quick_actions.subtitle": "常用库存操作",
  "inventory.quick_actions.receive": "接收",
  "inventory.quick_actions.ship": "发货",
  "inventory.quick_actions.transfer": "转移",
  "inventory.quick_actions.adjust": "调整",

  // Dialog Common
  "inventory.dialogs.product": "产品",
  "inventory.dialogs.quantity": "数量",
  "inventory.dialogs.location": "位置",
  "inventory.dialogs.location_optional": "位置（可选）",
  "inventory.dialogs.from_location": "源位置",
  "inventory.dialogs.to_location": "目标位置",
  "inventory.dialogs.reason": "原因",
  "inventory.dialogs.reference": "参考",
  "inventory.dialogs.notes": "备注",
  "inventory.dialogs.notes_required": "备注（必填）",
  "inventory.dialogs.cancel": "取消",
  "inventory.dialogs.processing": "处理中...",
  "inventory.dialogs.loading": "加载中...",
  "inventory.dialogs.select_product": "选择产品",
  "inventory.dialogs.select_product_with_stock": "选择有库存的产品",
  "inventory.dialogs.any_location": "任意位置",
  "inventory.dialogs.available": "可用",
  "inventory.dialogs.max": "最大",
  "inventory.dialogs.po_number": "采购订单号",
  "inventory.dialogs.order_number": "订单号",
  "inventory.dialogs.additional_notes": "附加备注",
  "inventory.dialogs.transfer_reason": "转移原因",
  "inventory.dialogs.adjustment_reason": "调整原因",

  // Receive Dialog
  "inventory.dialogs.receive.title": "接收库存",
  "inventory.dialogs.receive.description": "向库存添加新货物",
  "inventory.dialogs.receive.button": "接收库存",

  // Ship Dialog
  "inventory.dialogs.ship.title": "发货库存",
  "inventory.dialogs.ship.description": "从库存中移除货物",
  "inventory.dialogs.ship.button": "发货库存",

  // Transfer Dialog
  "inventory.dialogs.transfer.title": "转移库存",
  "inventory.dialogs.transfer.description": "在位置间移动库存",
  "inventory.dialogs.transfer.button": "转移库存",

  // Adjust Dialog
  "inventory.dialogs.adjust.title": "调整库存",
  "inventory.dialogs.adjust.description": "进行库存调整",
  "inventory.dialogs.adjust.button": "调整库存",

  // Stock Inventory Section
  "inventory.stock_inventory.title": "库存清单",
  "inventory.stock_inventory.subtitle": "项目",
  "inventory.stock_inventory.search_placeholder": "搜索产品...",
  "inventory.stock_inventory.all_quality": "所有质量",
  "inventory.stock_inventory.all_locations": "所有位置",
  "inventory.stock_inventory.total_products": "总产品数",
  "inventory.stock_inventory.total_lots": "总批次数",
  "inventory.stock_inventory.total_units": "总单位数",
  "inventory.stock_inventory.pending_quality": "待质检",

  // Table Headers
  "inventory.stock_inventory.table.product": "产品",
  "inventory.stock_inventory.table.total_quantity": "总数量",
  "inventory.stock_inventory.table.lots": "批次",
  "inventory.stock_inventory.table.locations": "位置",
  "inventory.stock_inventory.table.quality_status": "质量状态",
  "inventory.stock_inventory.table.actions": "操作",
  "inventory.stock_inventory.table.details": "详情",

  // Recent Activity
  "inventory.recent_activity.title": "最近活动",
  "inventory.recent_activity.subtitle": "最新库存交易",

  // Location Fallbacks
  "inventory.unknown_location": "未知位置",

  // ✅ NEW: Analytics Tab Translations (Chinese)
  "inventory.analytics.title": "综合分析",
  "inventory.analytics.subtitle": "高级库存洞察和指标",
  "inventory.analytics.view_analytics": "查看分析",
  "inventory.analytics.loading": "正在加载库存分析...",
  "inventory.analytics.stock_analytics": "库存分析",
  "inventory.analytics.location_alerts": "位置警报",
  "inventory.analytics.location_utilization": "位置利用率",
  "inventory.analytics.industrial_capacity": "工业产能管理",
  "inventory.analytics.flow_metrics_24h": "流量指标（24小时）",
  "inventory.analytics.workflow_analysis": "制造工作流分析",
  "inventory.analytics.stock_distribution": "库存分布",
  "inventory.analytics.stock_by_location": "按位置分类库存",
  "inventory.analytics.quality_status": "质量状态",
  "inventory.analytics.top_products_by_quantity": "按数量排名的热门产品",
  "inventory.analytics.finished_goods_analytics": "成品分析",
  "inventory.analytics.finished_goods_metrics": "成品库存实时指标",
  "inventory.analytics.finished_goods_only": "仅成品",
  "inventory.analytics.raw_materials_note": "原材料单独跟踪",
  "inventory.analytics.refresh_data": "刷新数据",
  "inventory.analytics.finished_goods_value": "成品价值",
  "inventory.analytics.fg_products": "成品产品",
  "inventory.analytics.finished_goods_units": "成品单位",
  "inventory.analytics.avg_unit": "平均单位",
  "inventory.analytics.unit": "单位",
  "inventory.analytics.low_stock_alerts": "低库存警报",
  "inventory.analytics.items_below": "低于10个单位的项目",
  "inventory.analytics.pending_inspections": "待检验",
  "inventory.analytics.quality_control_pending": "质量控制待处理",
  "inventory.analytics.category_breakdown": "类别细分",
  "inventory.analytics.inventory_by_location": "按位置分类库存",
  "inventory.analytics.total_value": "总价值",
  "inventory.analytics.recent_activity": "最近活动",
  "inventory.analytics.raw_materials": "原材料",
  "inventory.analytics.work_in_progress": "在制品",
  "inventory.analytics.finished_goods": "成品",
  "inventory.analytics.no_location_data": "无位置数据",
  "inventory.analytics.last_transactions": "最近 {count} 笔交易",
  "inventory.analytics.unknown_product": "未知产品",
  "inventory.analytics.no_recent_transactions": "无最近交易",
  "inventory.analytics.quality_status_distribution": "质量状态分布",
  "inventory.analytics.last_updated": "最后更新",
  "inventory.analytics.failed_to_load": "加载库存数据失败",
  "inventory.analytics.no_data_available": "无库存数据",
  "inventory.analytics.no_data_description": "未找到用于分析的库存数据。请先添加一些库存项目。",
  "inventory.analytics.professional_costing": "专业成本核算",

  // ✅ NEW: Discrepancy Tab Translations (Chinese)
  "inventory.discrepancy.title": "库存差异分析",
  "inventory.discrepancy.subtitle": "识别和解决库存不一致问题",
  "inventory.discrepancy.run_analysis": "运行分析",
  "inventory.discrepancy.analyzing": "分析中...",
  "inventory.discrepancy.analysis_complete": "分析完成",
  "inventory.discrepancy.analysis_results": "发现 {totalIssues} 个问题：{highRiskCount} 个高风险，{mediumRiskCount} 个中等风险",
  "inventory.discrepancy.analysis_failed": "分析失败",
  "inventory.discrepancy.unknown_product": "未知",
  "inventory.discrepancy.total_products": "总产品数",
  "inventory.discrepancy.high_risk": "高风险",
  "inventory.discrepancy.shortages": "短缺",
  "inventory.discrepancy.overages": "过量",
  "inventory.discrepancy.table.product": "产品",
  "inventory.discrepancy.table.current_stock": "当前库存",
  "inventory.discrepancy.table.expected_stock": "预期库存",
  "inventory.discrepancy.table.discrepancy": "差异",
  "inventory.discrepancy.table.risk_level": "风险等级",
  "inventory.discrepancy.table.recommended_action": "建议操作",
  "inventory.discrepancy.table.actions": "操作",
  "inventory.discrepancy.no_issues": "无问题",
  "inventory.discrepancy.shortage": "短缺",
  "inventory.discrepancy.overage": "过量",
  "inventory.discrepancy.view_details": "查看详情",
  "inventory.discrepancy.adjust": "调整",
  "inventory.discrepancy.click_run_analysis": "点击'运行分析'以识别库存差异",

  // ✅ NEW: Shipping Audit Translations (Chinese)
  "inventory.discrepancy.shipping_audit_title": "发货库存审计",
  "inventory.discrepancy.shipping_audit_subtitle": "验证已发货订单的库存减少情况",
  "inventory.discrepancy.run_audit": "运行审计",
  "inventory.discrepancy.click_run_audit": "点击'运行审计'以分析已发货订单",
  "inventory.discrepancy.auditing": "审计中...",
  "inventory.discrepancy.no_shipped_orders": "未找到已发货订单",
  "inventory.discrepancy.shipping_audit_complete": "发货审计完成",
  "inventory.discrepancy.audit_failed": "审计失败",
  "inventory.discrepancy.audit_summary": "审计摘要：",
  "inventory.discrepancy.total_shipped_orders": "已发货订单总数：",
  "inventory.discrepancy.inventory_issues": "库存问题：",
  "inventory.discrepancy.high_risk_orders": "高风险：",
  "inventory.discrepancy.orders_need_attention": "订单需要立即关注",
  "inventory.discrepancy.table.shipment": "发货单",
  "inventory.discrepancy.table.status": "状态",
  "inventory.discrepancy.table.total_qty": "总数量",
  "inventory.discrepancy.table.inventory_impact": "库存影响",
  "inventory.discrepancy.table.recommended_action": "建议操作",
  "inventory.discrepancy.items": "项目",
  "inventory.discrepancy.impact.correct": "正确",
  "inventory.discrepancy.impact.partial": "部分",
  "inventory.discrepancy.impact.missing": "缺失",
  "inventory.discrepancy.fix": "修复",
  "inventory.discrepancy.unknown_product": "未知",
}

type I18nContextValue = {
  locale: Locale
  setLocale: (l: Locale) => void
  t: (key: string, params?: Record<string, any>) => string
}

const I18nContext = createContext<I18nContextValue | null>(null)

export function I18nProvider({ children }: { children: React.ReactNode }) {
  const [locale, setLocaleState] = useState<Locale>("en")

  useEffect(() => {
    const saved = typeof window !== "undefined" ? (localStorage.getItem("locale") as Locale | null) : null
    if (saved === "en" || saved === "zh") setLocaleState(saved)
    else {
      const nav = typeof window !== "undefined" ? navigator.language.toLowerCase() : "en"
      if (nav.startsWith("zh")) setLocaleState("zh")
    }
  }, [])

  function setLocale(l: Locale) {
    setLocaleState(l)
    try {
      localStorage.setItem("locale", l)
    } catch { }
  }

  const dict = locale === "zh" ? zh : en

  const value = useMemo<I18nContextValue>(
    () => ({
      locale,
      setLocale,
      t: (key: string, params?: Record<string, any>) => {
        let text = dict[key] ?? key
        if (params) {
          Object.entries(params).forEach(([param, value]) => {
            text = text.replace(`{${param}}`, String(value))
          })
        }
        return text
      },
    }),
    [locale, dict],
  )

  return <I18nContext.Provider value={value}>{children}</I18nContext.Provider>
}

export function useI18n() {
  const ctx = useContext(I18nContext)
  if (!ctx) throw new Error("I18nProvider missing")
  return ctx
}
