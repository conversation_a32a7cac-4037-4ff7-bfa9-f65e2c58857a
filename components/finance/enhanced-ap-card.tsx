/**
 * Enhanced Accounts Payable Card - Professional Manufacturing ERP
 * Includes all missing critical fields for enterprise-grade AP management
 */

"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { useI18n } from "@/components/i18n-provider"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { safeJson } from "@/lib/safe-fetch"
import { Plus, Eye, Edit, FileText, Calendar, CreditCard, Search, Trash2, File } from "lucide-react"
import { PurchaseContractSelect } from "@/components/forms/purchase-contract-select"
import { SupplierSelect } from "@/components/forms/supplier-select"
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog"
import { InvoicePDFPreviewModal } from "@/components/invoice-pdf-preview-modal"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface APInvoice {
  id: string
  invoice_number: string
  supplier_id: string
  purchase_contract_id?: string
  amount: number
  paid: number
  currency: string
  status: 'draft' | 'received' | 'approved' | 'paid' | 'overdue' | 'cancelled' | 'deposit_paid' | 'partial_paid'
  invoice_date: string
  due_date: string
  payment_terms: string
  notes?: string
  aging_days: number
  supplier?: {
    id: string
    name: string
    email: string
  }
  purchaseContract?: {
    id: string
    contract_number: string
    title: string
  }
}

interface Supplier {
  id: string
  name: string
  email: string
}

interface PurchaseContract {
  id: string
  contract_number: string
  title: string
  supplier_id: string
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

const formatCurrency = (amount: number, currency: string = 'USD'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
  }).format(amount)
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString()
}

const getStatusColor = (status: string): string => {
  switch (status) {
    case 'paid': return 'bg-green-100 text-green-800'
    case 'deposit_paid': return 'bg-blue-100 text-blue-800'
    case 'partial_paid': return 'bg-yellow-100 text-yellow-800'
    case 'approved': return 'bg-purple-100 text-purple-800'
    case 'received': return 'bg-orange-100 text-orange-800'
    case 'overdue': return 'bg-red-100 text-red-800'
    case 'draft': return 'bg-gray-100 text-gray-800'
    case 'cancelled': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getAgingColor = (days: number): string => {
  if (days <= 30) return 'text-green-600'
  if (days <= 60) return 'text-yellow-600'
  return 'text-red-600'
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function EnhancedAPCard() {
  const { t } = useI18n()
  const { toast } = useSafeToast()

  const [apInvoices, setApInvoices] = useState<APInvoice[]>([])
  const [suppliers, setSuppliers] = useState<Supplier[]>([])
  const [purchaseContracts, setPurchaseContracts] = useState<PurchaseContract[]>([])
  const [loading, setLoading] = useState(true)

  // Form state
  const [formData, setFormData] = useState({
    invoice_number: '',
    supplier_id: '',
    purchase_contract_id: '',
    amount: '',
    currency: 'USD',
    status: 'received',
    invoice_date: new Date().toISOString().split('T')[0],
    due_date: '',
    payment_terms: 'TT',
    notes: ''
  })

  // State for confirmation dialogs and PDF preview
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean
    invoiceId: string
    invoiceNumber: string
  }>({
    isOpen: false,
    invoiceId: '',
    invoiceNumber: ''
  })

  const [pdfPreview, setPdfPreview] = useState<{
    isOpen: boolean
    invoiceId: string
    invoiceNumber: string
  }>({
    isOpen: false,
    invoiceId: '',
    invoiceNumber: ''
  })

  // Auto-generate invoice number
  async function generateInvoiceNumber() {
    try {
      const response = await fetch('/api/finance/ap/generate-number')
      if (response.ok) {
        const data = await response.json()
        setFormData(prev => ({ ...prev, invoice_number: data.number }))
        console.log("Generated AP invoice number:", data.number)
      } else {
        // Fallback to client-side generation
        const timestamp = Date.now().toString().slice(-6)
        const fallbackNumber = `BILL-${new Date().getFullYear()}-${timestamp}`
        setFormData(prev => ({ ...prev, invoice_number: fallbackNumber }))
        console.log("Using fallback AP invoice number:", fallbackNumber)
      }
    } catch (error) {
      console.error("Failed to generate invoice number:", error)
      // Fallback to client-side generation
      const timestamp = Date.now().toString().slice(-6)
      const fallbackNumber = `BILL-${new Date().getFullYear()}-${timestamp}`
      setFormData(prev => ({ ...prev, invoice_number: fallbackNumber }))
      console.log("Using fallback AP invoice number after error:", fallbackNumber)
    }
  }

  // Load data and generate invoice number
  useEffect(() => {
    loadData()
    generateInvoiceNumber()
  }, [])

  // Calculate contract total amount
  function calculateContractTotal(contract: any): number {
    if (!contract?.items || !Array.isArray(contract.items)) return 0

    return contract.items.reduce((sum: number, item: any) => {
      const price = parseFloat(item.price || '0')
      const qty = parseInt(item.qty || '0')
      return sum + (price * qty)
    }, 0)
  }

  // Handle contract selection and auto-populate supplier + amount
  function handleContractSelection(contract: any) {
    if (!contract) {
      setFormData(prev => ({
        ...prev,
        purchase_contract_id: "",
        supplier_id: "",
        amount: "",
        currency: "USD"
      }))
      return
    }

    // Calculate contract total
    const contractTotal = calculateContractTotal(contract)

    // Auto-populate supplier, amount, and currency from selected contract
    setFormData(prev => ({
      ...prev,
      purchase_contract_id: contract.id,
      supplier_id: contract.supplier?.id || "",
      amount: contractTotal > 0 ? contractTotal.toString() : "",
      currency: contract.currency || "USD"
    }))

    console.log("Auto-populated from contract:", {
      contractId: contract.id,
      supplierId: contract.supplier?.id,
      contractNumber: contract.number,
      supplierName: contract.supplier?.name,
      contractTotal,
      currency: contract.currency
    })

    toast({
      title: "Contract Selected",
      description: `Auto-populated: ${contract.supplier?.name || 'Unknown'} • ${contract.currency || 'USD'} ${contractTotal.toLocaleString()}`,
    })
  }

  async function loadData() {
    try {
      const [apResponse, suppliersResponse, contractsResponse] = await Promise.all([
        fetch("/api/finance/ap"),
        fetch("/api/suppliers"),
        fetch("/api/contracts/purchase")
      ])

      // Handle API responses properly
      const apData = apResponse.ok ? await apResponse.json() : { success: false, data: [] }
      const suppliersData = suppliersResponse.ok ? await suppliersResponse.json() : { success: false, data: [] }
      const contractsData = contractsResponse.ok ? await contractsResponse.json() : { success: false, data: [] }

      // Extract data from API response format { success: true, data: [...] }
      const apArray = Array.isArray(apData.data) ? apData.data : (Array.isArray(apData) ? apData : [])
      const suppliersArray = Array.isArray(suppliersData.data) ? suppliersData.data : (Array.isArray(suppliersData) ? suppliersData : [])
      const contractsArray = Array.isArray(contractsData.data) ? contractsData.data : (Array.isArray(contractsData) ? contractsData : [])

      setApInvoices(apArray)
      setSuppliers(suppliersArray)
      setPurchaseContracts(contractsArray)

      // Debug logging
      console.log("AP Data loaded:", {
        apInvoices: apArray.length,
        suppliers: suppliersArray.length,
        allContracts: contractsArray.length,
        contractStatuses: contractsArray.map((c: any) => c.status),
        apiResponses: {
          ap: { success: apData.success, dataType: typeof apData.data },
          suppliers: { success: suppliersData.success, dataType: typeof suppliersData.data },
          contracts: { success: contractsData.success, dataType: typeof contractsData.data }
        }
      })
    } catch (error) {
      console.error("Failed to load AP data:", error)
      toast({
        title: "Error",
        description: "Failed to load AP data",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault()

    try {
      // Transform form data to match API expectations
      const apiData = {
        number: formData.invoice_number,
        supplier_id: formData.supplier_id,
        purchase_contract_id: formData.purchase_contract_id || undefined,
        amount: parseFloat(formData.amount),
        currency: formData.currency,
        date: formData.invoice_date,
        due_date: formData.due_date || undefined,
        payment_terms: formData.payment_terms,
        notes: formData.notes || undefined,
      }

      console.log("Submitting AP invoice data:", apiData)

      const response = await fetch("/api/finance/ap", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(apiData)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error("AP invoice creation failed:", errorData)
        throw new Error(errorData.message || "Failed to create AP invoice")
      }

      const result = await response.json()
      console.log("AP invoice created successfully:", result)

      toast({
        title: "Success",
        description: "AP invoice created successfully"
      })

      // Reset form and regenerate invoice number
      setFormData({
        invoice_number: '',
        supplier_id: '',
        purchase_contract_id: '',
        amount: '',
        currency: 'USD',
        status: 'received',
        invoice_date: new Date().toISOString().split('T')[0],
        due_date: '',
        payment_terms: 'TT',
        notes: ''
      })

      // Generate new invoice number for next invoice
      generateInvoiceNumber()
      loadData()
    } catch (error) {
      console.error("AP invoice submission error:", error)
      toast({
        title: "Error",
        description: "Failed to create AP invoice",
        variant: "destructive"
      })
    }
  }

  // Handler functions for delete and preview
  const handleDelete = (invoice: APInvoice) => {
    setDeleteConfirmation({
      isOpen: true,
      invoiceId: invoice.id,
      invoiceNumber: invoice.number || invoice.id?.slice(-8) || 'N/A'
    })
  }

  const handlePreview = (invoice: APInvoice) => {
    setPdfPreview({
      isOpen: true,
      invoiceId: invoice.id,
      invoiceNumber: invoice.number || invoice.id?.slice(-8) || 'N/A'
    })
  }

  const confirmDelete = async () => {
    try {
      const response = await fetch(`/api/finance/ap/${deleteConfirmation.invoiceId}`, {
        method: "DELETE"
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || 'Failed to delete invoice')
      }

      toast({
        title: "Success",
        description: "AP invoice deleted successfully",
        variant: "default"
      })

      // Close the confirmation dialog
      setDeleteConfirmation({
        isOpen: false,
        invoiceId: '',
        invoiceNumber: ''
      })

      // Reload data
      await loadData()
    } catch (error) {
      console.error('Delete AP invoice error:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete invoice",
        variant: "destructive"
      })
    }
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="h-5 w-5 text-red-600" />
          {t("finance.ap.title")}
        </CardTitle>
        <CardDescription>
          {t("finance.ap.description")}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Enhanced Create Form */}
        <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 border rounded-lg">
          <div>
            <Label htmlFor="invoice_number">{t("finance.ap.invoiceNumber")}</Label>
            <Input
              id="invoice_number"
              value={formData.invoice_number}
              onChange={(e) => setFormData(prev => ({ ...prev, invoice_number: e.target.value }))}
              placeholder="BILL-2024-001"
              required
            />
          </div>

          <div>
            <Label htmlFor="purchase_contract">{t("finance.ap.purchaseContract")}</Label>
            <PurchaseContractSelect
              purchaseContracts={purchaseContracts}
              value={formData.purchase_contract_id}
              onValueChange={(value) => setFormData(prev => ({ ...prev, purchase_contract_id: value }))}
              onContractSelected={handleContractSelection}
              placeholder={t("finance.ap.searchPlaceholder")}
            />
          </div>

          <div>
            <Label htmlFor="supplier">{t("finance.ap.supplier")}</Label>
            <SupplierSelect
              suppliers={suppliers}
              value={formData.supplier_id}
              onValueChange={(value) => setFormData(prev => ({ ...prev, supplier_id: value }))}
              placeholder={t("finance.ap.supplierPlaceholder")}
            />
          </div>

          <div>
            <Label htmlFor="amount">{t("finance.ap.amount")}</Label>
            {formData.purchase_contract_id && (() => {
              const selectedContract = purchaseContracts.find(c => c.id === formData.purchase_contract_id)
              const contractTotal = selectedContract ? calculateContractTotal(selectedContract) : 0
              return contractTotal > 0 ? (
                <div className="text-sm text-muted-foreground mb-2 p-2 bg-blue-50 rounded-md border">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    <span>Contract Total: <strong>{selectedContract?.currency || 'USD'} {contractTotal.toLocaleString()}</strong></span>
                  </div>
                  <div className="text-xs mt-1">
                    You can invoice the full amount or enter a partial amount for deposits/progress billing
                  </div>
                </div>
              ) : null
            })()}
            <Input
              id="amount"
              type="number"
              step="0.01"
              value={formData.amount}
              onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
              placeholder="800.00"
              required
            />
          </div>

          <div>
            <Label htmlFor="currency">{t("finance.ap.currency")}</Label>
            <Select
              value={formData.currency}
              onValueChange={(value) => setFormData(prev => ({ ...prev, currency: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="USD">USD</SelectItem>
                <SelectItem value="EUR">EUR</SelectItem>
                <SelectItem value="CNY">CNY</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="invoice_date">{t("finance.ap.invoiceDate")}</Label>
            <Input
              id="invoice_date"
              type="date"
              value={formData.invoice_date}
              onChange={(e) => setFormData(prev => ({ ...prev, invoice_date: e.target.value }))}
              required
            />
          </div>

          <div>
            <Label htmlFor="due_date">{t("finance.ap.dueDate")}</Label>
            <Input
              id="due_date"
              type="date"
              value={formData.due_date}
              onChange={(e) => setFormData(prev => ({ ...prev, due_date: e.target.value }))}
              required
            />
          </div>

          <div>
            <Label htmlFor="payment_terms">{t("finance.ap.paymentTerms")}</Label>
            <Select
              value={formData.payment_terms}
              onValueChange={(value) => setFormData(prev => ({ ...prev, payment_terms: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="TT">TT (Telegraphic Transfer)</SelectItem>
                <SelectItem value="DP">DP (Documents against Payment)</SelectItem>
                <SelectItem value="LC">LC (Letter of Credit)</SelectItem>
                <SelectItem value="Deposit">Deposit (Advance Payment)</SelectItem>
                <SelectItem value="30% Deposit + 70% TT">30% Deposit + 70% TT</SelectItem>
                <SelectItem value="50% Deposit + 50% LC">50% Deposit + 50% LC</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="md:col-span-2 lg:col-span-4">
            <Button type="submit" className="w-full">
              <Plus className="mr-2 h-4 w-4" />
              {t("finance.ap.createInvoice")}
            </Button>
          </div>
        </form>

        {/* Enhanced AP Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t("finance.ap.invoiceNumber")}</TableHead>
                <TableHead>{t("finance.ap.supplier")}</TableHead>
                <TableHead>{t("finance.ap.contract")}</TableHead>
                <TableHead>{t("finance.ap.amount")}</TableHead>
                <TableHead>{t("finance.ap.paid")}</TableHead>
                <TableHead>{t("finance.ap.status")}</TableHead>
                <TableHead>{t("finance.ap.dueDate")}</TableHead>
                <TableHead>{t("finance.ap.aging")}</TableHead>
                <TableHead>{t("common.actions")}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {apInvoices.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} className="text-center text-muted-foreground">
                    {t("finance.ap.noInvoices")}
                  </TableCell>
                </TableRow>
              ) : (
                apInvoices.map((invoice) => (
                  <TableRow key={invoice.id}>
                    <TableCell className="font-medium">{invoice.number || invoice.id?.slice(-8) || 'N/A'}</TableCell>
                    <TableCell>{invoice.supplier?.name || 'Unknown'}</TableCell>
                    <TableCell>
                      {invoice.purchaseContract ? (
                        <span className="text-sm text-blue-600">
                          {invoice.purchaseContract.contract_number || invoice.purchaseContract.number}
                        </span>
                      ) : invoice.purchase_contract_id ? (
                        <span className="text-sm text-orange-600">Contract ID: {invoice.purchase_contract_id.slice(-8)}</span>
                      ) : (
                        <span className="text-sm text-gray-400">No Contract</span>
                      )}
                    </TableCell>
                    <TableCell>{formatCurrency(invoice.amount, invoice.currency)}</TableCell>
                    <TableCell>{formatCurrency(invoice.paid, invoice.currency)}</TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(invoice.status)}>
                        {invoice.status.toUpperCase()}
                      </Badge>
                    </TableCell>
                    <TableCell>{formatDate(invoice.due_date)}</TableCell>
                    <TableCell>
                      <span className={getAgingColor(invoice.aging_days)}>
                        {invoice.aging_days} days
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Button variant="ghost" size="sm" asChild title={t("finance.ap.viewInvoice")}>
                          <Link href={`/finance/ap/${invoice.id}`}>
                            <Eye className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button variant="ghost" size="sm" asChild title={t("finance.ap.editInvoice")}>
                          <Link href={`/finance/ap/${invoice.id}/edit`}>
                            <Edit className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handlePreview(invoice)}
                          title={t("finance.ap.generatePDF")}
                        >
                          <File className="h-4 w-4" />
                        </Button>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(invoice)}
                          title={t("finance.ap.deleteInvoice")}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>

      {/* Confirmation Dialog for Delete Actions */}
      <ConfirmationDialog
        open={deleteConfirmation.isOpen}
        onOpenChange={(open) => setDeleteConfirmation(prev => ({ ...prev, isOpen: open }))}
        onConfirm={confirmDelete}
        title={t("finance.ap.deleteConfirmTitle")}
        description={`Are you sure you want to delete AP invoice "${deleteConfirmation.invoiceNumber}"? This action cannot be undone.`}
        confirmText={t("finance.ap.deleteConfirmText")}
        cancelText="Cancel"
        variant="destructive"
      />

      {/* PDF Preview Modal */}
      <InvoicePDFPreviewModal
        isOpen={pdfPreview.isOpen}
        onClose={() => setPdfPreview(prev => ({ ...prev, isOpen: false }))}
        invoiceId={pdfPreview.invoiceId}
        invoiceNumber={pdfPreview.invoiceNumber}
        invoiceType="ap"
      />
    </Card>
  )
}
