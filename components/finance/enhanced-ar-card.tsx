/**
 * Enhanced Accounts Receivable Card - Professional Manufacturing ERP
 * Includes all missing critical fields for enterprise-grade AR management
 */

"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { useI18n } from "@/components/i18n-provider"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { safeJson } from "@/lib/safe-fetch"
import { Plus, Eye, Edit, FileText, Calendar, DollarSign, Search, Trash2, File } from "lucide-react"
import { SalesContractSelect } from "@/components/forms/sales-contract-select"
import { CustomerSelect } from "@/components/forms/customer-select"
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog"
import { InvoicePDFPreviewModal } from "@/components/invoice-pdf-preview-modal"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface ARInvoice {
  id: string
  number: string  // Fixed: Match database schema
  customer_id: string
  sales_contract_id?: string
  amount: string  // Database stores as string
  received: string  // Database stores as string
  currency: string
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled' | 'deposit_received' | 'partial_paid'
  date: string  // Fixed: Match database schema
  due_date: string
  payment_terms: string
  notes?: string
  created_at?: string
  updated_at?: string
  customer?: {
    id: string
    name: string
    email: string
  }
  salesContract?: {
    id: string
    contract_number: string
    title: string
  }
}

interface Customer {
  id: string
  name: string
  email: string
}

interface SalesContract {
  id: string
  contract_number: string
  title: string
  customer_id: string
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

const formatCurrency = (amount: number, currency: string = 'USD'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
  }).format(amount)
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString()
}

const calculateAgingDays = (dateString: string): number => {
  if (!dateString) return 0
  const invoiceDate = new Date(dateString)
  const today = new Date()
  const diffTime = today.getTime() - invoiceDate.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return Math.max(0, diffDays)
}

const getStatusColor = (status: string): string => {
  switch (status) {
    case 'paid': return 'bg-green-100 text-green-800'
    case 'deposit_received': return 'bg-blue-100 text-blue-800'
    case 'partial_paid': return 'bg-yellow-100 text-yellow-800'
    case 'sent': return 'bg-purple-100 text-purple-800'
    case 'overdue': return 'bg-red-100 text-red-800'
    case 'draft': return 'bg-gray-100 text-gray-800'
    case 'cancelled': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getAgingColor = (days: number): string => {
  if (days <= 30) return 'text-green-600'
  if (days <= 60) return 'text-yellow-600'
  return 'text-red-600'
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function EnhancedARCard() {
  const { t } = useI18n()
  const { toast } = useSafeToast()

  const [arInvoices, setArInvoices] = useState<ARInvoice[]>([])
  const [customers, setCustomers] = useState<Customer[]>([])
  const [salesContracts, setSalesContracts] = useState<SalesContract[]>([])
  const [loading, setLoading] = useState(true)

  // Form state
  const [formData, setFormData] = useState({
    invoice_number: '',
    customer_id: '',
    sales_contract_id: '',
    amount: '',
    currency: 'USD',
    status: 'draft',
    invoice_date: new Date().toISOString().split('T')[0],
    due_date: '',
    payment_terms: 'TT',
    notes: ''
  })

  // State for confirmation dialogs and PDF preview
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean
    invoiceId: string
    invoiceNumber: string
  }>({
    isOpen: false,
    invoiceId: '',
    invoiceNumber: ''
  })

  const [pdfPreview, setPdfPreview] = useState<{
    isOpen: boolean
    invoiceId: string
    invoiceNumber: string
  }>({
    isOpen: false,
    invoiceId: '',
    invoiceNumber: ''
  })

  // Auto-generate invoice number
  async function generateInvoiceNumber() {
    try {
      const response = await fetch('/api/finance/ar/generate-number')
      if (response.ok) {
        const data = await response.json()
        setFormData(prev => ({ ...prev, invoice_number: data.number }))
        console.log("Generated AR invoice number:", data.number)
      } else {
        // Fallback to client-side generation
        const timestamp = Date.now().toString().slice(-6)
        const fallbackNumber = `INV-${new Date().getFullYear()}-${timestamp}`
        setFormData(prev => ({ ...prev, invoice_number: fallbackNumber }))
        console.log("Using fallback AR invoice number:", fallbackNumber)
      }
    } catch (error) {
      console.error("Failed to generate invoice number:", error)
      // Fallback to client-side generation
      const timestamp = Date.now().toString().slice(-6)
      const fallbackNumber = `INV-${new Date().getFullYear()}-${timestamp}`
      setFormData(prev => ({ ...prev, invoice_number: fallbackNumber }))
      console.log("Using fallback AR invoice number after error:", fallbackNumber)
    }
  }

  // Load data and generate invoice number
  useEffect(() => {
    loadData()
    generateInvoiceNumber()
  }, [])

  // Calculate contract total amount
  function calculateContractTotal(contract: any): number {
    if (!contract?.items || !Array.isArray(contract.items)) return 0

    return contract.items.reduce((sum: number, item: any) => {
      const price = parseFloat(item.price || '0')
      const qty = parseInt(item.qty || '0')
      return sum + (price * qty)
    }, 0)
  }

  // Handle contract selection and auto-populate customer + amount
  function handleContractSelection(contract: any) {
    if (!contract) {
      setFormData(prev => ({
        ...prev,
        sales_contract_id: "",
        customer_id: "",
        amount: "",
        currency: "USD"
      }))
      return
    }

    // Calculate contract total
    const contractTotal = calculateContractTotal(contract)

    // Auto-populate customer, amount, and currency from selected contract
    setFormData(prev => ({
      ...prev,
      sales_contract_id: contract.id,
      customer_id: contract.customer?.id || "",
      amount: contractTotal > 0 ? contractTotal.toString() : "",
      currency: contract.currency || "USD"
    }))

    console.log("Auto-populated from contract:", {
      contractId: contract.id,
      customerId: contract.customer?.id,
      contractNumber: contract.number,
      customerName: contract.customer?.name,
      contractTotal,
      currency: contract.currency
    })

    toast({
      title: t("finance.ar.contractSelected"),
      description: `Auto-populated: ${contract.customer?.name || 'Unknown'} • ${contract.currency || 'USD'} ${contractTotal.toLocaleString()}`,
    })
  }

  async function loadData() {
    try {
      const [arResponse, customersResponse, contractsResponse] = await Promise.all([
        fetch("/api/finance/ar"),
        fetch("/api/customers"),
        fetch("/api/contracts/sales")
      ])

      // Handle API responses properly
      const arData = arResponse.ok ? await arResponse.json() : { success: false, data: [] }
      const customersData = customersResponse.ok ? await customersResponse.json() : { success: false, data: [] }
      const contractsData = contractsResponse.ok ? await contractsResponse.json() : { success: false, data: [] }

      // Extract data from API response format { success: true, data: [...] }
      const arArray = Array.isArray(arData.data) ? arData.data : (Array.isArray(arData) ? arData : [])
      const customersArray = Array.isArray(customersData.data) ? customersData.data : (Array.isArray(customersData) ? customersData : [])
      const contractsArray = Array.isArray(contractsData.data) ? contractsData.data : (Array.isArray(contractsData) ? contractsData : [])

      setArInvoices(arArray)
      setCustomers(customersArray)
      setSalesContracts(contractsArray)

      // Debug logging
      console.log("AR Data loaded:", {
        arInvoices: arArray.length,
        customers: customersArray.length,
        allContracts: contractsArray.length,
        contractStatuses: contractsArray.map((c: any) => c.status),
        firstARInvoice: arArray[0],
        arInvoiceFields: arArray[0] ? Object.keys(arArray[0]) : [],
        apiResponses: {
          ar: { success: arData.success, dataType: typeof arData.data },
          customers: { success: customersData.success, dataType: typeof customersData.data },
          contracts: { success: contractsData.success, dataType: typeof contractsData.data }
        }
      })
    } catch (error) {
      console.error("Failed to load AR data:", error)
      toast({
        title: "Error",
        description: "Failed to load AR data",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault()

    try {
      // Transform form data to match API expectations
      const apiData = {
        number: formData.invoice_number,
        customer_id: formData.customer_id,
        sales_contract_id: formData.sales_contract_id || undefined,
        amount: parseFloat(formData.amount),
        currency: formData.currency,
        date: formData.invoice_date,
        due_date: formData.due_date || undefined,
        payment_terms: formData.payment_terms,
        notes: formData.notes || undefined,
      }

      console.log("Submitting AR invoice data:", apiData)

      const response = await fetch("/api/finance/ar", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(apiData)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error("AR invoice creation failed:", errorData)
        throw new Error(errorData.message || "Failed to create AR invoice")
      }

      const result = await response.json()
      console.log("AR invoice created successfully:", result)

      toast({
        title: "Success",
        description: "AR invoice created successfully"
      })

      // Reset form and regenerate invoice number
      setFormData({
        invoice_number: '',
        customer_id: '',
        sales_contract_id: '',
        amount: '',
        currency: 'USD',
        status: 'draft',
        invoice_date: new Date().toISOString().split('T')[0],
        due_date: '',
        payment_terms: 'TT',
        notes: ''
      })

      // Generate new invoice number for next invoice
      generateInvoiceNumber()
      loadData()
    } catch (error) {
      console.error("AR invoice submission error:", error)
      toast({
        title: "Error",
        description: "Failed to create AR invoice",
        variant: "destructive"
      })
    }
  }

  // Handler functions for delete and preview
  const handleDelete = (invoice: ARInvoice) => {
    setDeleteConfirmation({
      isOpen: true,
      invoiceId: invoice.id,
      invoiceNumber: invoice.number || invoice.id?.slice(-8) || 'N/A'
    })
  }

  const handlePreview = (invoice: ARInvoice) => {
    setPdfPreview({
      isOpen: true,
      invoiceId: invoice.id,
      invoiceNumber: invoice.number || invoice.id?.slice(-8) || 'N/A'
    })
  }

  const confirmDelete = async () => {
    try {
      const response = await fetch(`/api/finance/ar/${deleteConfirmation.invoiceId}`, {
        method: "DELETE"
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || 'Failed to delete invoice')
      }

      toast({
        title: "Success",
        description: "AR invoice deleted successfully",
        variant: "default"
      })

      // Close the confirmation dialog
      setDeleteConfirmation({
        isOpen: false,
        invoiceId: '',
        invoiceNumber: ''
      })

      // Reload data
      await loadData()
    } catch (error) {
      console.error('Delete AR invoice error:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete invoice",
        variant: "destructive"
      })
    }
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="h-5 w-5 text-green-600" />
          {t("finance.ar.title")}
        </CardTitle>
        <CardDescription>
          {t("finance.ar.description")}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Enhanced Create Form */}
        <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 border rounded-lg">
          <div>
            <Label htmlFor="invoice_number">{t("finance.ar.invoiceNumber")}</Label>
            <Input
              id="invoice_number"
              value={formData.invoice_number}
              onChange={(e) => setFormData(prev => ({ ...prev, invoice_number: e.target.value }))}
              placeholder="INV-2024-001"
              required
            />
          </div>

          <div>
            <Label htmlFor="sales_contract">{t("finance.ar.salesContract")}</Label>
            <SalesContractSelect
              salesContracts={salesContracts}
              value={formData.sales_contract_id}
              onValueChange={(value) => setFormData(prev => ({ ...prev, sales_contract_id: value }))}
              onContractSelected={handleContractSelection}
              placeholder={t("finance.ar.searchPlaceholder")}
            />
          </div>

          <div>
            <Label htmlFor="customer">{t("finance.ar.customer")}</Label>
            <CustomerSelect
              customers={customers}
              value={formData.customer_id}
              onValueChange={(value) => setFormData(prev => ({ ...prev, customer_id: value }))}
              placeholder={t("finance.ar.customerPlaceholder")}
            />
          </div>

          <div>
            <Label htmlFor="amount">{t("finance.ar.amount")}</Label>
            {formData.sales_contract_id && (() => {
              const selectedContract = salesContracts.find(c => c.id === formData.sales_contract_id)
              const contractTotal = selectedContract ? calculateContractTotal(selectedContract) : 0
              return contractTotal > 0 ? (
                <div className="text-sm text-muted-foreground mb-2 p-2 bg-blue-50 rounded-md border">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    <span>{t("finance.ar.contractTotal")}: <strong>{selectedContract?.currency || 'USD'} {contractTotal.toLocaleString()}</strong></span>
                  </div>
                  <div className="text-xs mt-1">
                    You can invoice the full amount or enter a partial amount for deposits/progress billing
                  </div>
                </div>
              ) : null
            })()}
            <Input
              id="amount"
              type="number"
              step="0.01"
              value={formData.amount}
              onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
              placeholder="1000.00"
              required
            />
          </div>

          <div>
            <Label htmlFor="currency">{t("finance.ar.currency")}</Label>
            <Select
              value={formData.currency}
              onValueChange={(value) => setFormData(prev => ({ ...prev, currency: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="USD">USD</SelectItem>
                <SelectItem value="EUR">EUR</SelectItem>
                <SelectItem value="CNY">CNY</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="invoice_date">{t("finance.ar.invoiceDate")}</Label>
            <Input
              id="invoice_date"
              type="date"
              value={formData.invoice_date}
              onChange={(e) => setFormData(prev => ({ ...prev, invoice_date: e.target.value }))}
              required
            />
          </div>

          <div>
            <Label htmlFor="due_date">{t("finance.ar.dueDate")}</Label>
            <Input
              id="due_date"
              type="date"
              value={formData.due_date}
              onChange={(e) => setFormData(prev => ({ ...prev, due_date: e.target.value }))}
              required
            />
          </div>

          <div>
            <Label htmlFor="payment_terms">{t("finance.ar.paymentTerms")}</Label>
            <Select
              value={formData.payment_terms}
              onValueChange={(value) => setFormData(prev => ({ ...prev, payment_terms: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="TT">TT (Telegraphic Transfer)</SelectItem>
                <SelectItem value="DP">DP (Documents against Payment)</SelectItem>
                <SelectItem value="LC">LC (Letter of Credit)</SelectItem>
                <SelectItem value="Deposit">Deposit (Advance Payment)</SelectItem>
                <SelectItem value="30% Deposit + 70% TT">30% Deposit + 70% TT</SelectItem>
                <SelectItem value="50% Deposit + 50% LC">50% Deposit + 50% LC</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="md:col-span-2 lg:col-span-4">
            <Button type="submit" className="w-full">
              <Plus className="mr-2 h-4 w-4" />
              {t("finance.ar.createInvoice")}
            </Button>
          </div>
        </form>

        {/* Enhanced AR Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t("finance.ar.invoiceNumber")}</TableHead>
                <TableHead>{t("finance.ar.customer")}</TableHead>
                <TableHead>{t("finance.ar.contract")}</TableHead>
                <TableHead>{t("finance.ar.amount")}</TableHead>
                <TableHead>{t("finance.ar.received")}</TableHead>
                <TableHead>{t("finance.ar.status")}</TableHead>
                <TableHead>{t("finance.ar.dueDate")}</TableHead>
                <TableHead>{t("finance.ar.aging")}</TableHead>
                <TableHead>{t("common.actions")}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {arInvoices.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} className="text-center text-muted-foreground">
                    {t("finance.ar.noInvoices")}
                  </TableCell>
                </TableRow>
              ) : (
                arInvoices.map((invoice) => (
                  <TableRow key={invoice.id}>
                    <TableCell className="font-medium">
                      {invoice.number || invoice.id?.slice(-8) || 'N/A'}
                    </TableCell>
                    <TableCell>{invoice.customer?.name || 'Unknown'}</TableCell>
                    <TableCell>
                      {invoice.salesContract ? (
                        <span className="text-sm text-blue-600">
                          {invoice.salesContract.contract_number || invoice.salesContract.number}
                        </span>
                      ) : invoice.sales_contract_id ? (
                        <span className="text-sm text-orange-600">Contract ID: {invoice.sales_contract_id.slice(-8)}</span>
                      ) : (
                        <span className="text-sm text-gray-400">No Contract</span>
                      )}
                    </TableCell>
                    <TableCell>{formatCurrency(parseFloat(invoice.amount || '0'), invoice.currency)}</TableCell>
                    <TableCell>{formatCurrency(parseFloat(invoice.received || '0'), invoice.currency)}</TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(invoice.status)}>
                        {invoice.status.toUpperCase()}
                      </Badge>
                    </TableCell>
                    <TableCell>{formatDate(invoice.due_date)}</TableCell>
                    <TableCell>
                      <span className={getAgingColor(calculateAgingDays(invoice.date))}>
                        {calculateAgingDays(invoice.date)} days
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Button variant="ghost" size="sm" asChild title={t("finance.ar.viewInvoice")}>
                          <Link href={`/finance/ar/${invoice.id}`}>
                            <Eye className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button variant="ghost" size="sm" asChild title={t("finance.ar.editInvoice")}>
                          <Link href={`/finance/ar/${invoice.id}/edit`}>
                            <Edit className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handlePreview(invoice)}
                          title={t("finance.ar.generatePDF")}
                        >
                          <File className="h-4 w-4" />
                        </Button>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(invoice)}
                          title={t("finance.ar.deleteInvoice")}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>

      {/* Confirmation Dialog for Delete Actions */}
      <ConfirmationDialog
        open={deleteConfirmation.isOpen}
        onOpenChange={(open) => setDeleteConfirmation(prev => ({ ...prev, isOpen: open }))}
        onConfirm={confirmDelete}
        title={t("finance.ar.deleteConfirmTitle")}
        description={`Are you sure you want to delete AR invoice "${deleteConfirmation.invoiceNumber}"? This action cannot be undone.`}
        confirmText={t("finance.ar.deleteConfirmText")}
        cancelText="Cancel"
        variant="destructive"
      />

      {/* PDF Preview Modal */}
      <InvoicePDFPreviewModal
        isOpen={pdfPreview.isOpen}
        onClose={() => setPdfPreview(prev => ({ ...prev, isOpen: false }))}
        invoiceId={pdfPreview.invoiceId}
        invoiceNumber={pdfPreview.invoiceNumber}
        invoiceType="ar"
      />
    </Card>
  )
}
