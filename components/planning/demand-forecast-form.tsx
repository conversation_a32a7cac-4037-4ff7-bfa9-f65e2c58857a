"use client"

/**
 * Manufacturing ERP - Professional Demand Forecast Form Component
 * 
 * Professional form component for creating and editing demand forecasts.
 * Includes product selection, forecast period configuration, and confidence level settings.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 1A MRP UI Components
 */

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { ProductSelect } from "@/components/forms/product-select"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"
import { TrendingUp, Package, Calendar, Target, AlertCircle, CheckCircle, Settings, Users, X } from "lucide-react"

// ✅ PROFESSIONAL: Zod validation schema with optional supplier preferences
const demandForecastSchema = z.object({
  productId: z.string().min(1, "Product selection is required"),
  forecastPeriod: z.string().min(1, "Forecast period is required"),
  forecastedDemand: z.string().min(1, "Forecasted demand is required").refine(
    (val) => !isNaN(Number(val)) && Number(val) > 0,
    "Forecasted demand must be a positive number"
  ),
  confidenceLevel: z.enum(["low", "medium", "high"], {
    required_error: "Confidence level is required"
  }),
  forecastMethod: z.enum(["pipeline", "historical", "manual", "hybrid"]).default("pipeline"),
  seasonalityApplied: z.enum(["true", "false"]).default("false"),
  trendFactorApplied: z.string().default("1.0").refine(
    (val) => !isNaN(Number(val)) && Number(val) > 0,
    "Trend factor must be a positive number"
  ),
  notes: z.string().optional(),
  // ✅ REDESIGNED: Simple supplier selection (replaces complex preferences)
  preferredSupplierId: z.string().optional(),
})

type DemandForecastFormData = z.infer<typeof demandForecastSchema>

interface DemandForecastFormProps {
  initialData?: Partial<DemandForecastFormData>
  products?: Array<{ id: string; name: string; sku: string }>
  suppliers?: Array<{ id: string; name: string }>
  onSubmit?: (data: DemandForecastFormData) => Promise<void>
  onCancel?: () => void
  isLoading?: boolean
  mode?: "create" | "edit"
}

export function DemandForecastForm({
  initialData,
  products = [],
  suppliers = [],
  onSubmit,
  onCancel,
  isLoading = false,
  mode = "create"
}: DemandForecastFormProps) {
  const router = useRouter()
  const { toast } = useSafeToast()
  const { t } = useI18n()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<any>(null)

  const form = useForm<DemandForecastFormData>({
    resolver: zodResolver(demandForecastSchema),
    defaultValues: {
      productId: initialData?.productId || "",
      forecastPeriod: initialData?.forecastPeriod || "",
      forecastedDemand: initialData?.forecastedDemand || "",
      confidenceLevel: initialData?.confidenceLevel || "medium",
      forecastMethod: initialData?.forecastMethod || "pipeline",
      seasonalityApplied: initialData?.seasonalityApplied || "false",
      trendFactorApplied: initialData?.trendFactorApplied || "1.0",
      notes: initialData?.notes || "",
      // ✅ REDESIGNED: Simple supplier selection (use "auto" for empty)
      preferredSupplierId: initialData?.preferredSupplierId || "auto",
    }
  })

  // Generate forecast period options
  const generateForecastPeriods = () => {
    const periods = []
    const currentDate = new Date()
    const currentYear = currentDate.getFullYear()
    const currentMonth = currentDate.getMonth()

    // Next 12 months
    for (let i = 0; i < 12; i++) {
      const date = new Date(currentYear, currentMonth + i, 1)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      periods.push({
        value: `${year}-${month}`,
        label: `${date.toLocaleString('default', { month: 'long' })} ${year}`
      })
    }

    // Next 4 quarters
    for (let i = 0; i < 4; i++) {
      const quarterMonth = Math.floor((currentMonth + i * 3) / 3) * 3
      const quarterYear = currentYear + Math.floor((currentMonth + i * 3) / 12)
      const quarter = Math.floor(quarterMonth / 3) + 1
      periods.push({
        value: `${quarterYear}-Q${quarter}`,
        label: `Q${quarter} ${quarterYear}`
      })
    }

    return periods
  }

  const forecastPeriods = generateForecastPeriods()

  // Handle product selection
  useEffect(() => {
    const productId = form.watch("productId")
    if (productId && products.length > 0) {
      const product = products.find(p => p.id === productId)
      setSelectedProduct(product)
    }
  }, [form.watch("productId"), products])

  const handleSubmit = async (data: DemandForecastFormData) => {
    if (isSubmitting) return

    setIsSubmitting(true)
    try {
      if (onSubmit) {
        await onSubmit(data)
        toast({
          title: t("demandForecast.success"),
          description: `${t("forecasting.demandForecast")} ${mode === "create" ? t("demandForecast.createdSuccessfully") : t("demandForecast.updatedSuccessfully")}`,
        })
      } else {
        // Transform data for API (convert string numbers to actual numbers)
        const apiData = {
          ...data,
          forecastedDemand: Number(data.forecastedDemand),
          trendFactorApplied: Number(data.trendFactorApplied),
          // ✅ REDESIGNED: Simple supplier selection (convert "auto" to undefined)
          preferredSupplierId: data.preferredSupplierId === "auto" ? undefined : data.preferredSupplierId,
        }

        // Default API call
        const response = await fetch("/api/planning/demand-forecast", {
          method: mode === "create" ? "POST" : "PATCH",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(apiData),
        })

        if (!response.ok) {
          throw new Error(t("demandForecast.failedToSave"))
        }

        toast({
          title: t("demandForecast.success"),
          description: `${t("forecasting.demandForecast")} ${mode === "create" ? t("demandForecast.createdSuccessfully") : t("demandForecast.updatedSuccessfully")}`,
        })

        router.push("/planning")
      }
    } catch (error) {
      console.error("Error saving demand forecast:", error)
      toast({
        title: t("demandForecast.error"),
        description: error instanceof Error ? error.message : t("demandForecast.failedToSave"),
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    } else {
      router.push("/planning")
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="p-2 bg-blue-100 rounded-lg">
          <TrendingUp className="h-6 w-6 text-blue-600" />
        </div>
        <div>
          <h1 className="text-2xl font-bold">
            {mode === "create" ? t("demandForecast.createTitle") : t("demandForecast.editTitle")}
          </h1>
          <p className="text-muted-foreground">
            {t("demandForecast.description")}
          </p>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Form */}
            <div className="lg:col-span-2 space-y-6">
              {/* Product Selection */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    {t("demandForecast.productSelection")}
                  </CardTitle>
                  <CardDescription>
                    {t("demandForecast.selectProduct")}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="productId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("demandForecast.product")}</FormLabel>
                        <FormControl>
                          <ProductSelect
                            products={products}
                            value={field.value}
                            onValueChange={field.onChange}
                            placeholder={t("demandForecast.searchSelectProduct")}
                            showPrice={false}
                            showAddNew={false}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {selectedProduct && (
                    <Alert>
                      <CheckCircle className="h-4 w-4" />
                      <AlertDescription>
                        {t("demandForecast.selected")}: <strong>{selectedProduct.name}</strong> ({selectedProduct.sku})
                      </AlertDescription>
                    </Alert>
                  )}

                  {/* ✅ NEW: Primary Supplier Selection Field */}
                  {suppliers && suppliers.length > 0 && (
                    <FormField
                      control={form.control}
                      name="preferredSupplierId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("demandForecast.supplierOptional")}</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value || "auto"}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder={t("demandForecast.autoSelectSupplier")} />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="auto">
                                <div className="flex items-center gap-2">
                                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                  <span>{t("demandForecast.autoSelectSupplier")}</span>
                                </div>
                              </SelectItem>
                              {suppliers.map((supplier) => (
                                <SelectItem key={supplier.id} value={supplier.id}>
                                  <div className="flex flex-col">
                                    <span className="font-medium">{supplier.name}</span>
                                    <span className="text-sm text-muted-foreground">{t("demandForecast.preferredSupplier")}</span>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            {t("demandForecast.chooseSupplier")}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </CardContent>
              </Card>

              {/* Forecast Configuration */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    {t("demandForecast.forecastConfiguration")}
                  </CardTitle>
                  <CardDescription>
                    {t("demandForecast.configurePeriod")}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="forecastPeriod"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("demandForecast.forecastPeriod")}</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder={t("demandForecast.selectPeriod")} />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {forecastPeriods.map((period) => (
                                <SelectItem key={period.value} value={period.value}>
                                  {period.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="forecastedDemand"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("demandForecast.forecastedDemand")}</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder={t("demandForecast.enterQuantity")}
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            {t("demandForecast.expectedDemand")}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="confidenceLevel"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("demandForecast.confidenceLevel")}</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="low">
                                <div className="flex items-center gap-2">
                                  <Badge variant="outline">Low</Badge>
                                  <span>{t("demandForecast.lowConfidence")}</span>
                                </div>
                              </SelectItem>
                              <SelectItem value="medium">
                                <div className="flex items-center gap-2">
                                  <Badge variant="secondary">Medium</Badge>
                                  <span>{t("demandForecast.mediumConfidence")}</span>
                                </div>
                              </SelectItem>
                              <SelectItem value="high">
                                <div className="flex items-center gap-2">
                                  <Badge variant="default">High</Badge>
                                  <span>{t("demandForecast.highConfidence")}</span>
                                </div>
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="forecastMethod"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("demandForecast.forecastMethod")}</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="pipeline">{t("demandForecast.pipelineAnalysis")}</SelectItem>
                              <SelectItem value="historical">{t("demandForecast.historicalData")}</SelectItem>
                              <SelectItem value="manual">{t("demandForecast.manualEntry")}</SelectItem>
                              <SelectItem value="hybrid">{t("demandForecast.hybridMethod")}</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Advanced Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    {t("demandForecast.advancedSettings")}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="seasonalityApplied"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("demandForecast.seasonalityAdjustment")}</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="false">{t("demandForecast.noAdjustment")}</SelectItem>
                            <SelectItem value="true">{t("demandForecast.applySeasonality")}</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="trendFactorApplied"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("demandForecast.trendFactor")}</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.1"
                            placeholder="1.0"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          {t("demandForecast.growthMultiplier")}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>



              {/* Notes */}
              <Card>
                <CardHeader>
                  <CardTitle>{t("demandForecast.additionalNotes")}</CardTitle>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="notes"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Textarea
                            placeholder={t("demandForecast.addNotes")}
                            className="min-h-[100px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </div>
          </div>

          <Separator />

          {/* Actions */}
          <div className="flex items-center justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              {t("demandForecast.cancel")}
            </Button>

            <div className="flex items-center gap-3">
              {mode === "edit" && (
                <Alert className="flex items-center gap-2 px-3 py-2">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="text-sm">
                    {t("demandForecast.changesUpdate")}
                  </AlertDescription>
                </Alert>
              )}

              <Button
                type="submit"
                disabled={isSubmitting || isLoading}
                className="min-w-[120px]"
              >
                {isSubmitting ? t("demandForecast.saving") : mode === "create" ? t("demandForecast.createForecast") : t("demandForecast.updateForecast")}
              </Button>
            </div>
          </div>
        </form>
      </Form>
    </div>
  )
}
