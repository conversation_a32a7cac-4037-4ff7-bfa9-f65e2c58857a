"use client"

/**
 * Manufacturing ERP - Quality Analytics Dashboard Component
 * 
 * Professional quality analytics dashboard with trend analysis, performance metrics,
 * and quality KPIs visualization. Enhances existing quality control system with
 * advanced analytics capabilities.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 2 Quality Control Enhancement
 */

import { useState, useEffect, useMemo } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useI18n } from "@/components/i18n-provider"
import { useSafeToast } from "@/hooks/use-safe-toast"
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Area,
  AreaChart
} from "recharts"
import {
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  Award,
  BarChart3,

  Activity,
  RefreshCw
} from "lucide-react"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface QualityMetrics {
  totalInspections: number
  passRate: number
  failRate: number
  pendingInspections: number
  averageInspectionTime: number
  defectRate: number
  firstPassYield: number
  customerComplaints: number
}

interface QualityTrend {
  period: string
  inspections: number
  passed: number
  failed: number
  passRate: number
  defectRate: number
}



interface SupplierQuality {
  supplierId: string
  supplierName: string
  totalInspections: number
  passRate: number
  defectRate: number
  rating: "excellent" | "good" | "fair" | "poor"
}

interface QualityAnalyticsDashboardProps {
  companyId: string
  timeRange?: "7d" | "30d" | "90d" | "1y"
  refreshInterval?: number
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function QualityAnalyticsDashboard({
  companyId,
  timeRange = "30d",
  refreshInterval = 300000 // 5 minutes
}: QualityAnalyticsDashboardProps) {
  const { t } = useI18n()
  const { toast } = useSafeToast()

  // ✅ STATE MANAGEMENT
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange)
  const [loading, setLoading] = useState(true)
  const [metrics, setMetrics] = useState<QualityMetrics | null>(null)
  const [trends, setTrends] = useState<QualityTrend[]>([])

  const [supplierQuality, setSupplierQuality] = useState<SupplierQuality[]>([])
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())

  // ✅ FETCH QUALITY ANALYTICS DATA
  const fetchAnalyticsData = async () => {
    try {
      setLoading(true)

      const [metricsRes, trendsRes, suppliersRes] = await Promise.all([
        fetch(`/api/quality/analytics/metrics?timeRange=${selectedTimeRange}`),
        fetch(`/api/quality/analytics/trends?timeRange=${selectedTimeRange}`),
        fetch(`/api/quality/analytics/suppliers?timeRange=${selectedTimeRange}`)
      ])

      if (metricsRes.ok) {
        const metricsData = await metricsRes.json()
        setMetrics(metricsData)
      }

      if (trendsRes.ok) {
        const trendsData = await trendsRes.json()
        // Ensure we have the trends array from the response
        setTrends(trendsData.trends || [])
      } else {
        setTrends([])
      }



      if (suppliersRes.ok) {
        const suppliersData = await suppliersRes.json()
        // Ensure we have the suppliers array from the response
        setSupplierQuality(suppliersData.suppliers || [])
      } else {
        setSupplierQuality([])
      }

      setLastUpdated(new Date())
    } catch (error) {
      console.error("Error fetching quality analytics:", error)
      // Set empty arrays on error to prevent chart crashes
      setTrends([])
      setSupplierQuality([])

      toast({
        title: t('quality.error'),
        description: t('quality.failed_to_load_analytics'),
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  // ✅ EFFECTS
  useEffect(() => {
    fetchAnalyticsData()
  }, [selectedTimeRange])

  useEffect(() => {
    const interval = setInterval(fetchAnalyticsData, refreshInterval)
    return () => clearInterval(interval)
  }, [refreshInterval, selectedTimeRange])

  // ✅ COMPUTED VALUES
  const chartColors = {
    primary: "#3b82f6",
    success: "#10b981",
    warning: "#f59e0b",
    danger: "#ef4444",
    secondary: "#6b7280"
  }

  const qualityGrade = useMemo(() => {
    if (!metrics) return "unknown"
    if (metrics.passRate >= 98) return "excellent"
    if (metrics.passRate >= 95) return "good"
    if (metrics.passRate >= 90) return "fair"
    return "poor"
  }, [metrics])

  // ✅ RENDER LOADING STATE
  if (loading && !metrics) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-muted rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-muted rounded w-1/2 mb-4"></div>
                  <div className="h-64 bg-muted rounded"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* ✅ HEADER WITH CONTROLS */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">{t('quality.quality_analytics')}</h2>
          <p className="text-muted-foreground">
            {t('quality.advanced_quality_metrics')}
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">{t('quality.last_7_days')}</SelectItem>
              <SelectItem value="30d">{t('quality.last_30_days')}</SelectItem>
              <SelectItem value="90d">{t('quality.last_90_days')}</SelectItem>
              <SelectItem value="1y">{t('quality.last_year')}</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchAnalyticsData}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`} />
            {t('quality.refresh')}
          </Button>
        </div>
      </div>

      {/* ✅ KEY METRICS CARDS */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('quality.pass_rate')}</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {metrics.passRate.toFixed(1)}%
              </div>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant={qualityGrade === "excellent" ? "default" : "secondary"}>
                  {t(`quality.${qualityGrade}`)}
                </Badge>
                {metrics.passRate >= 95 ? (
                  <TrendingUp className="h-3 w-3 text-green-600" />
                ) : (
                  <TrendingDown className="h-3 w-3 text-red-600" />
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('quality.total_inspections')}</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.totalInspections}</div>
              <p className="text-xs text-muted-foreground">
                {metrics.pendingInspections} {t('quality.pending')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('quality.defect_rate')}</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {metrics.defectRate.toFixed(2)}%
              </div>
              <p className="text-xs text-muted-foreground">
                {t('quality.target')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('quality.first_pass_yield')}</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {metrics.firstPassYield.toFixed(1)}%
              </div>
              <p className="text-xs text-muted-foreground">
                {t('quality.industry_benchmark')}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* ✅ CHARTS SECTION */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quality Trends Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              {t('quality.quality_trends')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {trends && trends.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={trends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="period" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="passRate"
                    stackId="1"
                    stroke={chartColors.success}
                    fill={chartColors.success}
                    fillOpacity={0.6}
                    name={t('quality.pass_rate_percent')}
                  />
                  <Area
                    type="monotone"
                    dataKey="defectRate"
                    stackId="2"
                    stroke={chartColors.danger}
                    fill={chartColors.danger}
                    fillOpacity={0.6}
                    name={t('quality.defect_rate_percent')}
                  />
                </AreaChart>
              </ResponsiveContainer>
            ) : (
              <div className="h-[300px] flex items-center justify-center text-muted-foreground">
                {t('quality.no_trend_data')}
              </div>
            )}
          </CardContent>
        </Card>


      </div>

      {/* ✅ LAST UPDATED INFO */}
      <div className="text-xs text-muted-foreground text-center">
        {t('quality.last_updated', { timestamp: lastUpdated.toLocaleString() })}
      </div>
    </div>
  )
}
