"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from "react"
import { useRouter, usePathname } from "next/navigation"
import { Plus, Eye, Edit, List, Home, Users, Building, Package } from "lucide-react"
import { useI18n } from "@/components/i18n-provider"

export interface Tab {
  id: string
  title: string
  path: string
  type: 'list' | 'add' | 'view' | 'edit' | 'dashboard'
  icon?: React.ReactNode
  closeable?: boolean
  data?: any
}

interface TabContextType {
  tabs: Tab[]
  activeTabId: string
  openTab: (tab: Tab) => void
  closeTab: (tabId: string) => void
  switchTab: (tabId: string) => void
  openCustomerListTab: () => void
  openCustomerViewTab: (customerId: string, customerName?: string) => void

  openCustomerEditTab: (customerId: string, customerName?: string) => void
  openSupplierListTab: () => void
  openSupplierViewTab: (supplierId: string, supplierName?: string) => void
  openSupplierAddTab: () => void
  openSupplierEditTab: (supplierId: string, supplierName?: string) => void
  openProductListTab: () => void
  openProductViewTab: (productId: string, productName?: string) => void
  openProductAddTab: () => void
  openProductEditTab: (productId: string, productName?: string) => void
}

const TabContext = createContext<TabContextType | undefined>(undefined)

interface TabProviderProps {
  children: ReactNode
}

export function TabProvider({ children }: TabProviderProps) {
  const router = useRouter()
  const pathname = usePathname()
  const { t } = useI18n()
  const [tabs, setTabs] = useState<Tab[]>([])
  const [activeTabId, setActiveTabId] = useState<string>("")

  // Initialize with dashboard tab
  useEffect(() => {
    const dashboardTab: Tab = {
      id: "dashboard",
      title: t("nav.item.dashboard"),
      path: "/",
      type: "dashboard",
      icon: <Home className="h-4 w-4" />,
      closeable: false
    }

    setTabs([dashboardTab])
    setActiveTabId("dashboard")
  }, [t])

  // Sync active tab with current pathname
  useEffect(() => {
    const currentTab = tabs.find(tab => tab.path === pathname)
    if (currentTab) {
      setActiveTabId(currentTab.id)
    } else {
      // Auto-create tab for current path if it doesn't exist
      createTabFromPath(pathname)
    }
  }, [pathname, tabs])

  const createTabFromPath = (path: string) => {
    // Skip if tab already exists or is dashboard
    if (tabs.some(tab => tab.path === path) || path === "/") return

    let newTabs: Tab[] = []

    // Customer module paths
    if (path === "/customers" || path === "/crm") {
      newTabs.push({
        id: "customers-list",
        title: t("customers.title"),
        path: path,
        type: "list",
        icon: <Users className="h-4 w-4" />,
        closeable: false
      })
    } else if (path === "/suppliers") {
      newTabs.push({
        id: "suppliers-list",
        title: t("suppliers.title"),
        path: path,
        type: "list",
        icon: <Building className="h-4 w-4" />,
        closeable: false
      })
    } else if (path === "/products") {
      newTabs.push({
        id: "products-list",
        title: t("products.title"),
        path: path,
        type: "list",
        icon: <Package className="h-4 w-4" />,
        closeable: false
      })
    } else if (path.startsWith("/crm/") && path !== "/crm") {
      // Always ensure main Customers tab exists for sub-pages
      const mainCustomersTab = tabs.find(tab => tab.path === "/crm" || tab.path === "/customers")
      if (!mainCustomersTab) {
        newTabs.push({
          id: "customers-list",
          title: t("customers.title"),
          path: "/crm",
          type: "list",
          icon: <Users className="h-4 w-4" />,
          closeable: false // Main tab should not be closeable when sub-tabs are open
        })
      }

      const segments = path.split("/")
      const customerId = segments[2]

      if (segments.length === 3) {
        // Customer detail view: /crm/[id]
        newTabs.push({
          id: `customer-view-${customerId}`,
          title: `${t("common.view")} Customer`,
          path: path,
          type: "view",
          icon: <Eye className="h-4 w-4" />,
          closeable: true,
          data: { customerId }
        })
      } else if (segments[3] === "edit") {
        // Customer edit: /crm/[id]/edit
        newTabs.push({
          id: `customer-edit-${customerId}`,
          title: `${t("common.edit")} Customer`,
          path: path,
          type: "edit",
          icon: <Edit className="h-4 w-4" />,
          closeable: true,
          data: { customerId }
        })
      }
    } else if (path.startsWith("/suppliers/") && path !== "/suppliers") {
      // Always ensure main Suppliers tab exists for sub-pages
      const mainSuppliersTab = tabs.find(tab => tab.path === "/suppliers")
      if (!mainSuppliersTab) {
        newTabs.push({
          id: "suppliers-list",
          title: t("suppliers.title"),
          path: "/suppliers",
          type: "list",
          icon: <Building className="h-4 w-4" />,
          closeable: false // Main tab should not be closeable when sub-tabs are open
        })
      }

      const segments = path.split("/")

      if (segments[2] === "view" && segments[3]) {
        // Supplier view: /suppliers/view/[id]
        const supplierId = segments[3]
        newTabs.push({
          id: `supplier-view-${supplierId}`,
          title: `${t("common.view")} Supplier`,
          path: path,
          type: "view",
          icon: <Eye className="h-4 w-4" />,
          closeable: true,
          data: { supplierId }
        })
      } else if (segments[2] === "edit" && segments[3]) {
        // Supplier edit: /suppliers/edit/[id]
        const supplierId = segments[3]
        newTabs.push({
          id: `supplier-edit-${supplierId}`,
          title: `${t("common.edit")} Supplier`,
          path: path,
          type: "edit",
          icon: <Edit className="h-4 w-4" />,
          closeable: true,
          data: { supplierId }
        })
      } else if (segments[2] === "add") {
        // Supplier add: /suppliers/add
        newTabs.push({
          id: `supplier-add-${Date.now()}`,
          title: `${t("common.add")} Supplier`,
          path: path,
          type: "add",
          icon: <Plus className="h-4 w-4" />,
          closeable: true
        })
      }
    } else if (path.startsWith("/products/") && path !== "/products") {
      // Always ensure main Products tab exists for sub-pages
      const mainProductsTab = tabs.find(tab => tab.path === "/products")
      if (!mainProductsTab) {
        newTabs.push({
          id: "products-list",
          title: t("products.title"),
          path: "/products",
          type: "list",
          icon: <Package className="h-4 w-4" />,
          closeable: false // Main tab should not be closeable when sub-tabs are open
        })
      }

      const segments = path.split("/")
      const productId = segments[2]

      if (segments.length === 3) {
        // Product detail view: /products/[id]
        newTabs.push({
          id: `product-view-${productId}`,
          title: `${t("common.view")} Product`,
          path: path,
          type: "view",
          icon: <Eye className="h-4 w-4" />,
          closeable: true,
          data: { productId }
        })
      } else if (segments[2] === "edit" && segments[3]) {
        // Product edit: /products/edit/[id]
        const editProductId = segments[3]
        newTabs.push({
          id: `product-edit-${editProductId}`,
          title: `${t("common.edit")} Product`,
          path: path,
          type: "edit",
          icon: <Edit className="h-4 w-4" />,
          closeable: true,
          data: { productId: editProductId }
        })
      } else if (segments[2] === "add") {
        // Product add: /products/add
        newTabs.push({
          id: `product-add-${Date.now()}`,
          title: `${t("common.add")} Product`,
          path: path,
          type: "add",
          icon: <Plus className="h-4 w-4" />,
          closeable: true
        })
      }
    }

    if (newTabs.length > 0) {
      setTabs(prev => [...prev, ...newTabs])
      // Set active tab to the last created tab (the specific page tab)
      const activeTab = newTabs[newTabs.length - 1]
      setActiveTabId(activeTab.id)
    }
  }

  const openTab = (tab: Tab) => {
    // Check if tab already exists
    const existingTab = tabs.find(t => t.path === tab.path)
    if (existingTab) {
      // Update tab title if the new one is more specific (has more info)
      if (tab.title.length > existingTab.title.length ||
        (tab.data && !existingTab.data)) {
        setTabs(prev => prev.map(t =>
          t.path === tab.path
            ? { ...t, title: tab.title, data: tab.data || t.data }
            : t
        ))
      }
      setActiveTabId(existingTab.id)
      router.push(existingTab.path)
      return
    }

    // Add new tab
    setTabs(prev => [...prev, tab])
    setActiveTabId(tab.id)
    router.push(tab.path)
  }

  const closeTab = (tabId: string) => {
    const tabToClose = tabs.find(t => t.id === tabId)
    if (!tabToClose || !tabToClose.closeable) return

    const newTabs = tabs.filter(t => t.id !== tabId)

    // Update main tab closeable status based on remaining sub-tabs
    const updatedTabs = newTabs.map(tab => {
      // If this is a main customers tab, make it closeable if no sub-tabs remain
      if ((tab.path === "/crm" || tab.path === "/customers") && tab.type === "list") {
        const hasSubTabs = newTabs.some(t =>
          t.path.startsWith("/crm/") && t.path !== "/crm" && t.path !== "/customers"
        )
        return { ...tab, closeable: !hasSubTabs }
      }
      return tab
    })

    setTabs(updatedTabs)

    // If closing active tab, switch to the last remaining tab
    if (activeTabId === tabId) {
      const lastTab = updatedTabs[updatedTabs.length - 1]
      if (lastTab) {
        setActiveTabId(lastTab.id)
        router.push(lastTab.path)
      }
    }
  }

  const switchTab = (tabId: string) => {
    const tab = tabs.find(t => t.id === tabId)
    if (tab) {
      setActiveTabId(tabId)
      router.push(tab.path)
    }
  }

  // Customer-specific tab functions
  const openCustomerListTab = () => {
    const tab: Tab = {
      id: "customers-list",
      title: t("customers.title"),
      path: "/crm",
      type: "list",
      icon: <Users className="h-4 w-4" />,
      closeable: false
    }
    openTab(tab)
  }

  const openCustomerViewTab = (customerId: string, customerName?: string) => {
    const tab: Tab = {
      id: `customer-view-${customerId}`,
      title: customerName ? `${t("common.view")} ${customerName}` : `${t("common.view")} Customer`,
      path: `/crm/${customerId}`,
      type: "view",
      icon: <Eye className="h-4 w-4" />,
      closeable: true,
      data: { customerId, customerName }
    }
    openTab(tab)
  }



  const openCustomerEditTab = (customerId: string, customerName?: string) => {
    const tab: Tab = {
      id: `customer-edit-${customerId}`,
      title: customerName ? `${t("common.edit")} ${customerName}` : `${t("common.edit")} Customer`,
      path: `/crm/${customerId}/edit`,
      type: "edit",
      icon: <Edit className="h-4 w-4" />,
      closeable: true,
      data: { customerId, customerName }
    }
    openTab(tab)
  }

  const openSupplierListTab = () => {
    const tab: Tab = {
      id: "suppliers-list",
      title: t("suppliers.title"),
      path: "/suppliers",
      type: "list",
      icon: <Building className="h-4 w-4" />,
      closeable: false
    }
    openTab(tab)
  }

  const openSupplierViewTab = (supplierId: string, supplierName?: string) => {
    const tab: Tab = {
      id: `supplier-view-${supplierId}`,
      title: supplierName ? `${t("common.view")} ${supplierName}` : `${t("common.view")} Supplier`,
      path: `/suppliers/view/${supplierId}`,
      type: "view",
      icon: <Eye className="h-4 w-4" />,
      closeable: true,
      data: { supplierId, supplierName }
    }
    openTab(tab)
  }

  const openSupplierAddTab = () => {
    const tab: Tab = {
      id: `supplier-add-${Date.now()}`,
      title: `${t("common.add")} Supplier`,
      path: "/suppliers/add",
      type: "add",
      icon: <Plus className="h-4 w-4" />,
      closeable: true
    }
    openTab(tab)
  }

  const openSupplierEditTab = (supplierId: string, supplierName?: string) => {
    const tab: Tab = {
      id: `supplier-edit-${supplierId}`,
      title: supplierName ? `${t("common.edit")} ${supplierName}` : `${t("common.edit")} Supplier`,
      path: `/suppliers/edit/${supplierId}`,
      type: "edit",
      icon: <Edit className="h-4 w-4" />,
      closeable: true,
      data: { supplierId, supplierName }
    }
    openTab(tab)
  }

  // Product navigation functions
  const openProductListTab = () => {
    const tab: Tab = {
      id: "products-list",
      title: t("products.title"),
      path: "/products",
      type: "list",
      icon: <Package className="h-4 w-4" />,
      closeable: false
    }
    openTab(tab)
  }

  const openProductViewTab = (productId: string, productName?: string) => {
    const tab: Tab = {
      id: `product-view-${productId}`,
      title: productName ? `${t("common.view")} ${productName}` : `${t("common.view")} Product`,
      path: `/products/${productId}`,
      type: "view",
      icon: <Eye className="h-4 w-4" />,
      closeable: true,
      data: { productId, productName }
    }
    openTab(tab)
  }

  const openProductAddTab = () => {
    const tab: Tab = {
      id: "product-add",
      title: t("products.add"),
      path: "/products/add",
      type: "add",
      icon: <Plus className="h-4 w-4" />,
      closeable: true
    }
    openTab(tab)
  }

  const openProductEditTab = (productId: string, productName?: string) => {
    const tab: Tab = {
      id: `product-edit-${productId}`,
      title: productName ? `${t("common.edit")} ${productName}` : `${t("common.edit")} Product`,
      path: `/products/edit/${productId}`,
      type: "edit",
      icon: <Edit className="h-4 w-4" />,
      closeable: true,
      data: { productId, productName }
    }
    openTab(tab)
  }

  const value: TabContextType = {
    tabs,
    activeTabId,
    openTab,
    closeTab,
    switchTab,
    openCustomerListTab,
    openCustomerViewTab,
    openCustomerEditTab,
    openSupplierListTab,
    openSupplierViewTab,
    openSupplierAddTab,
    openSupplierEditTab,
    openProductListTab,
    openProductViewTab,
    openProductAddTab,
    openProductEditTab
  }

  return (
    <TabContext.Provider value={value}>
      {children}
    </TabContext.Provider>
  )
}

export function useTabContext() {
  const context = useContext(TabContext)
  if (context === undefined) {
    throw new Error("useTabContext must be used within a TabProvider")
  }
  return context
}

// Convenience hook for tab navigation
export function useTabNavigation() {
  const {
    openCustomerListTab,
    openCustomerViewTab,
    openCustomerAddTab,
    openCustomerEditTab,
    openSupplierListTab,
    openSupplierViewTab,
    openSupplierAddTab,
    openSupplierEditTab,
    openProductListTab,
    openProductViewTab,
    openProductAddTab,
    openProductEditTab
  } = useTabContext()

  return {
    openCustomerListTab,
    openCustomerViewTab,
    openCustomerAddTab,
    openCustomerEditTab,
    openSupplierListTab,
    openSupplierViewTab,
    openSupplierAddTab,
    openSupplierEditTab,
    openProductListTab,
    openProductViewTab,
    openProductAddTab,
    openProductEditTab
  }
}
