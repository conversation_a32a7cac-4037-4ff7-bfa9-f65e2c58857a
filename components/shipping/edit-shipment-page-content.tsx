"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft, Edit } from "lucide-react"
import Link from "next/link"
import { useI18n } from "@/components/i18n-provider"
import { EditShipmentForm } from "./edit-shipment-form"

interface EditShipmentPageContentProps {
  shipment: any
  customers: any[]
  products: any[]
}

export function EditShipmentPageContent({ 
  shipment, 
  customers, 
  products 
}: EditShipmentPageContentProps) {
  const { t } = useI18n()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href={`/shipping/${shipment.id}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t("shipping.edit.back_to_shipment")}
          </Link>
        </Button>
        
        <div className="flex items-center gap-2">
          <Edit className="h-6 w-6 text-blue-600" />
          <div>
            <h1 className="text-2xl font-bold tracking-tight">{t("shipping.edit.title")}</h1>
            <p className="text-muted-foreground">
              {shipment.shipment_number} • {shipment.customer.name}
            </p>
          </div>
        </div>
      </div>

      {/* Edit Shipment Form */}
      <div className="max-w-4xl">
        <EditShipmentForm
          shipment={shipment}
          customers={customers}
          products={products}
        />
      </div>
    </div>
  )
}
