/**
 * Manufacturing ERP - Shipping Filters Component
 * Client-side search and filter controls for shipments
 */

"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Search, Filter, X } from "lucide-react"
import { useI18n } from "@/components/i18n-provider"

interface ShippingFiltersProps {
  className?: string
}

export function ShippingFilters({ className }: ShippingFiltersProps) {
  const { t } = useI18n()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [searchValue, setSearchValue] = useState(searchParams.get('search') || '')

  const currentStatus = searchParams.get('status') || 'all'
  const currentCustomer = searchParams.get('customer_id') || 'all'

  // Update search with debouncing
  useEffect(() => {
    const timer = setTimeout(() => {
      const params = new URLSearchParams(searchParams.toString())
      if (searchValue.trim()) {
        params.set('search', searchValue.trim())
      } else {
        params.delete('search')
      }
      router.replace(`/shipping?${params.toString()}`)
    }, 500)

    return () => clearTimeout(timer)
  }, [searchValue, router, searchParams])

  const handleStatusChange = (value: string) => {
    const params = new URLSearchParams(searchParams.toString())
    if (value && value !== "all") {
      params.set('status', value)
    } else {
      params.delete('status')
    }
    router.replace(`/shipping?${params.toString()}`)
  }

  const clearFilters = () => {
    setSearchValue('')
    router.replace('/shipping')
  }

  const hasActiveFilters = searchValue || currentStatus !== 'all' || currentCustomer !== 'all'

  return (
    <div className={`flex flex-col sm:flex-row gap-4 ${className}`}>
      {/* Search Input */}
      <div className="flex-1">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder={t("shipping.search_placeholder")}
            className="pl-10"
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
          />
        </div>
      </div>

      {/* Filters */}
      <div className="flex gap-2">
        {/* Status Filter */}
        <Select value={currentStatus} onValueChange={handleStatusChange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={t("shipping.filter.status_placeholder")} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t("shipping.filter.all_statuses")}</SelectItem>
            <SelectItem value="preparing">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                {t("shipping.status.preparing")}
              </div>
            </SelectItem>
            <SelectItem value="ready">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                {t("shipping.status.ready")}
              </div>
            </SelectItem>
            <SelectItem value="shipped">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-green-500"></div>
                {t("shipping.status.shipped")}
              </div>
            </SelectItem>
            <SelectItem value="in_transit">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                {t("shipping.status.in_transit")}
              </div>
            </SelectItem>
            <SelectItem value="out_for_delivery">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-orange-500"></div>
                {t("shipping.status.out_for_delivery")}
              </div>
            </SelectItem>
            <SelectItem value="delivered">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-green-600"></div>
                {t("shipping.status.delivered")}
              </div>
            </SelectItem>
            <SelectItem value="cancelled">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-red-500"></div>
                {t("shipping.status.cancelled")}
              </div>
            </SelectItem>
            <SelectItem value="exception">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-red-600"></div>
                {t("shipping.status.exception")}
              </div>
            </SelectItem>
          </SelectContent>
        </Select>

        {/* Clear Filters */}
        {hasActiveFilters && (
          <Button variant="outline" size="icon" onClick={clearFilters} title="Clear all filters">
            <X className="h-4 w-4" />
          </Button>
        )}

        {/* Filter Icon */}
        <Button variant="outline" size="icon" title="More filters">
          <Filter className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}
