/**
 * Manufacturing ERP - Edit Shipment Form Component
 * Professional shipment editing with validation
 */

"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { useSafeToast } from "@/hooks/use-safe-toast"
import {
  Ship,
  Plane,
  Truck,
  Package,
  User,
  Calendar,
  DollarSign,
  AlertCircle
} from "lucide-react"
import { useI18n } from "@/components/i18n-provider"

// Form validation schema
const editShipmentSchema = z.object({
  customer_id: z.string().min(1, "Customer is required"),
  shipping_method: z.enum(["sea_freight", "air_freight", "express", "truck"]),
  carrier: z.string().default(""),
  service_type: z.enum(["standard", "express", "economy"]).default("standard"),
  ship_date: z.string().default(""),
  estimated_delivery: z.string().default(""),
  shipping_cost: z.string().default(""),
  insurance_cost: z.string().default(""),
  tracking_number: z.string().default(""),
  notes: z.string().default(""),
  special_instructions: z.string().default(""),
  status: z.enum(["preparing", "ready", "shipped", "in_transit", "out_for_delivery", "delivered", "cancelled", "exception"])
})

type FormData = z.infer<typeof editShipmentSchema>

interface EditShipmentFormProps {
  shipment: any
  customers: Array<{
    id: string
    name: string
    contact_name: string | null
    contact_email: string | null
    address: string | null
  }>
  products: Array<{
    id: string
    name: string
    sku: string
    unit: string
    image: string | null
  }>
}

export function EditShipmentForm({ shipment, customers, products }: EditShipmentFormProps) {
  const { t } = useI18n()
  const router = useRouter()
  const { toast } = useSafeToast()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<FormData>({
    resolver: zodResolver(editShipmentSchema),
    defaultValues: {
      customer_id: shipment.customer_id || "",
      shipping_method: shipment.shipping_method || "sea_freight",
      carrier: shipment.carrier || "",
      service_type: shipment.service_type || "standard",
      ship_date: shipment.ship_date ? shipment.ship_date.split('T')[0] : "",
      estimated_delivery: shipment.estimated_delivery ? shipment.estimated_delivery.split('T')[0] : "",
      shipping_cost: shipment.shipping_cost || "",
      insurance_cost: shipment.insurance_cost || "",
      tracking_number: shipment.tracking_number || "",
      notes: shipment.notes || "",
      special_instructions: shipment.special_instructions || "",
      status: shipment.status || "preparing"
    }
  })

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true)

    try {
      const response = await fetch(`/api/shipping/shipments/${shipment.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to update shipment")
      }

      const updatedShipment = await response.json()

      toast({
        title: t("shipping.toast.updated"),
        description: `${t("shipping.view.shipment")} ${updatedShipment.shipment_number} ${t("shipping.toast.updated_desc")}`,
      })

      router.push(`/shipping/${shipment.id}`)
    } catch (error) {
      console.error("Error updating shipment:", error)
      toast({
        title: t("shipping.toast.error"),
        description: error instanceof Error ? error.message : t("shipping.toast.update_failed"),
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const getShippingMethodIcon = (method: string) => {
    switch (method) {
      case 'air_freight': return <Plane className="h-4 w-4" />
      case 'sea_freight': return <Ship className="h-4 w-4" />
      case 'express': return <Truck className="h-4 w-4 text-green-600" />
      case 'truck': return <Truck className="h-4 w-4" />
      default: return <Package className="h-4 w-4" />
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      preparing: { variant: "secondary" as const, color: "text-yellow-600", label: t("shipping.status.preparing") },
      ready: { variant: "secondary" as const, color: "text-blue-600", label: t("shipping.status.ready") },
      shipped: { variant: "default" as const, color: "text-purple-600", label: t("shipping.status.shipped") },
      in_transit: { variant: "default" as const, color: "text-indigo-600", label: t("shipping.status.in_transit") },
      out_for_delivery: { variant: "default" as const, color: "text-orange-600", label: t("shipping.status.out_for_delivery") },
      delivered: { variant: "default" as const, color: "text-green-600", label: t("shipping.status.delivered") },
      cancelled: { variant: "destructive" as const, color: "text-red-600", label: t("shipping.status.cancelled") },
      exception: { variant: "destructive" as const, color: "text-red-600", label: t("shipping.status.exception") }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.preparing

    return (
      <Badge variant={config.variant} className={config.color}>
        {config.label}
      </Badge>
    )
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Current Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Ship className="h-5 w-5" />
              {t("shipping.edit.current_status")}
            </CardTitle>
            <CardDescription>
              {t("shipping.view.shipment")} {shipment.shipment_number} • {t("shipping.edit.created")} {new Date(shipment.created_at).toLocaleDateString()}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <div>
                <span className="text-sm font-medium">{t("shipping.edit.current_status_label")}</span>
                <div className="mt-1">
                  {getStatusBadge(shipment.status)}
                </div>
              </div>
              {shipment.salesContract && (
                <div>
                  <span className="text-sm font-medium">{t("shipping.edit.related_contract")}</span>
                  <div className="mt-1">
                    <Badge variant="outline">{shipment.salesContract.number}</Badge>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Customer Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              {t("shipping.edit.customer_info")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <FormField
              control={form.control}
              name="customer_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("shipping.edit.customer")}</FormLabel>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={t("shipping.edit.select_customer")} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {customers.map((customer) => (
                        <SelectItem key={customer.id} value={customer.id}>
                          <div>
                            <div className="font-medium">{customer.name}</div>
                            {customer.contact_name && (
                              <div className="text-sm text-muted-foreground">
                                {customer.contact_name}
                              </div>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Shipping Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Ship className="h-5 w-5" />
              {t("shipping.edit.shipping_details")}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="shipping_method"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("shipping.edit.shipping_method")}</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="sea_freight">
                          <div className="flex items-center gap-2">
                            <Ship className="h-4 w-4" />
                            {t("shipping.edit.sea_freight")}
                          </div>
                        </SelectItem>
                        <SelectItem value="air_freight">
                          <div className="flex items-center gap-2">
                            <Plane className="h-4 w-4" />
                            {t("shipping.edit.air_freight")}
                          </div>
                        </SelectItem>
                        <SelectItem value="express">
                          <div className="flex items-center gap-2">
                            <Truck className="h-4 w-4 text-green-600" />
                            {t("shipping.edit.express")}
                          </div>
                        </SelectItem>
                        <SelectItem value="truck">
                          <div className="flex items-center gap-2">
                            <Truck className="h-4 w-4" />
                            {t("shipping.edit.truck")}
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("shipping.edit.status")}</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="preparing">
                          <div className="flex items-center gap-2">
                            <Package className="h-4 w-4 text-yellow-600" />
                            {t("shipping.status.preparing")}
                          </div>
                        </SelectItem>
                        <SelectItem value="ready">
                          <div className="flex items-center gap-2">
                            <Package className="h-4 w-4 text-blue-600" />
                            {t("shipping.status.ready")}
                          </div>
                        </SelectItem>
                        <SelectItem value="shipped">
                          <div className="flex items-center gap-2">
                            <Ship className="h-4 w-4 text-purple-600" />
                            {t("shipping.status.shipped")}
                          </div>
                        </SelectItem>
                        <SelectItem value="in_transit">
                          <div className="flex items-center gap-2">
                            <Truck className="h-4 w-4 text-indigo-600" />
                            {t("shipping.status.in_transit")}
                          </div>
                        </SelectItem>
                        <SelectItem value="out_for_delivery">
                          <div className="flex items-center gap-2">
                            <Truck className="h-4 w-4 text-orange-600" />
                            {t("shipping.status.out_for_delivery")}
                          </div>
                        </SelectItem>
                        <SelectItem value="delivered">
                          <div className="flex items-center gap-2">
                            <Package className="h-4 w-4 text-green-600" />
                            {t("shipping.status.delivered")}
                          </div>
                        </SelectItem>
                        <SelectItem value="cancelled">
                          <div className="flex items-center gap-2">
                            <AlertCircle className="h-4 w-4 text-red-600" />
                            {t("shipping.status.cancelled")}
                          </div>
                        </SelectItem>
                        <SelectItem value="exception">
                          <div className="flex items-center gap-2">
                            <AlertCircle className="h-4 w-4 text-red-600" />
                            {t("shipping.status.exception")}
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="carrier"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("shipping.edit.carrier")}</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., DHL, FedEx, Maersk"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="tracking_number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("shipping.edit.tracking_number")}</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter tracking number"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="ship_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("shipping.edit.ship_date")}</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="estimated_delivery"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("shipping.edit.estimated_delivery")}</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Cost Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              {t("shipping.edit.cost_info")}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="shipping_cost"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("shipping.edit.shipping_cost")}</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="insurance_cost"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("shipping.edit.insurance_cost")}</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Notes */}
        <Card>
          <CardHeader>
            <CardTitle>{t("shipping.edit.additional_info")}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("shipping.edit.notes")}</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={t("shipping.edit.notes_placeholder")}
                      className="min-h-[80px]"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="special_instructions"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("shipping.edit.special_instructions")}</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={t("shipping.edit.special_instructions_placeholder")}
                      className="min-h-[80px]"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex items-center justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            {t("shipping.edit.cancel")}
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? t("shipping.edit.updating") : t("shipping.edit.update_shipment")}
          </Button>
        </div>
      </form>
    </Form>
  )
}
