/**
 * Manufacturing ERP - Create Shipment Page Content Component
 * Client component for localized create shipment page
 */

"use client"

import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, Ship } from "lucide-react"
import Link from "next/link"
import { useI18n } from "@/components/i18n-provider"
import { CreateShipmentForm } from "@/components/shipping/create-shipment-form"

interface CreateShipmentPageContentProps {
  customers: any[]
  contracts: any[]
  products: any[]
  defaultMethod?: string
  selectedContract?: any
}

export function CreateShipmentPageContent({ 
  customers, 
  contracts, 
  products, 
  defaultMethod, 
  selectedContract 
}: CreateShipmentPageContentProps) {
  const { t } = useI18n()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/shipping">
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t("shipping.create.back_to_shipping")}
          </Link>
        </Button>

        <div className="flex items-center gap-2">
          <Ship className="h-6 w-6 text-blue-600" />
          <div>
            <h1 className="text-2xl font-bold tracking-tight">{t("shipping.create.title")}</h1>
            <p className="text-muted-foreground">
              {t("shipping.create.subtitle")}
            </p>
          </div>
        </div>
      </div>

      {/* Create Shipment Form */}
      <div className="max-w-4xl">
        <CreateShipmentForm
          customers={customers}
          contracts={contracts}
          products={products}
          defaultMethod={defaultMethod}
          selectedContract={selectedContract}
        />
      </div>
    </div>
  )
}
