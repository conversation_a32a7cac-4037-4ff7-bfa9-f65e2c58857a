/**
 * Manufacturing ERP - Shipping Page Content Component
 * Client component for localized shipping page
 */

"use client"

import { Suspense } from "react"
import { Button } from "@/components/ui/button"
import { Plus, Ship, Package, Truck, Plane } from "lucide-react"
import Link from "next/link"
import { useI18n } from "@/components/i18n-provider"
import { ShipmentsTable } from "@/components/shipping/shipments-table"
import { ShippingStats } from "@/components/shipping/shipping-stats"
import { ShippingFilters } from "@/components/shipping/shipping-filters"

interface ShippingPageContentProps {
  shipments: any[]
  stats: any
}

export function ShippingPageContent({ shipments, stats }: ShippingPageContentProps) {
  const { t } = useI18n()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Ship className="h-8 w-8 text-blue-600" />
            {t("shipping.title")}
          </h1>
          <p className="text-muted-foreground">
            {t("shipping.subtitle")}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button asChild>
            <Link href="/shipping/create">
              <Plus className="mr-2 h-4 w-4" />
              {t("shipping.new_shipment")}
            </Link>
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <ShippingFilters />

      {/* Stats Cards */}
      <Suspense fallback={<div>Loading stats...</div>}>
        <ShippingStats stats={stats} />
      </Suspense>

      {/* Shipments Table */}
      <div className="rounded-md border">
        <Suspense fallback={<div>Loading shipments...</div>}>
          <ShipmentsTable shipments={shipments} />
        </Suspense>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="p-4 border rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Plane className="h-5 w-5 text-blue-600" />
            <h3 className="font-semibold">{t("shipping.quick_actions.air_freight")}</h3>
          </div>
          <p className="text-sm text-muted-foreground mb-3">
            {t("shipping.quick_actions.air_freight_desc")}
          </p>
          <Button variant="outline" size="sm" asChild>
            <Link href="/shipping/create?method=air_freight">
              {t("shipping.quick_actions.create_air")}
            </Link>
          </Button>
        </div>

        <div className="p-4 border rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Ship className="h-5 w-5 text-blue-600" />
            <h3 className="font-semibold">{t("shipping.quick_actions.sea_freight")}</h3>
          </div>
          <p className="text-sm text-muted-foreground mb-3">
            {t("shipping.quick_actions.sea_freight_desc")}
          </p>
          <Button variant="outline" size="sm" asChild>
            <Link href="/shipping/create?method=sea_freight">
              {t("shipping.quick_actions.create_sea")}
            </Link>
          </Button>
        </div>

        <div className="p-4 border rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Truck className="h-5 w-5 text-blue-600" />
            <h3 className="font-semibold">{t("shipping.quick_actions.express_delivery")}</h3>
          </div>
          <p className="text-sm text-muted-foreground mb-3">
            {t("shipping.quick_actions.express_delivery_desc")}
          </p>
          <Button variant="outline" size="sm" asChild>
            <Link href="/shipping/create?method=express">
              {t("shipping.quick_actions.create_express")}
            </Link>
          </Button>
        </div>
      </div>
    </div>
  )
}
