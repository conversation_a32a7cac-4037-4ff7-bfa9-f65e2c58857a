"use client"

/**
 * Manufacturing ERP - Inventory Analytics Dashboard Component
 * 
 * Professional inventory analytics dashboard with valuation methods, turnover analysis,
 * and advanced inventory metrics. Enhances existing inventory management system.
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - Phase 2 Advanced Inventory Management
 */

import { useState, useEffect, useMemo } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useI18n } from "@/components/i18n-provider"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { getLocationForUI } from "@/lib/location-config"
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Area,
  AreaChart
} from "recharts"
import {
  TrendingUp,
  TrendingDown,
  Package,
  DollarSign,
  RotateCcw,
  AlertTriangle,
  CheckCircle,
  Clock,
  RefreshCw,
  BarChart3,
  PieChart as PieChartIcon,
  Activity,
  Warehouse,
  ArrowUp,
  ArrowDown
} from "lucide-react"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface InventoryMetrics {
  totalValue: number
  totalItems: number
  totalProducts: number
  averageTurnover: number
  slowMovingItems: number
  stockoutRisk: number
  excessInventory: number
  inventoryAccuracy: number
}

interface InventoryValuation {
  method: 'fifo' | 'lifo' | 'weighted_average' | 'standard_cost'
  totalValue: number
  breakdown: {
    rawMaterials: number
    workInProgress: number
    finishedGoods: number
  }
  byLocation: Record<string, number>
}

interface TurnoverAnalysis {
  productId: string
  productName: string
  sku: string
  currentStock: number
  averageMonthlyUsage: number
  turnoverRatio: number
  daysOnHand: number
  classification: 'fast' | 'medium' | 'slow' | 'dead'
  reorderPoint: number
  suggestedAction: string
}

interface LocationAnalysis {
  location: string
  totalValue: number
  totalItems: number
  utilizationRate: number
  topProducts: Array<{
    productName: string
    value: number
    percentage: number
  }>
}

interface InventoryAnalyticsDashboardProps {
  companyId: string
  timeRange?: "30d" | "90d" | "6m" | "1y"
  refreshInterval?: number
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function InventoryAnalyticsDashboard({
  companyId,
  timeRange = "90d",
  refreshInterval = 300000 // 5 minutes
}: InventoryAnalyticsDashboardProps) {
  const { t } = useI18n()
  const { toast } = useSafeToast()

  // ✅ PROFESSIONAL STATE MANAGEMENT - Use costing service
  const [loading, setLoading] = useState(true)
  const [inventoryData, setInventoryData] = useState<any[]>([])
  const [transactionData, setTransactionData] = useState<any[]>([])
  const [costingData, setCostingData] = useState<any[]>([])
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())

  // ✅ FETCH REAL INVENTORY DATA WITH PROFESSIONAL COSTING
  const fetchAnalyticsData = async () => {
    try {
      setLoading(true)

      const [stockResponse, transactionsResponse] = await Promise.all([
        fetch("/api/inventory/lots").then(res => res.json()),
        fetch("/api/inventory/transactions?limit=50").then(res => res.json())
      ])

      const stockData = Array.isArray(stockResponse) ? stockResponse : (stockResponse?.data || [])
      const transactionsData = Array.isArray(transactionsResponse) ? transactionsResponse : (transactionsResponse?.data || [])

      console.log('📦 Real inventory data:', stockData.length, 'lots')
      console.log('📊 Real transaction data:', transactionsData.length, 'transactions')

      // ✅ PROFESSIONAL COSTING: Use API endpoint instead of direct service
      try {
        const costingResponse = await fetch("/api/inventory/costing", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ inventoryLots: stockData })
        })

        if (costingResponse.ok) {
          const costingResults = await costingResponse.json()
          console.log('💰 Professional costing results:', costingResults.length, 'calculated')
          setCostingData(costingResults)
        } else {
          console.error('❌ Costing API failed:', costingResponse.status)
          setCostingData([])
        }
      } catch (error) {
        console.error('❌ Costing service error:', error)
        setCostingData([])
      }

      setInventoryData(stockData)
      setTransactionData(transactionsData)
      setLastUpdated(new Date())
    } catch (error) {
      console.error("Error fetching inventory data:", error)
      toast({
        title: t('common.error'),
        description: t('inventory.analytics.failed_to_load'),
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  // ✅ EFFECTS
  useEffect(() => {
    fetchAnalyticsData()
  }, [])

  useEffect(() => {
    const interval = setInterval(fetchAnalyticsData, refreshInterval)
    return () => clearInterval(interval)
  }, [refreshInterval])

  // ✅ COMPUTED VALUES FROM REAL DATA
  const chartColors = {
    primary: "#3b82f6",
    success: "#10b981",
    warning: "#f59e0b",
    danger: "#ef4444",
    secondary: "#6b7280",
    purple: "#8b5cf6"
  }

  // ✅ CALCULATE PROFESSIONAL METRICS FROM COSTING DATA
  const calculatedMetrics = useMemo(() => {
    if (!inventoryData.length) return null

    console.log('🔍 Calculating metrics from inventory data:', inventoryData.length, 'lots')
    console.log('💰 Using costing data:', costingData.length, 'costed items')

    const totalItems = inventoryData.reduce((sum, lot) => sum + parseFloat(lot.qty || '0'), 0)
    const totalProducts = new Set(inventoryData.map(lot => lot.product_id)).size

    // ✅ PROFESSIONAL: Use costing data if available, otherwise fallback
    let totalValue = 0
    if (costingData.length > 0) {
      totalValue = costingData.reduce((sum, item) => sum + (item.totalValue || 0), 0)
      console.log('💰 Using professional costing data for total value:', totalValue)
    } else {
      // Fallback to simple calculation
      totalValue = inventoryData.reduce((sum, lot) => {
        const qty = parseFloat(lot.qty || '0')
        const price = parseFloat(lot.product?.price || '20')
        return sum + (qty * price)
      }, 0)
      console.log('📦 Using fallback pricing for total value:', totalValue)
    }

    const lowStockItems = inventoryData.filter(lot => parseFloat(lot.qty || '0') < 10).length
    const pendingInspections = inventoryData.filter(lot => lot.quality_status === 'pending').length

    console.log('📊 Calculated metrics:', {
      totalValue,
      totalItems,
      totalProducts,
      lowStockItems,
      pendingInspections
    })

    return {
      totalValue: Math.round(totalValue * 100) / 100,
      totalItems: Math.round(totalItems),
      totalProducts,
      lowStockItems,
      pendingInspections,
      recentTransactions: transactionData.length,
      averageUnitCost: totalItems > 0 ? Math.round((totalValue / totalItems) * 100) / 100 : 0,
      usingProfessionalCosting: costingData.length > 0
    }
  }, [inventoryData, transactionData, costingData])

  // ✅ CALCULATE LOCATION BREAKDOWN FROM PROFESSIONAL COSTING DATA
  const locationBreakdown = useMemo(() => {
    if (!inventoryData.length) return []

    console.log('🏢 Calculating location breakdown from:', inventoryData.length, 'lots')
    console.log('💰 Using costing data:', costingData.length, 'costed items')

    const locationStats = inventoryData.reduce((acc, lot, index) => {
      const rawLocation = lot.location || 'Unknown'
      const locationConfig = getLocationForUI(rawLocation)
      const location = locationConfig ? `${locationConfig.icon} ${locationConfig.displayName}` : rawLocation
      const qty = parseFloat(lot.qty || '0')

      // ✅ PROFESSIONAL: Use costing data if available
      let value = 0
      if (costingData[index]) {
        value = costingData[index].totalValue || 0
        console.log(`📍 ${location}: ${lot.product?.name} (${lot.product?.sku}) - Professional cost: $${value}`)
      } else {
        // Fallback calculation
        const price = parseFloat(lot.product?.price || '20')
        value = qty * price
        console.log(`📍 ${location}: ${lot.product?.name} (${lot.product?.sku}) - Fallback: ${qty} × $${price} = $${value}`)
      }

      if (!acc[location]) {
        acc[location] = { location, totalValue: 0, totalItems: 0, products: new Set() }
      }

      acc[location].totalValue += value
      acc[location].totalItems += qty
      acc[location].products.add(lot.product_id)

      return acc
    }, {} as Record<string, any>)

    const result = Object.values(locationStats).map((stats: any) => ({
      ...stats,
      totalProducts: stats.products.size,
      totalValue: Math.round(stats.totalValue * 100) / 100,
      totalItems: Math.round(stats.totalItems),
      averageUnitCost: stats.totalItems > 0 ?
        Math.round((stats.totalValue / stats.totalItems) * 100) / 100 : 0
    }))

    console.log('🏢 Location breakdown result:', result)
    return result
  }, [inventoryData, costingData])

  // ✅ CALCULATE CATEGORY BREAKDOWN FROM REAL DATA
  const categoryBreakdown = useMemo(() => {
    if (!inventoryData.length) return { rawMaterials: 0, workInProgress: 0, finishedGoods: 0 }

    console.log('📂 Calculating category breakdown from:', inventoryData.length, 'lots')

    const result = inventoryData.reduce((acc, lot) => {
      const category = lot.product?.category?.toLowerCase() || 'finished_goods'
      const qty = parseFloat(lot.qty || '0')
      const price = parseFloat(lot.product?.price || '20') // ✅ FIX: Use price with $20 fallback
      const value = qty * price

      console.log(`📂 ${lot.product?.name} (${lot.product?.sku}) [${category}]: ${qty} × $${price} = $${value}`)

      switch (category) {
        case 'raw_material':
        case 'raw_materials':
        case 'raw material':
          acc.rawMaterials += value
          break
        case 'work_in_progress':
        case 'wip':
        case 'work in progress':
          acc.workInProgress += value
          break
        case 'finished_goods':
        case 'finished goods':
        case 'finished':
        default:
          acc.finishedGoods += value
          break
      }

      return acc
    }, { rawMaterials: 0, workInProgress: 0, finishedGoods: 0 })

    console.log('📂 Category breakdown result:', result)
    return result
  }, [inventoryData])

  // ✅ RENDER LOADING STATE
  if (loading && !calculatedMetrics) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-muted rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-muted rounded w-1/2 mb-4"></div>
                  <div className="h-64 bg-muted rounded"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  // ✅ RENDER ERROR STATE OR NO DATA STATE
  if (!loading && (!calculatedMetrics || inventoryData.length === 0)) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-12 text-center">
            <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">{t('inventory.analytics.no_data_available')}</h3>
            <p className="text-muted-foreground mb-4">
              {t('inventory.analytics.no_data_description')}
            </p>
            <Button onClick={fetchAnalyticsData}>
              <RefreshCw className="h-4 w-4 mr-2" />
              {t('common.retry')}
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* ✅ HEADER WITH CONTROLS */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">{t('inventory.analytics.finished_goods_analytics')}</h2>
          <p className="text-muted-foreground">
            {t('inventory.analytics.finished_goods_metrics')}
          </p>
          <div className="flex items-center gap-2 mt-2">
            <Badge variant="outline" className="text-blue-700 border-blue-300">
              <Package className="h-3 w-3 mr-1" />
              {t('inventory.analytics.finished_goods_only')}
            </Badge>
            <span className="text-xs text-muted-foreground">
              {t('inventory.analytics.raw_materials_note')}
            </span>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={fetchAnalyticsData}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`} />
            {t('inventory.analytics.refresh_data')}
          </Button>
        </div>
      </div>

      {/* ✅ KEY METRICS CARDS */}
      {
        calculatedMetrics && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t('inventory.analytics.finished_goods_value')}</CardTitle>
                <DollarSign className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  ${calculatedMetrics.totalValue.toLocaleString()}
                </div>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="default" className="bg-blue-100 text-blue-800">
                    {calculatedMetrics.totalProducts} {t('inventory.analytics.fg_products')}
                  </Badge>
                  {calculatedMetrics.usingProfessionalCosting && (
                    <Badge variant="secondary" className="text-xs">
                      {t('inventory.analytics.professional_costing')}
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t('inventory.analytics.finished_goods_units')}</CardTitle>
                <Package className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{calculatedMetrics.totalItems.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  {t('inventory.analytics.avg_unit')}: ${calculatedMetrics.averageUnitCost}/{t('inventory.analytics.unit')} (FG)
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t('inventory.analytics.low_stock_alerts')}</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">
                  {calculatedMetrics.lowStockItems}
                </div>
                <p className="text-xs text-muted-foreground">
                  {t('inventory.analytics.items_below')}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t('inventory.analytics.pending_inspections')}</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  {calculatedMetrics.pendingInspections}
                </div>
                <p className="text-xs text-muted-foreground">
                  {t('inventory.analytics.quality_control_pending')}
                </p>
              </CardContent>
            </Card>
          </div>
        )
      }

      {/* ✅ CHARTS SECTION */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Inventory Category Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChartIcon className="h-5 w-5" />
              {t('inventory.analytics.category_breakdown')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={[
                    { name: t('inventory.analytics.raw_materials'), value: Math.round(categoryBreakdown.rawMaterials * 100) / 100 },
                    { name: t('inventory.analytics.work_in_progress'), value: Math.round(categoryBreakdown.workInProgress * 100) / 100 },
                    { name: t('inventory.analytics.finished_goods'), value: Math.round(categoryBreakdown.finishedGoods * 100) / 100 }
                  ].filter(item => item.value > 0)}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => value > 0 ? `${name}: $${value.toLocaleString()}` : ''}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  <Cell fill={chartColors.primary} />
                  <Cell fill={chartColors.warning} />
                  <Cell fill={chartColors.success} />
                </Pie>
                <Tooltip formatter={(value) => `$${Number(value).toLocaleString()}`} />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Inventory by Location */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Warehouse className="h-5 w-5" />
              {t('inventory.analytics.inventory_by_location')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {locationBreakdown && locationBreakdown.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={locationBreakdown}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="location" />
                  <YAxis />
                  <Tooltip formatter={(value) => `$${Number(value).toLocaleString()}`} />
                  <Legend />
                  <Bar
                    dataKey="totalValue"
                    fill={chartColors.success}
                    name={t('inventory.analytics.total_value')}
                  />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="h-[300px] flex items-center justify-center text-muted-foreground">
                {t('inventory.analytics.no_location_data')}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recent Transactions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              {t('inventory.analytics.recent_activity')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {transactionData && transactionData.length > 0 ? (
              <div className="space-y-4">
                <div className="text-sm text-muted-foreground">
                  {t('inventory.analytics.last_transactions', { count: transactionData.length })}
                </div>
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {transactionData.slice(0, 10).map((txn, index) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex items-center gap-2">
                        {txn.type === 'inbound' ? (
                          <ArrowUp className="h-4 w-4 text-green-600" />
                        ) : (
                          <ArrowDown className="h-4 w-4 text-red-600" />
                        )}
                        <span className="text-sm font-medium">
                          {txn.product?.name || t('inventory.analytics.unknown_product')}
                        </span>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">
                          {txn.type === 'inbound' ? '+' : '-'}{txn.qty}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {(() => {
                            const rawLoc = txn.to_location || txn.from_location || 'Unknown'
                            const loc = getLocationForUI(rawLoc)
                            return loc ? `${loc.icon} ${loc.displayName}` : rawLoc
                          })()}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="h-[300px] flex items-center justify-center text-muted-foreground">
                {t('inventory.analytics.no_recent_transactions')}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quality Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              {t('inventory.analytics.quality_status_distribution')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={Object.entries(
                    inventoryData.reduce((acc, lot) => {
                      const status = lot.quality_status || 'pending'
                      acc[status] = (acc[status] || 0) + 1
                      return acc
                    }, {} as Record<string, number>)
                  ).map(([status, count]) => ({
                    name: status.charAt(0).toUpperCase() + status.slice(1),
                    value: count
                  }))}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${name}: ${value}`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  <Cell fill={chartColors.success} />
                  <Cell fill={chartColors.warning} />
                  <Cell fill={chartColors.danger} />
                  <Cell fill={chartColors.secondary} />
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* ✅ LAST UPDATED INFO */}
      <div className="text-xs text-muted-foreground text-center">
        {t('inventory.analytics.last_updated')}: {lastUpdated.toLocaleString()}
      </div>
    </div >
  )
}
