"use client"

import React, { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useI18n } from "@/components/i18n-provider"
import {
  Package,
  Package2,
  DollarSign,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Warehouse,
  Factory,
  RefreshCw
} from "lucide-react"

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface FinishedGoodsKPIs {
  totalUnits: number
  totalValue: number
  totalProducts: number
  lowStockAlerts: number
  pendingInspections: number
  locations: string[]
}

interface RawMaterialsKPIs {
  totalUnits: number
  totalValue: number
  totalMaterials: number
  expiringLots: number
  availableLots: number
  locations: string[]
}

interface CombinedKPIs {
  totalInventoryValue: number
  totalLocations: number
  recentTransactions: number
  qualityIssues: number
}

interface EnhancedInventoryKPIsProps {
  refreshInterval?: number
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function EnhancedInventoryKPIs({
  refreshInterval = 30000
}: EnhancedInventoryKPIsProps) {
  const { t } = useI18n()

  // ✅ STATE MANAGEMENT
  const [finishedGoods, setFinishedGoods] = useState<FinishedGoodsKPIs>({
    totalUnits: 0,
    totalValue: 0,
    totalProducts: 0,
    lowStockAlerts: 0,
    pendingInspections: 0,
    locations: []
  })

  const [rawMaterials, setRawMaterials] = useState<RawMaterialsKPIs>({
    totalUnits: 0,
    totalValue: 0,
    totalMaterials: 0,
    expiringLots: 0,
    availableLots: 0,
    locations: []
  })

  const [combined, setCombined] = useState<CombinedKPIs>({
    totalInventoryValue: 0,
    totalLocations: 0,
    recentTransactions: 0,
    qualityIssues: 0
  })

  const [loading, setLoading] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())

  // ✅ DATA FETCHING FUNCTIONS - DIRECT RETURN VERSIONS
  const fetchFinishedGoodsDataDirect = async (): Promise<FinishedGoodsKPIs> => {
    try {
      const response = await fetch('/api/inventory/lots')
      if (!response.ok) throw new Error('Failed to fetch finished goods')

      const stockData = await response.json()
      const data = Array.isArray(stockData) ? stockData : (stockData?.data || [])

      // Calculate finished goods KPIs
      const totalUnits = data.reduce((sum: number, lot: any) => sum + parseFloat(lot.qty || '0'), 0)
      const totalProducts = new Set(data.map((lot: any) => lot.product_id)).size
      const lowStockAlerts = data.filter((lot: any) => parseFloat(lot.qty || '0') < 10).length
      const pendingInspections = data.filter((lot: any) => lot.quality_status === 'pending').length

      // Calculate total value (using product price or fallback)
      const totalValue = data.reduce((sum: number, lot: any) => {
        const qty = parseFloat(lot.qty || '0')
        const price = parseFloat(lot.product?.price || '20') // $20 fallback
        return sum + (qty * price)
      }, 0)

      // Get unique locations
      const locations = [...new Set(data.map((lot: any) => lot.location).filter(Boolean))]

      return {
        totalUnits: Math.round(totalUnits),
        totalValue: Math.round(totalValue),
        totalProducts,
        lowStockAlerts,
        pendingInspections,
        locations
      }

    } catch (error) {
      console.error('Error fetching finished goods data:', error)
      return {
        totalUnits: 0,
        totalValue: 0,
        totalProducts: 0,
        lowStockAlerts: 0,
        pendingInspections: 0,
        locations: []
      }
    }
  }

  const fetchRawMaterialsDataDirect = async (): Promise<RawMaterialsKPIs> => {
    try {
      // ✅ FIXED: Fetch raw materials with correct data structure
      const materialsResponse = await fetch('/api/raw-materials')
      if (!materialsResponse.ok) throw new Error('Failed to fetch raw materials')

      const materialsData = await materialsResponse.json()

      // ✅ FIXED: Handle the correct API response structure { materials: [...], pagination: {...} }
      const materials = materialsData?.materials || []

      let totalUnits = 0
      let totalValue = 0
      let availableLots = 0
      let expiringLots = 0
      const allLocations = new Set<string>()

      // ✅ IMPROVED: Process all materials with their included lots data
      materials.forEach((material: any) => {
        // Each material already includes lots from the API response
        const lots = material.lots || []

        lots.forEach((lot: any) => {
          if (lot.status === 'available') {
            const qty = parseFloat(lot.qty || '0')
            const cost = parseFloat(lot.total_cost || '0')

            totalUnits += qty
            totalValue += cost
            availableLots++

            if (lot.location) allLocations.add(lot.location)

            // Check for expiring lots (within 30 days)
            if (lot.expiry_date) {
              const expiryDate = new Date(lot.expiry_date)
              const thirtyDaysFromNow = new Date()
              thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30)

              if (expiryDate <= thirtyDaysFromNow) {
                expiringLots++
              }
            }
          }
        })
      })

      return {
        totalUnits: Math.round(totalUnits * 100) / 100, // Round to 2 decimal places
        totalValue: Math.round(totalValue),
        totalMaterials: materials.length,
        expiringLots,
        availableLots,
        locations: Array.from(allLocations)
      }

    } catch (error) {
      console.error('Error fetching raw materials data:', error)
      return {
        totalUnits: 0,
        totalValue: 0,
        totalMaterials: 0,
        expiringLots: 0,
        availableLots: 0,
        locations: []
      }
    }
  }

  // ✅ LEGACY FUNCTIONS - Keep for compatibility but use direct versions
  const fetchFinishedGoodsData = async () => {
    try {
      const response = await fetch('/api/inventory/lots')
      if (!response.ok) throw new Error('Failed to fetch finished goods')

      const stockData = await response.json()
      const data = Array.isArray(stockData) ? stockData : (stockData?.data || [])

      // Calculate finished goods KPIs
      const totalUnits = data.reduce((sum: number, lot: any) => sum + parseFloat(lot.qty || '0'), 0)
      const totalProducts = new Set(data.map((lot: any) => lot.product_id)).size
      const lowStockAlerts = data.filter((lot: any) => parseFloat(lot.qty || '0') < 10).length
      const pendingInspections = data.filter((lot: any) => lot.quality_status === 'pending').length

      // Calculate total value (using product price or fallback)
      const totalValue = data.reduce((sum: number, lot: any) => {
        const qty = parseFloat(lot.qty || '0')
        const price = parseFloat(lot.product?.price || '20') // $20 fallback
        return sum + (qty * price)
      }, 0)

      // Get unique locations
      const locations = [...new Set(data.map((lot: any) => lot.location).filter(Boolean))]

      setFinishedGoods({
        totalUnits: Math.round(totalUnits),
        totalValue: Math.round(totalValue),
        totalProducts,
        lowStockAlerts,
        pendingInspections,
        locations
      })

    } catch (error) {
      console.error('Error fetching finished goods data:', error)
    }
  }

  const fetchRawMaterialsData = async () => {
    try {
      console.log('🔍 Enhanced KPIs: Fetching raw materials data...')

      // ✅ FIXED: Fetch raw materials with correct data structure
      const materialsResponse = await fetch('/api/raw-materials')
      if (!materialsResponse.ok) throw new Error('Failed to fetch raw materials')

      const materialsData = await materialsResponse.json()
      console.log('📊 Enhanced KPIs: Raw materials API response:', materialsData)

      // ✅ FIXED: Handle the correct API response structure { materials: [...], pagination: {...} }
      const materials = materialsData?.materials || []
      console.log('📦 Enhanced KPIs: Materials array:', materials.length, 'materials')

      let totalUnits = 0
      let totalValue = 0
      let availableLots = 0
      let expiringLots = 0
      const allLocations = new Set<string>()

      // ✅ IMPROVED: Process all materials with their included lots data
      materials.forEach((material: any, index: number) => {
        console.log(`📋 Enhanced KPIs: Processing material ${index + 1}: ${material.name}`)

        // Each material already includes lots from the API response
        const lots = material.lots || []
        console.log(`📦 Enhanced KPIs: Material has ${lots.length} lots`)

        lots.forEach((lot: any, lotIndex: number) => {
          console.log(`🏷️ Enhanced KPIs: Processing lot ${lotIndex + 1}: ${lot.lot_number}, Status: ${lot.status}`)

          if (lot.status === 'available') {
            const qty = parseFloat(lot.qty || '0')
            const cost = parseFloat(lot.total_cost || '0')

            console.log(`💰 Enhanced KPIs: Adding qty=${qty}, cost=${cost}`)

            totalUnits += qty
            totalValue += cost
            availableLots++

            if (lot.location) allLocations.add(lot.location)

            // Check for expiring lots (within 30 days)
            if (lot.expiry_date) {
              const expiryDate = new Date(lot.expiry_date)
              const thirtyDaysFromNow = new Date()
              thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30)

              if (expiryDate <= thirtyDaysFromNow) {
                expiringLots++
              }
            }
          }
        })
      })

      const finalData = {
        totalUnits: Math.round(totalUnits * 100) / 100, // Round to 2 decimal places
        totalValue: Math.round(totalValue),
        totalMaterials: materials.length,
        expiringLots,
        availableLots,
        locations: Array.from(allLocations)
      }

      console.log('✅ Enhanced KPIs: Final raw materials data:', finalData)

      // ✅ FIXED: Set the correct raw materials data
      setRawMaterials(finalData)

    } catch (error) {
      console.error('❌ Enhanced KPIs: Error fetching raw materials data:', error)
      // ✅ ADDED: Set empty state on error to prevent showing zeros
      setRawMaterials({
        totalUnits: 0,
        totalValue: 0,
        totalMaterials: 0,
        expiringLots: 0,
        availableLots: 0,
        locations: []
      })
    }
  }

  const fetchCombinedData = async () => {
    try {
      // Fetch recent transactions
      const transactionsResponse = await fetch('/api/inventory/transactions?limit=50')
      let recentTransactions = 0

      if (transactionsResponse.ok) {
        const transactionsData = await transactionsResponse.json()
        const transactions = Array.isArray(transactionsData) ? transactionsData : (transactionsData?.data || [])
        recentTransactions = transactions.length
      }

      // Calculate combined metrics
      const totalInventoryValue = finishedGoods.totalValue + rawMaterials.totalValue
      const totalLocations = new Set([...finishedGoods.locations, ...rawMaterials.locations]).size
      const qualityIssues = finishedGoods.pendingInspections + rawMaterials.expiringLots

      setCombined({
        totalInventoryValue: Math.round(totalInventoryValue),
        totalLocations,
        recentTransactions,
        qualityIssues
      })

    } catch (error) {
      console.error('Error fetching combined data:', error)
    }
  }

  // ✅ LOAD ALL DATA - FIXED: Calculate combined data directly
  const loadAllData = async () => {
    setLoading(true)
    try {
      // Fetch data and calculate totals directly
      const [finishedGoodsData, rawMaterialsData] = await Promise.all([
        fetchFinishedGoodsDataDirect(),
        fetchRawMaterialsDataDirect()
      ])

      // Set individual data
      setFinishedGoods(finishedGoodsData)
      setRawMaterials(rawMaterialsData)

      // Calculate combined data directly from the fetched data
      const transactionsResponse = await fetch('/api/inventory/transactions?limit=50')
      let recentTransactions = 0

      if (transactionsResponse.ok) {
        const transactionsData = await transactionsResponse.json()
        const transactions = Array.isArray(transactionsData) ? transactionsData : (transactionsData?.data || [])
        recentTransactions = transactions.length
      }

      // Calculate combined metrics using the fresh data
      const totalInventoryValue = finishedGoodsData.totalValue + rawMaterialsData.totalValue
      const totalLocations = new Set([...finishedGoodsData.locations, ...rawMaterialsData.locations]).size
      const qualityIssues = finishedGoodsData.pendingInspections + rawMaterialsData.expiringLots

      setCombined({
        totalInventoryValue: Math.round(totalInventoryValue),
        totalLocations,
        recentTransactions,
        qualityIssues
      })

      setLastUpdated(new Date())
    } catch (error) {
      console.error('Error loading inventory data:', error)
    } finally {
      setLoading(false)
    }
  }

  // ✅ EFFECTS
  useEffect(() => {
    loadAllData()

    // Set up refresh interval
    const interval = setInterval(loadAllData, refreshInterval)
    return () => clearInterval(interval)
  }, [refreshInterval])

  // ✅ REMOVED: Problematic useEffect that was causing state sync issues
  // Combined data is now calculated directly in loadAllData()

  // ✅ RENDER LOADING STATE
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 bg-gray-200 rounded w-24"></div>
              <div className="h-4 w-4 bg-gray-200 rounded"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-32"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  // ✅ MAIN RENDER
  return (
    <div className="space-y-4">
      {/* Header with Last Updated */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">{t('inventory.overview.title')}</h2>
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <RefreshCw className="h-4 w-4" />
          {t('inventory.overview.last_updated')}: {lastUpdated.toLocaleTimeString()}
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Finished Goods Card */}
        <Card className="border-blue-200 bg-blue-50/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-700">
              {t("inventory.finishedGoods")}
            </CardTitle>
            <Package className="h-5 w-5 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-900">
              {finishedGoods.totalUnits.toLocaleString()}
            </div>
            <p className="text-xs text-blue-600 mb-2">
              {t('inventory.kpi.units_ready_to_ship')}
            </p>
            <div className="flex items-center gap-2 text-xs">
              <Badge variant="secondary" className="text-xs">
                {finishedGoods.totalProducts} {t('inventory.kpi.products')}
              </Badge>
              {finishedGoods.lowStockAlerts > 0 && (
                <Badge variant="destructive" className="text-xs">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  {finishedGoods.lowStockAlerts} {t('inventory.kpi.low_stock')}
                </Badge>
              )}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {t('inventory.kpi.value')}: ${finishedGoods.totalValue.toLocaleString()}
            </p>
          </CardContent>
        </Card>

        {/* Raw Materials Card */}
        <Card className="border-green-200 bg-green-50/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-700">
              {t("inventory.rawMaterials")}
            </CardTitle>
            <Package2 className="h-5 w-5 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-900">
              {rawMaterials.totalUnits.toLocaleString()}
            </div>
            <p className="text-xs text-green-600 mb-2">
              {t('inventory.kpi.units_available_for_production')}
            </p>
            <div className="flex items-center gap-2 text-xs">
              <Badge variant="secondary" className="text-xs">
                {rawMaterials.availableLots} {t('inventory.kpi.lots')}
              </Badge>
              {rawMaterials.expiringLots > 0 && (
                <Badge variant="outline" className="text-xs border-orange-300 text-orange-600">
                  <TrendingDown className="h-3 w-3 mr-1" />
                  {rawMaterials.expiringLots} {t('inventory.kpi.expiring')}
                </Badge>
              )}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {t('inventory.kpi.value')}: ${rawMaterials.totalValue.toLocaleString()}
            </p>
          </CardContent>
        </Card>

        {/* Combined Total Value Card */}
        <Card className="border-purple-200 bg-purple-50/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-700">
              {t("inventory.totalValue")}
            </CardTitle>
            <DollarSign className="h-5 w-5 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-900">
              ${combined.totalInventoryValue.toLocaleString()}
            </div>
            <p className="text-xs text-purple-600 mb-2">
              {t('inventory.kpi.combined_inventory_value')}
            </p>
            <div className="flex items-center gap-2 text-xs">
              <Badge variant="secondary" className="text-xs">
                <Warehouse className="h-3 w-3 mr-1" />
                {combined.totalLocations} {t('inventory.kpi.locations')}
              </Badge>
              <Badge variant="outline" className="text-xs">
                <Factory className="h-3 w-3 mr-1" />
                {combined.recentTransactions} {t('inventory.kpi.transactions')}
              </Badge>
            </div>
            {combined.qualityIssues > 0 && (
              <p className="text-xs text-orange-600 mt-1">
                <AlertTriangle className="h-3 w-3 inline mr-1" />
                {combined.qualityIssues} {t('inventory.kpi.quality_alerts')}
              </p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
