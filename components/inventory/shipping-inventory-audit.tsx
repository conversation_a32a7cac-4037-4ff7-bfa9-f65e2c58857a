"use client"

import React, { useState } from "react"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"
import {
  AlertTriangle,
  CheckCircle,
  XCircle,
  Truck,
  Package,
  RefreshCw,
  Eye,
  Settings
} from "lucide-react"

interface ShipmentAuditResult {
  shipmentNumber: string
  shipmentId: string
  status: string
  items: Array<{
    productName: string
    productSku: string
    quantity: number
    stockLotId?: string
    inventoryReduced: boolean
    currentStock: number
    expectedStock: number
    analysisNotes?: string
  }>
  totalQuantity: number
  inventoryImpact: 'correct' | 'missing' | 'partial'
  riskLevel: 'high' | 'medium' | 'low'
  recommendedAction: string
}

export function ShippingInventoryAudit() {
  const { t } = useI18n()
  const [auditResults, setAuditResults] = useState<ShipmentAuditResult[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useSafeToast()

  const runShippingAudit = async () => {
    setLoading(true)
    setError(null)

    try {
      // Fetch shipments with "shipped" status
      const shipmentsResponse = await fetch('/api/shipping/shipments')
      if (!shipmentsResponse.ok) {
        throw new Error(`Failed to fetch shipments: ${shipmentsResponse.status}`)
      }

      const shipmentsResult = await shipmentsResponse.json()
      let shipmentsData = []

      // Handle the correct API response format: { shipments: [...], pagination: {...} }
      if (shipmentsResult.shipments && Array.isArray(shipmentsResult.shipments)) {
        shipmentsData = shipmentsResult.shipments
      } else if (Array.isArray(shipmentsResult)) {
        shipmentsData = shipmentsResult
      } else if (shipmentsResult.data && Array.isArray(shipmentsResult.data)) {
        shipmentsData = shipmentsResult.data
      }

      console.log(`Found ${shipmentsData.length} total shipments`)

      // Filter for shipped shipments
      const shippedShipments = shipmentsData.filter((s: any) => s.status === 'shipped')
      console.log(`Found ${shippedShipments.length} shipped shipments out of ${shipmentsData.length} total`)

      // Also check for other statuses to help debug
      const statusCounts = shipmentsData.reduce((acc: any, s: any) => {
        acc[s.status] = (acc[s.status] || 0) + 1
        return acc
      }, {})
      console.log('Shipment status breakdown:', statusCounts)

      // Fetch current inventory
      const inventoryResponse = await fetch('/api/inventory/lots')
      if (!inventoryResponse.ok) {
        throw new Error(`Failed to fetch inventory: ${inventoryResponse.status}`)
      }

      const inventoryResult = await inventoryResponse.json()
      let inventoryData = []

      if (Array.isArray(inventoryResult)) {
        inventoryData = inventoryResult
      } else if (inventoryResult.data && Array.isArray(inventoryResult.data)) {
        inventoryData = inventoryResult.data
      } else if (inventoryResult.lots && Array.isArray(inventoryResult.lots)) {
        inventoryData = inventoryResult.lots
      }

      console.log(`Found ${inventoryData.length} inventory lots`)

      // Check if we have any shipped shipments to analyze
      if (shippedShipments.length === 0) {
        setAuditResults([])
        toast({
          title: t('inventory.discrepancy.no_shipped_orders'),
          description: `Found ${shipmentsData.length} total shipments, but none with 'shipped' status. Status breakdown: ${Object.entries(statusCounts).map(([status, count]) => `${status}: ${count}`).join(', ')}`,
          variant: "default"
        })
        return
      }

      // Analyze each shipped shipment
      const results: ShipmentAuditResult[] = []

      for (const shipment of shippedShipments) {
        const auditResult = await analyzeShipment(shipment, inventoryData)
        results.push(auditResult)
      }

      setAuditResults(results.sort((a, b) => {
        const riskOrder = { high: 3, medium: 2, low: 1 }
        return riskOrder[b.riskLevel] - riskOrder[a.riskLevel]
      }))

      const highRiskCount = results.filter(r => r.riskLevel === 'high').length
      const issuesCount = results.filter(r => r.inventoryImpact !== 'correct').length

      toast({
        title: t('inventory.discrepancy.shipping_audit_complete'),
        description: `Found ${issuesCount} inventory issues in ${results.length} shipped orders (${highRiskCount} high risk)`,
        variant: issuesCount > 0 ? "destructive" : "default"
      })

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Audit failed'
      setError(errorMessage)
      toast({
        title: t('inventory.discrepancy.audit_failed'),
        description: errorMessage,
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const analyzeShipment = async (shipment: any, inventory: any[]): Promise<ShipmentAuditResult> => {
    const items = shipment.items || []
    const analyzedItems = []
    let totalQuantity = 0
    let inventoryIssues = 0

    console.log(`Analyzing shipment ${shipment.shipment_number || shipment.id} with ${items.length} items`)

    for (const item of items) {
      const quantity = parseFloat(item.quantity || 0)
      totalQuantity += quantity

      // Find current stock for this product
      const productInventory = inventory.filter(lot => lot.product_id === item.product_id)
      const currentStock = productInventory.reduce((sum, lot) => sum + parseFloat(lot.qty || 0), 0)

      // Enhanced inventory reduction detection
      let inventoryReduced = false
      let analysisNotes = []

      // Method 1: Check if stock lot is linked and has been processed
      if (item.stock_lot_id) {
        const linkedLot = inventory.find(lot => lot.id === item.stock_lot_id)
        if (linkedLot) {
          // If the lot still exists and has quantity, check if it was reduced
          const lotQty = parseFloat(linkedLot.qty || 0)
          if (linkedLot.status === 'shipped' || linkedLot.status === 'depleted') {
            inventoryReduced = true
            analysisNotes.push('✅ Stock lot marked as shipped/depleted')
          } else if (lotQty === 0) {
            inventoryReduced = true
            analysisNotes.push('✅ Stock lot quantity reduced to zero')
          } else {
            analysisNotes.push(`⚠️ Stock lot still has ${lotQty} units`)
          }
        } else {
          analysisNotes.push('⚠️ Linked stock lot not found')
        }
      } else {
        analysisNotes.push('❌ No stock lot linked to shipment item')
      }

      // Method 2: Check if current stock seems reasonable for shipped quantity
      if (!inventoryReduced && currentStock > 0) {
        // If we have very high stock compared to shipped quantity, it might not have been reduced
        if (currentStock >= quantity * 10) {
          analysisNotes.push(`⚠️ High remaining stock (${currentStock}) vs shipped (${quantity})`)
        }
      }

      if (!inventoryReduced) {
        inventoryIssues++
      }

      analyzedItems.push({
        productName: item.product?.name || t('inventory.discrepancy.unknown_product'),
        productSku: item.product?.sku || item.product_id,
        quantity,
        stockLotId: item.stock_lot_id,
        inventoryReduced,
        currentStock,
        expectedStock: currentStock + (inventoryReduced ? 0 : quantity),
        analysisNotes: analysisNotes.join('; ')
      })
    }

    console.log(`Shipment analysis: ${inventoryIssues} issues out of ${items.length} items`)

    // Determine inventory impact
    let inventoryImpact: 'correct' | 'missing' | 'partial' = 'correct'
    let riskLevel: 'high' | 'medium' | 'low' = 'low'
    let recommendedAction = 'Inventory properly reduced'

    if (inventoryIssues === items.length) {
      inventoryImpact = 'missing'
      riskLevel = 'high'
      recommendedAction = '🚨 CRITICAL: No inventory reduction detected - manual adjustment needed'
    } else if (inventoryIssues > 0) {
      inventoryImpact = 'partial'
      riskLevel = 'medium'
      recommendedAction = '⚠️ PARTIAL: Some items may not have reduced inventory properly'
    }

    return {
      shipmentNumber: shipment.number || shipment.id,
      shipmentId: shipment.id,
      status: shipment.status,
      items: analyzedItems,
      totalQuantity,
      inventoryImpact,
      riskLevel,
      recommendedAction
    }
  }

  const getImpactBadge = (impact: string) => {
    const config = {
      correct: { variant: "default" as const, icon: CheckCircle, label: t('inventory.discrepancy.impact.correct') },
      partial: { variant: "secondary" as const, icon: AlertTriangle, label: t('inventory.discrepancy.impact.partial') },
      missing: { variant: "destructive" as const, icon: XCircle, label: t('inventory.discrepancy.impact.missing') }
    }

    const { variant, icon: Icon, label } = config[impact as keyof typeof config]

    return (
      <Badge variant={variant}>
        <Icon className="mr-1 h-3 w-3" />
        {label}
      </Badge>
    )
  }

  const getRiskBadge = (riskLevel: string) => {
    const config = {
      high: { variant: "destructive" as const, icon: XCircle },
      medium: { variant: "secondary" as const, icon: AlertTriangle },
      low: { variant: "default" as const, icon: CheckCircle }
    }

    const { variant, icon: Icon } = config[riskLevel as keyof typeof config]

    return (
      <Badge variant={variant}>
        <Icon className="mr-1 h-3 w-3" />
        {riskLevel.toUpperCase()}
      </Badge>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Truck className="h-5 w-5" />
              {t('inventory.discrepancy.shipping_audit_title')}
            </CardTitle>
            <p className="text-sm text-muted-foreground mt-1">
              {t('inventory.discrepancy.shipping_audit_subtitle')}
            </p>
          </div>
          <Button
            onClick={runShippingAudit}
            disabled={loading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            {loading ? t('inventory.discrepancy.auditing') : t('inventory.discrepancy.run_audit')}
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {auditResults.length > 0 && (
          <>
            {/* Summary */}
            <Alert className="mb-6">
              <Package className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <div className="font-medium">{t('inventory.discrepancy.audit_summary')}</div>
                  <div className="text-sm space-y-1">
                    <div>• <strong>{t('inventory.discrepancy.total_shipped_orders')}</strong> {auditResults.length}</div>
                    <div>• <strong>{t('inventory.discrepancy.inventory_issues')}</strong> {auditResults.filter(r => r.inventoryImpact !== 'correct').length}</div>
                    <div>• <strong>{t('inventory.discrepancy.high_risk_orders')}</strong> {auditResults.filter(r => r.riskLevel === 'high').length} {t('inventory.discrepancy.orders_need_attention')}</div>
                  </div>
                </div>
              </AlertDescription>
            </Alert>

            {/* Results Table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('inventory.discrepancy.table.shipment')}</TableHead>
                    <TableHead>{t('inventory.discrepancy.table.status')}</TableHead>
                    <TableHead className="text-right">{t('inventory.discrepancy.table.total_qty')}</TableHead>
                    <TableHead>{t('inventory.discrepancy.table.inventory_impact')}</TableHead>
                    <TableHead>{t('inventory.discrepancy.table.risk_level')}</TableHead>
                    <TableHead>{t('inventory.discrepancy.table.recommended_action')}</TableHead>
                    <TableHead>{t('inventory.discrepancy.table.actions')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {auditResults.map((result) => (
                    <TableRow key={result.shipmentId}>
                      <TableCell>
                        <div className="font-medium">{result.shipmentNumber}</div>
                        <div className="text-sm text-muted-foreground">
                          {result.items.length} {t('inventory.discrepancy.items')}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{result.status}</Badge>
                      </TableCell>
                      <TableCell className="text-right font-medium">
                        {result.totalQuantity.toLocaleString()}
                      </TableCell>
                      <TableCell>
                        {getImpactBadge(result.inventoryImpact)}
                      </TableCell>
                      <TableCell>
                        {getRiskBadge(result.riskLevel)}
                      </TableCell>
                      <TableCell className="max-w-xs">
                        <p className="text-sm">{result.recommendedAction}</p>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {result.riskLevel === 'high' && (
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              onClick={() => {
                                window.location.href = '/inventory'
                              }}
                            >
                              <Settings className="h-4 w-4 mr-1" />
                              {t('inventory.discrepancy.fix')}
                            </Button>
                          )}
                          <Button variant="ghost" size="sm" title={t('inventory.discrepancy.view_details')}>
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </>
        )}

        {auditResults.length === 0 && !loading && !error && (
          <div className="text-center py-8">
            <Truck className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
            <p className="text-muted-foreground">{t('inventory.discrepancy.click_run_audit')}</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
