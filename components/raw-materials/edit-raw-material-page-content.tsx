"use client"

/**
 * Manufacturing ERP - Edit Raw Material Page Content
 * Client component for localized raw material edit page
 */

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Package2 } from "lucide-react"
import Link from "next/link"
import { EditRawMaterialForm } from "@/components/raw-materials/edit-raw-material-form"
import { useI18n } from "@/components/i18n-provider"

interface EditRawMaterialPageContentProps {
  material: any
  suppliers: any[]
}

export function EditRawMaterialPageContent({
  material,
  suppliers,
}: EditRawMaterialPageContentProps) {
  const { t } = useI18n()

  return (
    <div className="space-y-6">
      {/* Professional Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href={`/raw-materials/${material.id}`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t("raw_materials.edit.back_to_material")}
          </Link>
        </Button>
        <div className="flex items-center gap-2">
          <Package2 className="h-6 w-6 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t("raw_materials.edit.title")}</h1>
            <p className="text-muted-foreground">
              {t("raw_materials.edit.update_material", {
                name: material.name,
                sku: material.sku
              })}
            </p>
          </div>
        </div>
      </div>

      {/* Edit Form */}
      <div className="max-w-2xl">
        <Card>
          <CardHeader>
            <CardTitle>{t("raw_materials.edit.material_information")}</CardTitle>
          </CardHeader>
          <CardContent>
            <EditRawMaterialForm material={material} suppliers={suppliers} />
          </CardContent>
        </Card>
      </div>

      {/* Professional Note */}
      <Card>
        <CardContent className="pt-6">
          <p className="text-sm text-muted-foreground">
            <strong>{t("raw_materials.edit.note_title")}</strong> {t("raw_materials.edit.note")}
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
