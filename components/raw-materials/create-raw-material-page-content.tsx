"use client"

/**
 * Manufacturing ERP - Create Raw Material Page Content
 * Client component for localized raw material creation page
 */

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, Package2 } from "lucide-react"
import Link from "next/link"
import { CreateRawMaterialForm } from "@/components/raw-materials/create-raw-material-form"
import { useI18n } from "@/components/i18n-provider"

interface CreateRawMaterialPageContentProps {
  suppliers: any[]
}

export function CreateRawMaterialPageContent({
  suppliers,
}: CreateRawMaterialPageContentProps) {
  const { t } = useI18n()

  return (
    <div className="space-y-6">
      {/* Professional Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/raw-materials">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t("raw_materials.create.back_to_materials")}
          </Link>
        </Button>
        <div className="flex items-center gap-2">
          <Package2 className="h-6 w-6 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t("raw_materials.create.title")}</h1>
            <p className="text-muted-foreground">
              {t("raw_materials.create.subtitle")}
            </p>
          </div>
        </div>
      </div>

      {/* Create Form */}
      <div className="max-w-2xl">
        <Card>
          <CardHeader>
            <CardTitle>{t("raw_materials.create.material_information")}</CardTitle>
          </CardHeader>
          <CardContent>
            <CreateRawMaterialForm suppliers={suppliers} />
          </CardContent>
        </Card>
      </div>

      {/* Professional Note */}
      <Card>
        <CardContent className="pt-6">
          <p className="text-sm text-muted-foreground">
            <strong>{t("raw_materials.create.note_title")}</strong> {t("raw_materials.create.note")}
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
