"use client"

/**
 * Manufacturing ERP - Edit Raw Material Form
 * Professional form component for editing existing raw materials
 */

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent } from "@/components/ui/card"
import { Save, Loader2 } from "lucide-react"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"

interface Supplier {
  id: string
  name: string
  email: string | null
}

interface RawMaterial {
  id: string
  sku: string
  name: string
  category: string
  unit: string
  primary_supplier_id: string | null
  composition: string | null
  quality_grade: string | null
  specifications: string | null
  standard_cost: string | null
  currency: string | null
  reorder_point: string | null
  max_stock_level: string | null
  lead_time_days: string | null
  inspection_required: string | null
  quality_tolerance: string | null
  quality_notes: string | null
  status: string | null
}

interface EditRawMaterialFormProps {
  material: RawMaterial
  suppliers: Supplier[]
}

export function EditRawMaterialForm({ material, suppliers }: EditRawMaterialFormProps) {
  const router = useRouter()
  const { toast } = useSafeToast()
  const { t } = useI18n()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    sku: material.sku,
    name: material.name,
    category: material.category,
    unit: material.unit,
    primary_supplier_id: material.primary_supplier_id || "",
    composition: material.composition || "",
    quality_grade: material.quality_grade || "",
    specifications: material.specifications || "",
    standard_cost: material.standard_cost || "",
    currency: material.currency || "USD",
    reorder_point: material.reorder_point || "0",
    max_stock_level: material.max_stock_level || "0",
    lead_time_days: material.lead_time_days || "7",
    inspection_required: material.inspection_required || "false",
    quality_tolerance: material.quality_tolerance || "",
    quality_notes: material.quality_notes || "",
    status: material.status || "active",
  })

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const response = await fetch(`/api/raw-materials/${material.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to update raw material")
      }

      const result = await response.json()

      toast({
        title: t("common.success"),
        description: t("raw_materials.edit.success_message"),
      })

      // Redirect back to the material detail page
      router.push(`/raw-materials/${material.id}`)
    } catch (error) {
      console.error("Update raw material error:", error)
      toast({
        title: t("common.error"),
        description: error instanceof Error ? error.message : t("raw_materials.edit.error_message"),
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="sku">{t("raw_materials.edit.sku_label")} *</Label>
          <Input
            id="sku"
            value={formData.sku}
            onChange={(e) => handleInputChange("sku", e.target.value)}
            placeholder={t("raw_materials.edit.sku_placeholder")}
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="name">{t("raw_materials.edit.name_label")} *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => handleInputChange("name", e.target.value)}
            placeholder={t("raw_materials.edit.name_placeholder")}
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="category">{t("raw_materials.edit.category_label")} *</Label>
          <Select value={formData.category} onValueChange={(value) => handleInputChange("category", value)}>
            <SelectTrigger>
              <SelectValue placeholder={t("raw_materials.edit.category_placeholder")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="yarn">{t("raw_materials.filters.yarn")}</SelectItem>
              <SelectItem value="fabric">{t("raw_materials.filters.fabric")}</SelectItem>
              <SelectItem value="dyes">{t("raw_materials.filters.dyes")}</SelectItem>
              <SelectItem value="chemicals">{t("raw_materials.filters.chemicals")}</SelectItem>
              <SelectItem value="accessories">{t("raw_materials.filters.accessories")}</SelectItem>
              <SelectItem value="other">{t("raw_materials.filters.other")}</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="unit">{t("raw_materials.edit.unit_label")} *</Label>
          <Input
            id="unit"
            value={formData.unit}
            onChange={(e) => handleInputChange("unit", e.target.value)}
            placeholder={t("raw_materials.edit.unit_placeholder")}
            required
          />
        </div>
      </div>

      {/* Supplier Information */}
      <div className="space-y-2">
        <Label htmlFor="supplier">{t("raw_materials.edit.supplier_label")}</Label>
        <Select value={formData.primary_supplier_id || undefined} onValueChange={(value) => handleInputChange("primary_supplier_id", value || "")}>
          <SelectTrigger>
            <SelectValue placeholder={t("raw_materials.edit.supplier_placeholder")} />
          </SelectTrigger>
          <SelectContent>
            {suppliers.map((supplier) => (
              <SelectItem key={supplier.id} value={supplier.id}>
                {supplier.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Material Specifications */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="composition">{t("raw_materials.edit.composition_label")}</Label>
          <Input
            id="composition"
            value={formData.composition}
            onChange={(e) => handleInputChange("composition", e.target.value)}
            placeholder={t("raw_materials.edit.composition_placeholder")}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="quality_grade">{t("raw_materials.edit.quality_grade_label")}</Label>
          <Input
            id="quality_grade"
            value={formData.quality_grade}
            onChange={(e) => handleInputChange("quality_grade", e.target.value)}
            placeholder={t("raw_materials.edit.quality_grade_placeholder")}
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="specifications">{t("raw_materials.edit.specifications_label")}</Label>
        <Textarea
          id="specifications"
          value={formData.specifications}
          onChange={(e) => handleInputChange("specifications", e.target.value)}
          placeholder={t("raw_materials.edit.specifications_placeholder")}
          rows={3}
        />
      </div>

      {/* Cost Information */}
      <Card>
        <CardContent className="pt-6">
          <h3 className="text-lg font-semibold mb-4">{t("raw_materials.edit.cost_inventory_settings")}</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="standard_cost">{t("raw_materials.edit.standard_cost_label")}</Label>
              <Input
                id="standard_cost"
                type="number"
                step="0.01"
                value={formData.standard_cost}
                onChange={(e) => handleInputChange("standard_cost", e.target.value)}
                placeholder="0.00"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="reorder_point">{t("raw_materials.edit.reorder_point_label")}</Label>
              <Input
                id="reorder_point"
                type="number"
                value={formData.reorder_point}
                onChange={(e) => handleInputChange("reorder_point", e.target.value)}
                placeholder="0"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="lead_time_days">{t("raw_materials.edit.lead_time_label")}</Label>
              <Input
                id="lead_time_days"
                type="number"
                value={formData.lead_time_days}
                onChange={(e) => handleInputChange("lead_time_days", e.target.value)}
                placeholder="7"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Status and Quality Control */}
      <Card>
        <CardContent className="pt-6">
          <h3 className="text-lg font-semibold mb-4">{t("raw_materials.edit.status_quality_control")}</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="status">{t("raw_materials.edit.status_label")}</Label>
              <Select value={formData.status} onValueChange={(value) => handleInputChange("status", value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">{t("raw_materials.filters.active")}</SelectItem>
                  <SelectItem value="inactive">{t("raw_materials.filters.inactive")}</SelectItem>
                  <SelectItem value="discontinued">{t("raw_materials.filters.discontinued")}</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="inspection_required">{t("raw_materials.edit.inspection_required_label")}</Label>
              <Select value={formData.inspection_required} onValueChange={(value) => handleInputChange("inspection_required", value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="false">{t("raw_materials.view.no")}</SelectItem>
                  <SelectItem value="true">{t("raw_materials.view.yes")}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="space-y-2 mt-4">
            <Label htmlFor="quality_notes">{t("raw_materials.edit.quality_notes_label")}</Label>
            <Textarea
              id="quality_notes"
              value={formData.quality_notes}
              onChange={(e) => handleInputChange("quality_notes", e.target.value)}
              placeholder={t("raw_materials.edit.quality_notes_placeholder")}
              rows={2}
            />
          </div>
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex items-center gap-4">
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t("raw_materials.edit.updating")}
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              {t("raw_materials.edit.update_button")}
            </>
          )}
        </Button>
        <Button type="button" variant="outline" asChild>
          <Link href={`/raw-materials/${material.id}`}>{t("raw_materials.edit.cancel")}</Link>
        </Button>
      </div>
    </form>
  )
}
