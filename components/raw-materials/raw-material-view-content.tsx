"use client"

/**
 * Manufacturing ERP - Raw Material View Content
 * Client component for localized raw material detail view
 */

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ArrowLeft, Edit, Plus, Package, TrendingUp, AlertTriangle, DollarSign } from "lucide-react"
import Link from "next/link"
import { LotActions } from "@/components/raw-materials/lot-actions"
import { useI18n } from "@/components/i18n-provider"

interface RawMaterialViewContentProps {
  material: any
  totalLots: number
  availableQty: number
  totalValue: number
}

export function RawMaterialViewContent({
  material,
  totalLots,
  availableQty,
  totalValue,
}: RawMaterialViewContentProps) {
  const { t } = useI18n()

  // Helper functions moved to client component
  const formatCurrency = (value: string | number) => {
    const num = typeof value === "string" ? parseFloat(value) : value
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(num)
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A"
    return new Date(dateString).toLocaleDateString()
  }

  const getStatusBadge = (status: string | null) => {
    const statusValue = status || "active"
    const variant = statusValue === "active" ? "default" : "secondary"
    return <Badge variant={variant}>{statusValue}</Badge>
  }

  const getCategoryBadge = (category: string) => {
    const colors = {
      yarn: "bg-blue-100 text-blue-800",
      fabric: "bg-green-100 text-green-800",
      dyes: "bg-purple-100 text-purple-800",
      chemicals: "bg-orange-100 text-orange-800",
      accessories: "bg-pink-100 text-pink-800",
      other: "bg-gray-100 text-gray-800",
    }
    const colorClass = colors[category as keyof typeof colors] || colors.other
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass}`}>
        {category}
      </span>
    )
  }

  return (
    <div className="space-y-6">
      {/* Professional Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/raw-materials">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t("raw_materials.view.back_to_materials")}
            </Link>
          </Button>
          <div>
            <div className="flex items-center gap-3">
              <h1 className="text-3xl font-bold tracking-tight">{material.name}</h1>
              {getStatusBadge(material.status)}
              {getCategoryBadge(material.category)}
            </div>
            <p className="text-muted-foreground">
              SKU: {material.sku} • Unit: {material.unit}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" asChild>
            <Link href={`/raw-materials/${material.id}/lots/create`}>
              <Plus className="mr-2 h-4 w-4" />
              {t("raw_materials.view.add_lot")}
            </Link>
          </Button>
          <Button asChild>
            <Link href={`/raw-materials/${material.id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              {t("raw_materials.view.edit")}
            </Link>
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t("raw_materials.view.total_lots")}</p>
                <p className="text-2xl font-bold">{totalLots}</p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t("raw_materials.view.available_qty")}</p>
                <p className="text-2xl font-bold">{availableQty.toFixed(2)}</p>
                <p className="text-xs text-muted-foreground">{material.unit}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t("raw_materials.view.total_value")}</p>
                <p className="text-2xl font-bold">{formatCurrency(totalValue)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t("raw_materials.view.standard_cost")}</p>
                <p className="text-2xl font-bold">
                  {material.standard_cost ? formatCurrency(material.standard_cost) : "N/A"}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Material Information */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>{t("raw_materials.view.material_information")}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t("raw_materials.view.primary_supplier")}</p>
                <p className="font-medium">
                  {material.primarySupplier?.name || t("raw_materials.view.no_supplier_assigned")}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t("raw_materials.view.composition")}</p>
                <p className="font-medium">{material.composition || t("raw_materials.view.not_specified")}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t("raw_materials.view.quality_grade")}</p>
                <p className="font-medium">{material.quality_grade || t("raw_materials.view.not_specified")}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t("raw_materials.view.lead_time")}</p>
                <p className="font-medium">{material.lead_time_days} {t("raw_materials.view.days")}</p>
              </div>
            </div>
            {material.specifications && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t("raw_materials.view.specifications")}</p>
                <p className="text-sm">{material.specifications}</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t("raw_materials.view.inventory_settings")}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">{t("raw_materials.view.reorder_point")}</p>
              <p className="font-medium">{material.reorder_point} {material.unit}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">{t("raw_materials.view.max_stock_level")}</p>
              <p className="font-medium">{material.max_stock_level} {material.unit}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">{t("raw_materials.view.inspection_required")}</p>
              <Badge variant={material.inspection_required === "true" ? "default" : "secondary"}>
                {material.inspection_required === "true" ? t("raw_materials.view.yes") : t("raw_materials.view.no")}
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs for Lots, BOM Usage, Consumption History */}
      <Tabs defaultValue="lots" className="space-y-4">
        <TabsList>
          <TabsTrigger value="lots">{t("raw_materials.view.tabs.inventory_lots")} ({totalLots})</TabsTrigger>
          <TabsTrigger value="bom">{t("raw_materials.view.tabs.bom_usage")} ({material.bomItems.length})</TabsTrigger>
          <TabsTrigger value="consumption">{t("raw_materials.view.tabs.consumption_history")}</TabsTrigger>
        </TabsList>

        <TabsContent value="lots">
          <Card>
            <CardHeader>
              <CardTitle>{t("raw_materials.view.tabs.inventory_lots")}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t("raw_materials.view.lots.lot_number")}</TableHead>
                      <TableHead>{t("raw_materials.view.lots.supplier")}</TableHead>
                      <TableHead>{t("raw_materials.view.lots.quantity")}</TableHead>
                      <TableHead>{t("raw_materials.view.lots.unit_cost")}</TableHead>
                      <TableHead>{t("raw_materials.view.lots.received_date")}</TableHead>
                      <TableHead>{t("raw_materials.view.lots.quality_status")}</TableHead>
                      <TableHead>{t("raw_materials.view.lots.status")}</TableHead>
                      <TableHead>{t("raw_materials.view.lots.actions")}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {material.lots.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                          {t("raw_materials.view.lots.no_lots_found")}
                        </TableCell>
                      </TableRow>
                    ) : (
                      material.lots.map((lot: any) => (
                        <TableRow key={lot.id}>
                          <TableCell className="font-mono text-sm">
                            {lot.lot_number || `LOT-${lot.id.slice(-8)}`}
                          </TableCell>
                          <TableCell>{lot.supplier?.name || t("raw_materials.view.lots.unknown_supplier")}</TableCell>
                          <TableCell>
                            {parseFloat(lot.qty || "0").toFixed(2)} {material.unit}
                          </TableCell>
                          <TableCell>{formatCurrency(lot.unit_cost || "0")}</TableCell>
                          <TableCell>{formatDate(lot.received_date)}</TableCell>
                          <TableCell>
                            <Badge variant={lot.quality_status === "approved" ? "default" : "secondary"}>
                              {lot.quality_status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant={lot.status === "available" ? "default" : "secondary"}>
                              {lot.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <LotActions
                              lotId={lot.id}
                              lotNumber={lot.lot_number || `LOT-${lot.id.slice(-8)}`}
                              materialId={material.id}
                              materialName={material.name}
                              quantity={lot.qty || "0"}
                              unit={material.unit}
                            />
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Other tabs content would go here */}
        <TabsContent value="bom">
          <Card>
            <CardHeader>
              <CardTitle>{t("raw_materials.view.tabs.bom_usage")}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">BOM usage content will be implemented here.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="consumption">
          <Card>
            <CardHeader>
              <CardTitle>{t("raw_materials.view.tabs.consumption_history")}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Consumption history content will be implemented here.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
