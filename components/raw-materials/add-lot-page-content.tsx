"use client"

/**
 * Manufacturing ERP - Add Inventory Lot Page Content
 * Client component for localized add inventory lot page
 */

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Package } from "lucide-react"
import Link from "next/link"
import { CreateRawMaterialLotForm } from "@/components/raw-materials/create-raw-material-lot-form"
import { useI18n } from "@/components/i18n-provider"

interface AddLotPageContentProps {
  material: any
  suppliers: any[]
}

export function AddLotPageContent({
  material,
  suppliers,
}: AddLotPageContentProps) {
  const { t } = useI18n()

  return (
    <div className="space-y-6">
      {/* Professional Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href={`/raw-materials/${material.id}`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t("raw_materials.add_lot.back_to_material")}
          </Link>
        </Button>
        <div className="flex items-center gap-2">
          <Package className="h-6 w-6 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t("raw_materials.add_lot.title")}</h1>
            <p className="text-muted-foreground">
              {t("raw_materials.add_lot.subtitle", { materialName: `${material.name} (${material.sku})` })}
            </p>
          </div>
        </div>
      </div>

      {/* Create Form */}
      <div className="max-w-2xl">
        <Card>
          <CardHeader>
            <CardTitle>{t("raw_materials.add_lot.lot_information")}</CardTitle>
          </CardHeader>
          <CardContent>
            <CreateRawMaterialLotForm material={material} suppliers={suppliers} />
          </CardContent>
        </Card>
      </div>

      {/* Professional Note */}
      <Card>
        <CardContent className="pt-6">
          <p className="text-sm text-muted-foreground">
            <strong>{t("raw_materials.create.note_title")}</strong> Inventory lots track specific batches of materials 
            with their own costs, quality status, and supplier information. This enables 
            FIFO consumption tracking and accurate cost allocation.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
