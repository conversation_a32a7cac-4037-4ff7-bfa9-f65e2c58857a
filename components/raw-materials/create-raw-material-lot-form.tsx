"use client"

/**
 * Manufacturing ERP - Create Raw Material Lot Form
 * Professional form component for adding inventory lots to raw materials
 */

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent } from "@/components/ui/card"
import { Save, Loader2 } from "lucide-react"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"

interface Supplier {
  id: string
  name: string
  email: string | null
}

interface RawMaterial {
  id: string
  sku: string
  name: string
  unit: string
}

interface Location {
  id: string
  name: string
  type: string
  location_code?: string
}

interface CreateRawMaterialLotFormProps {
  material: RawMaterial
  suppliers: Supplier[]
}

export function CreateRawMaterialLotForm({ material, suppliers }: CreateRawMaterialLotFormProps) {
  const router = useRouter()
  const { toast } = useSafeToast()
  const { t } = useI18n()
  const [isLoading, setIsLoading] = useState(false)
  const [locations, setLocations] = useState<Location[]>([])
  const [formData, setFormData] = useState({
    lot_number: "",
    supplier_id: "",
    purchase_contract_id: "",
    qty: "",
    unit_cost: "",
    currency: "USD",
    received_date: new Date().toISOString().split('T')[0], // Today's date
    expiry_date: "",
    quality_status: "pending",
    status: "available",
    location: "", // Will be set when locations are loaded
    batch_notes: "",
    inspection_notes: "",
  })

  // ✅ Fetch locations on component mount
  useEffect(() => {
    const fetchLocations = async () => {
      try {
        const response = await fetch('/api/locations')
        if (response.ok) {
          const data = await response.json()
          setLocations(data.locations || [])

          // Set default location to Raw Materials Storage if available
          const rawMaterialsLocation = data.locations?.find((loc: Location) =>
            loc.type === 'raw_materials' || loc.name.toLowerCase().includes('raw materials')
          )
          if (rawMaterialsLocation && !formData.location) {
            setFormData(prev => ({ ...prev, location: rawMaterialsLocation.id }))
          }
        }
      } catch (error) {
        console.error('Failed to fetch locations:', error)
      }
    }

    fetchLocations()
  }, [])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      console.log("Submitting lot data:", formData) // Debug log
      const response = await fetch(`/api/raw-materials/${material.id}/lots`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to create lot")
      }

      const result = await response.json()

      toast({
        title: "Success",
        description: "Inventory lot created successfully",
      })

      // Redirect back to the material detail page
      router.push(`/raw-materials/${material.id}`)
    } catch (error) {
      console.error("Create lot error:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create lot",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Material Information */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">{t("raw_materials.edit.name_label")}</p>
              <p className="font-medium">{material.name}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">{t("raw_materials.edit.sku_label")}</p>
              <p className="font-medium">{material.sku}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">{t("raw_materials.edit.unit_label")}</p>
              <p className="font-medium">{material.unit}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Lot Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="lot_number">{t("raw_materials.add_lot.lot_number_label")}</Label>
          <Input
            id="lot_number"
            value={formData.lot_number}
            onChange={(e) => handleInputChange("lot_number", e.target.value)}
            placeholder="e.g., LOT-2024-001"
          />
          <p className="text-xs text-muted-foreground">
            {t("raw_materials.add_lot.lot_number_placeholder")}
          </p>
        </div>
        <div className="space-y-2">
          <Label htmlFor="supplier">{t("raw_materials.add_lot.supplier_label")}</Label>
          <Select value={formData.supplier_id || undefined} onValueChange={(value) => handleInputChange("supplier_id", value || "")}>
            <SelectTrigger>
              <SelectValue placeholder={t("raw_materials.add_lot.supplier_placeholder")} />
            </SelectTrigger>
            <SelectContent>
              {suppliers.map((supplier) => (
                <SelectItem key={supplier.id} value={supplier.id}>
                  {supplier.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Quantity and Cost */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="qty">{t("raw_materials.add_lot.quantity_label")} *</Label>
          <Input
            id="qty"
            type="number"
            step="0.01"
            value={formData.qty}
            onChange={(e) => handleInputChange("qty", e.target.value)}
            placeholder={t("raw_materials.add_lot.quantity_placeholder")}
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="unit_cost">{t("raw_materials.add_lot.unit_cost_label")} *</Label>
          <Input
            id="unit_cost"
            type="number"
            step="0.01"
            value={formData.unit_cost}
            onChange={(e) => handleInputChange("unit_cost", e.target.value)}
            placeholder={t("raw_materials.add_lot.unit_cost_placeholder")}
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="currency">{t("raw_materials.edit.currency_label")}</Label>
          <Select value={formData.currency} onValueChange={(value) => handleInputChange("currency", value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="USD">USD</SelectItem>
              <SelectItem value="EUR">EUR</SelectItem>
              <SelectItem value="CNY">CNY</SelectItem>
              <SelectItem value="GBP">GBP</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Dates */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="received_date">{t("raw_materials.add_lot.received_date_label")} *</Label>
          <Input
            id="received_date"
            type="date"
            value={formData.received_date}
            onChange={(e) => handleInputChange("received_date", e.target.value)}
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="expiry_date">{t("raw_materials.edit.expiry_date_label")}</Label>
          <Input
            id="expiry_date"
            type="date"
            value={formData.expiry_date}
            onChange={(e) => handleInputChange("expiry_date", e.target.value)}
          />
        </div>
      </div>

      {/* Status and Location */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="quality_status">{t("raw_materials.add_lot.quality_status_label")}</Label>
          <Select value={formData.quality_status} onValueChange={(value) => handleInputChange("quality_status", value)}>
            <SelectTrigger>
              <SelectValue placeholder={t("raw_materials.add_lot.quality_status_placeholder")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
              <SelectItem value="quarantined">Quarantined</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="status">{t("raw_materials.edit.availability_status_label")}</Label>
          <Select value={formData.status} onValueChange={(value) => handleInputChange("status", value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="available">Available</SelectItem>
              <SelectItem value="reserved">Reserved</SelectItem>
              <SelectItem value="consumed">Consumed</SelectItem>
              <SelectItem value="expired">Expired</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="location">{t("raw_materials.add_lot.location_label")} *</Label>
          <Select value={formData.location} onValueChange={(value) => handleInputChange("location", value)}>
            <SelectTrigger>
              <SelectValue placeholder={t("raw_materials.add_lot.location_placeholder")} />
            </SelectTrigger>
            <SelectContent>
              {locations.map((location) => (
                <SelectItem key={location.id} value={location.id}>
                  <div className="flex items-center gap-2">
                    <span>{location.name}</span>
                    {location.location_code && (
                      <span className="text-xs text-muted-foreground">({location.location_code})</span>
                    )}
                  </div>
                </SelectItem>
              ))}
              {locations.length === 0 && (
                <SelectItem value="loading" disabled>Loading locations...</SelectItem>
              )}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Notes */}
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="batch_notes">{t("raw_materials.add_lot.notes_label")}</Label>
          <Textarea
            id="batch_notes"
            value={formData.batch_notes}
            onChange={(e) => handleInputChange("batch_notes", e.target.value)}
            placeholder={t("raw_materials.add_lot.notes_placeholder")}
            rows={2}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="inspection_notes">{t("raw_materials.edit.inspection_notes_label")}</Label>
          <Textarea
            id="inspection_notes"
            value={formData.inspection_notes}
            onChange={(e) => handleInputChange("inspection_notes", e.target.value)}
            placeholder={t("raw_materials.edit.inspection_notes_placeholder")}
            rows={2}
          />
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex items-center gap-4">
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t("raw_materials.add_lot.adding")}
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              {t("raw_materials.add_lot.add_lot_button")}
            </>
          )}
        </Button>
        <Button type="button" variant="outline" asChild>
          <Link href={`/raw-materials/${material.id}`}>{t("raw_materials.add_lot.cancel")}</Link>
        </Button>
      </div>
    </form>
  )
}
