"use client"

/**
 * Manufacturing ERP - BOM Overview Client Component
 * 
 * Client-side functionality for BOM management with search, filtering, and real-time updates
 * 
 * <AUTHOR> ERP Developer
 * @version 1.0.0 - BOM Management Implementation
 */

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import { useI18n } from "@/components/i18n-provider"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { Package, Search, Plus, Edit, Eye, AlertTriangle, CheckCircle, RefreshCw, Trash2 } from "lucide-react"
import Link from "next/link"
import { useSafeToast } from "@/hooks/use-safe-toast"

interface BOMOverviewItem {
  productId: string
  productName: string
  productSku: string
  bomItemCount: number
  totalEstimatedCost: number
  hasIncompleteBOM: boolean
  lastUpdated: string | Date
  categories: string[]
  suppliers: string[]
  // ✅ NEW: Profitability analysis fields
  sellingPrice: number
  materialCost: number
  profit: number
  marginPercentage: number
  profitabilityStatus: 'excellent' | 'good' | 'fair' | 'poor'
  productCurrency: string
}

interface BOMStats {
  totalProducts: number
  productsWithBOM: number
  productsWithoutBOM: number
  incompleteBOMs: number
  totalBOMItems: number
  totalEstimatedValue: number
  averageBOMComplexity: number
}

interface BOMOverviewClientProps {
  initialData: BOMOverviewItem[]
  initialStats: BOMStats
}

export function BOMOverviewClient({ initialData, initialStats }: BOMOverviewClientProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useSafeToast()
  const { t } = useI18n()

  const [bomData, setBomData] = useState<BOMOverviewItem[]>(initialData)
  const [stats, setStats] = useState<BOMStats>(initialStats)
  const [loading, setLoading] = useState(false)
  const [search, setSearch] = useState(searchParams.get('search') || '')
  const [status, setStatus] = useState(searchParams.get('status') || 'all')
  const [deletingProductId, setDeletingProductId] = useState<string | null>(null)

  // ✅ PROFESSIONAL: Initial data fetch
  useEffect(() => {
    if (initialData.length === 0) {
      updateFilters()
    }
  }, [])

  // ✅ PROFESSIONAL: Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      updateFilters()
    }, 300)

    return () => clearTimeout(timer)
  }, [search, status])

  const updateFilters = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      if (search) params.set('search', search)
      if (status !== 'all') params.set('status', status)

      // Update URL
      router.push(`/bom?${params.toString()}`, { scroll: false })

      // Fetch updated data
      const response = await fetch(`/api/bom?${params.toString()}`)
      if (response.ok) {
        const data = await response.json()
        setBomData(data.bomOverview)
        setStats(data.statistics)
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update BOM data",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const refreshData = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      if (search) params.set('search', search)
      if (status !== 'all') params.set('status', status)

      const response = await fetch(`/api/bom?${params.toString()}`)
      if (response.ok) {
        const data = await response.json()
        setBomData(data.bomOverview)
        setStats(data.statistics)
        toast({
          title: "Success",
          description: "BOM data refreshed successfully",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to refresh BOM data",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // ✅ PROFESSIONAL: Delete product function with business logic validation
  const handleDeleteProduct = async (productId: string, productName: string) => {
    setDeletingProductId(productId)
    try {
      const response = await fetch(`/api/products/${productId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        // Remove from local state
        setBomData(prev => prev.filter(item => item.productId !== productId))
        setStats(prev => ({
          ...prev,
          totalProducts: prev.totalProducts - 1,
          productsWithoutBOM: prev.productsWithoutBOM - 1,
        }))

        toast({
          title: "Success",
          description: `Product "${productName}" deleted successfully`,
        })
      } else {
        const errorData = await response.json().catch(() => ({}))

        if (response.status === 409) {
          // Foreign key constraint error
          toast({
            title: "Cannot Delete Product",
            description: errorData.error || "Product is referenced by existing contracts, work orders, or inventory records.",
            variant: "destructive",
          })
        } else {
          toast({
            title: "Error",
            description: errorData.error || "Failed to delete product",
            variant: "destructive",
          })
        }
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete product",
        variant: "destructive",
      })
    } finally {
      setDeletingProductId(null)
    }
  }

  return (
    <div className="space-y-6">
      {/* ✅ PROFESSIONAL: Statistics cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">{t("bom.total_products")}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalProducts}</div>
            <p className="text-xs text-muted-foreground">
              {stats.averageBOMComplexity.toFixed(1)} {t("bom.avg_materials_per_product")}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">{t("bom.with_bom")}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.productsWithBOM}</div>
            <p className="text-xs text-muted-foreground">
              {stats.totalBOMItems} {t("bom.total_materials")}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">{t("bom.without_bom")}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.productsWithoutBOM}</div>
            <p className="text-xs text-muted-foreground">
              {t("bom.need_bom_configuration")}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">{t("bom.total_value")}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${stats.totalEstimatedValue.toFixed(0)}</div>
            <p className="text-xs text-muted-foreground">
              {stats.incompleteBOMs} {t("bom.incomplete_boms")}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* ✅ PROFESSIONAL: Search and filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{t("bom.overview")}</CardTitle>
              <CardDescription>
                {t("bom.view_and_manage_description")}
              </CardDescription>
            </div>
            <Button onClick={refreshData} disabled={loading} variant="outline">
              <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              {t("bom.refresh")}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder={t("bom.search_products")}
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={status} onValueChange={setStatus}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t("bom.all_products")}</SelectItem>
                <SelectItem value="complete">{t("bom.complete_boms")}</SelectItem>
                <SelectItem value="incomplete">{t("bom.incomplete_boms_filter")}</SelectItem>
                <SelectItem value="empty">{t("bom.no_bom")}</SelectItem>
              </SelectContent>
            </Select>
            <Button asChild>
              <Link href="/products">
                <Plus className="mr-2 h-4 w-4" />
                {t("bom.add_product")}
              </Link>
            </Button>
          </div>

          {/* ✅ PROFESSIONAL: BOM overview table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t("bom.product")}</TableHead>
                  <TableHead>{t("bom.bom_status")}</TableHead>
                  <TableHead>{t("bom.materials")}</TableHead>
                  <TableHead>{t("bom.categories")}</TableHead>
                  <TableHead>{t("bom.material_cost")}</TableHead>
                  <TableHead>{t("bom.selling_price")}</TableHead>
                  <TableHead>{t("bom.profit")}</TableHead>
                  <TableHead>{t("bom.margin_percent")}</TableHead>
                  <TableHead>{t("bom.last_updated")}</TableHead>
                  <TableHead>{t("bom.actions")}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={10} className="text-center py-8">
                      <RefreshCw className="h-4 w-4 animate-spin mx-auto mb-2" />
                      Loading BOM data...
                    </TableCell>
                  </TableRow>
                ) : bomData.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={10} className="text-center py-8 text-muted-foreground">
                      No products found matching your criteria
                    </TableCell>
                  </TableRow>
                ) : (
                  bomData.map((item) => (
                    <TableRow key={item.productId}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Package className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="font-medium">{item.productName}</p>
                            <p className="text-sm text-muted-foreground">{item.productSku}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {item.bomItemCount === 0 ? (
                          <Badge variant="outline" className="text-orange-600 border-orange-200">
                            <AlertTriangle className="h-3 w-3 mr-1" />
                            No BOM
                          </Badge>
                        ) : item.hasIncompleteBOM ? (
                          <Badge variant="outline" className="text-red-600 border-red-200">
                            <AlertTriangle className="h-3 w-3 mr-1" />
                            Incomplete
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="text-green-600 border-green-200">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Complete
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <span className="font-medium">{item.bomItemCount}</span> materials
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {item.categories.slice(0, 2).map((category) => (
                            <Badge key={category} variant="secondary" className="text-xs">
                              {category}
                            </Badge>
                          ))}
                          {item.categories.length > 2 && (
                            <Badge variant="secondary" className="text-xs">
                              +{item.categories.length - 2}
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      {/* ✅ NEW: Material Cost */}
                      <TableCell>
                        <span className="font-medium">
                          ${item.materialCost.toFixed(2)}
                        </span>
                      </TableCell>

                      {/* ✅ NEW: Selling Price */}
                      <TableCell>
                        {item.sellingPrice > 0 ? (
                          <span className="font-medium text-green-700">
                            ${item.sellingPrice.toFixed(2)}
                          </span>
                        ) : (
                          <span className="text-muted-foreground">Not set</span>
                        )}
                      </TableCell>

                      {/* ✅ NEW: Profit */}
                      <TableCell>
                        {item.sellingPrice > 0 ? (
                          <span className={`font-medium ${item.profit >= 0 ? 'text-green-700' : 'text-red-600'}`}>
                            ${item.profit.toFixed(2)}
                          </span>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>

                      {/* ✅ NEW: Margin Percentage */}
                      <TableCell>
                        {item.sellingPrice > 0 ? (
                          <div className="flex items-center gap-2">
                            <span className={`font-medium ${item.profitabilityStatus === 'excellent' ? 'text-green-700' :
                              item.profitabilityStatus === 'good' ? 'text-blue-600' :
                                item.profitabilityStatus === 'fair' ? 'text-yellow-600' : 'text-red-600'
                              }`}>
                              {item.marginPercentage.toFixed(1)}%
                            </span>
                            <Badge
                              variant={
                                item.profitabilityStatus === 'excellent' ? 'default' :
                                  item.profitabilityStatus === 'good' ? 'secondary' :
                                    item.profitabilityStatus === 'fair' ? 'outline' : 'destructive'
                              }
                              className="text-xs"
                            >
                              {item.profitabilityStatus}
                            </Badge>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {(() => {
                          const lastUpdated = typeof item.lastUpdated === 'string'
                            ? new Date(item.lastUpdated)
                            : item.lastUpdated
                          return lastUpdated && lastUpdated.getTime() > 0
                            ? lastUpdated.toLocaleDateString()
                            : '-'
                        })()}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/products/${item.productId}`}>
                              <Eye className="h-4 w-4" />
                            </Link>
                          </Button>
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/products/${item.productId}#bom`}>
                              <Edit className="h-4 w-4" />
                            </Link>
                          </Button>
                          {/* ✅ PROFESSIONAL: Delete button only for products with no BOM */}
                          {item.bomItemCount === 0 && (
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  disabled={deletingProductId === item.productId}
                                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                >
                                  {deletingProductId === item.productId ? (
                                    <RefreshCw className="h-4 w-4 animate-spin" />
                                  ) : (
                                    <Trash2 className="h-4 w-4" />
                                  )}
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Delete Product</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to delete "{item.productName}" ({item.productSku})?
                                    <br /><br />
                                    <strong>This action cannot be undone.</strong> The product will be permanently removed from your system.
                                    <br /><br />
                                    Note: Products referenced in contracts, work orders, or inventory cannot be deleted.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleDeleteProduct(item.productId, item.productName)}
                                    className="bg-red-600 hover:bg-red-700"
                                  >
                                    Delete Product
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
