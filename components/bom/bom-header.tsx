"use client"

import { useI18n } from "@/components/i18n-provider"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Package, Plus } from "lucide-react"
import Link from "next/link"

export function BOMHeader() {
  const { t } = useI18n()

  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
          <Package className="h-8 w-8 text-blue-600" />
          {t("bom.title")}
        </h1>
        <p className="text-muted-foreground">
          {t("bom.subtitle")}
        </p>
      </div>
      <Button asChild>
        <Link href="/products">
          <Plus className="mr-2 h-4 w-4" />
          {t("bom.manage_products")}
        </Link>
      </Button>
    </div>
  )
}
